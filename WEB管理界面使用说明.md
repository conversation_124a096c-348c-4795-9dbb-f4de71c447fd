# 量化交易系统 Web 管理界面使用说明

## 概述

本Web管理界面为量化交易系统提供了友好的中文图形化管理功能，替代了原有的命令行脚本操作方式。通过Web界面，您可以轻松地启动/停止交易程序、管理配置文件、查看系统状态等。

## 功能特性

### 🎛️ 程序控制
- **一键启动/停止**: 通过Web界面按钮控制交易程序的启动和停止
- **实时状态监控**: 显示程序运行状态、进程ID、CPU和内存使用情况
- **自动状态更新**: 使用WebSocket技术实现状态的实时更新

### ⚙️ 配置管理
- **全局配置**: 管理系统的全局参数，如数据路径、调试模式、通知设置等
- **账户配置**: 创建、编辑、删除交易账户配置
- **配置验证**: 自动验证配置的正确性
- **配置导入/导出**: 支持配置文件的备份和恢复

### 📊 系统监控
- **实时日志查看**: 在线查看系统运行日志
- **系统信息**: 显示服务器硬件信息和资源使用情况
- **活动记录**: 记录重要操作的历史记录

## 安装和启动

### 方法一：使用启动脚本（推荐）

1. **运行启动脚本**：
   ```bash
   python start_web_manager.py
   ```

2. **自动安装依赖**：
   脚本会自动检查并安装所需的Python包：
   - Flask 3.0.0
   - Flask-SocketIO 5.3.6
   - psutil 5.9.6
   - PyYAML 6.0.1
   - python-dotenv 1.0.0

3. **访问界面**：
   启动成功后，在浏览器中访问 `http://localhost:5000`

### 方法二：手动安装

1. **安装依赖**：
   ```bash
   pip install flask==3.0.0 flask-socketio==5.3.6 psutil==5.9.6 pyyaml==6.0.1 python-dotenv==1.0.0
   ```

2. **启动服务**：
   ```bash
   python web_manager.py
   ```

## 使用指南

### 1. 程序控制面板

#### 启动交易程序
1. 在主页点击"启动交易程序"按钮
2. 系统会自动启动 `startup.py` 脚本
3. 状态面板会显示程序运行信息

#### 停止交易程序
1. 点击"停止交易程序"按钮
2. 确认停止操作
3. 系统会优雅地停止交易程序

#### 状态监控
- **运行状态**: 显示程序是否正在运行
- **进程信息**: 显示进程ID和启动时间
- **资源使用**: 实时显示CPU和内存使用率
- **连接状态**: 显示Web界面与后端的连接状态

### 2. 配置管理

#### 全局配置
在配置管理页面可以修改以下全局设置：

- **实盘数据路径**: 数据中心的数据存储路径
- **错误通知URL**: 企业微信机器人通知地址
- **调试模式**: 启用后不会实际下单
- **清理启动**: 启动时是否删除缓存
- **并行任务数**: 回测时的并行处理数量
- **因子列限制**: 内存优化参数

#### 账户配置
- **添加账户**: 创建新的交易账户配置
- **编辑账户**: 修改现有账户的参数
- **删除账户**: 移除不需要的账户配置

账户配置包括：
- API密钥和密钥
- 账户类型（普通账户/统一账户）
- 小时偏移设置
- 杠杆倍数
- 企业微信通知设置
- BNB手续费抵扣设置

### 3. 日志查看

1. 点击导航栏的"日志查看"或快速操作中的"查看日志"
2. 选择要查看的日志文件
3. 系统会显示最近1000行日志内容
4. 可以点击"刷新"按钮获取最新日志

### 4. 系统监控

在控制面板可以查看：
- 系统时间
- 连接状态
- 最近活动记录
- 系统资源使用情况

## 安全注意事项

### 1. 网络安全
- 默认绑定到 `0.0.0.0:5000`，在生产环境中建议修改为内网IP
- 考虑使用反向代理（如Nginx）并配置HTTPS
- 设置防火墙规则限制访问

### 2. 配置安全
- API密钥等敏感信息会保存在配置文件中，请妥善保管
- 建议定期备份配置文件
- 不要在公共网络环境下使用

### 3. 访问控制
- 当前版本未包含用户认证，建议在受信任的网络环境中使用
- 可以考虑添加基础认证或IP白名单

## 故障排除

### 常见问题

1. **无法启动Web服务**
   - 检查端口5000是否被占用
   - 确认Python版本 >= 3.8
   - 检查依赖包是否正确安装

2. **无法启动交易程序**
   - 确认 `startup.py` 文件存在
   - 检查账户配置是否正确
   - 查看错误日志获取详细信息

3. **配置保存失败**
   - 检查文件权限
   - 确认配置格式正确
   - 查看浏览器控制台错误信息

4. **实时状态不更新**
   - 检查WebSocket连接
   - 刷新页面重新连接
   - 检查网络连接稳定性

### 日志文件位置
- Web管理界面日志: 控制台输出
- 交易程序日志: `logs/` 目录
- 错误日志: `logs/` 目录下的 `.error.log` 文件

## 技术架构

### 后端技术
- **Flask**: Web框架
- **Flask-SocketIO**: WebSocket支持
- **psutil**: 系统监控
- **subprocess**: 进程管理

### 前端技术
- **Bootstrap 5**: UI框架
- **jQuery**: JavaScript库
- **Socket.IO**: 实时通信

### 文件结构
```
├── web_manager.py          # 主Web服务器
├── start_web_manager.py    # 启动脚本
├── templates/              # HTML模板
│   ├── base.html          # 基础模板
│   ├── index.html         # 主页
│   └── config.html        # 配置页面
├── static/                # 静态资源
│   └── css/
│       └── style.css      # 样式文件
└── logs/                  # 日志目录
```

## 更新和维护

### 版本更新
1. 备份当前配置文件
2. 更新代码文件
3. 重启Web服务
4. 验证功能正常

### 定期维护
- 清理旧日志文件
- 备份重要配置
- 检查系统资源使用情况
- 更新依赖包版本

## 联系支持

如果您在使用过程中遇到问题，请：
1. 查看本文档的故障排除部分
2. 检查系统日志获取错误信息
3. 确认配置文件格式正确
4. 联系技术支持获取帮助

---

**注意**: 本Web管理界面是对原有量化交易系统的补充，不会影响原有的命令行操作方式。您仍然可以通过传统方式运行和管理系统。
