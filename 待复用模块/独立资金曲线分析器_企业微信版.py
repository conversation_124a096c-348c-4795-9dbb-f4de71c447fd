#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立资金曲线分析器 - 企业微信版
================================

这是一个专门为企业微信设计的资金曲线分析工具，可以自动分析策略表现并发送报告到企业微信群。
保留了原版本的所有指标计算逻辑，但将输出方式改为企业微信消息。

功能特性：
- 完整的策略分析指标计算
- 多策略对比功能
- 企业微信消息推送
- 定时任务支持
- 灵活的报告格式
- 异常监控和告警

使用方法：
1. 配置企业微信机器人webhook URL
2. 设置数据目录和监控策略
3. 运行脚本或设置定时任务

依赖项：
- pandas>=1.5.0
- numpy>=1.21.0
- requests>=2.25.0
- python-dateutil>=2.8.0

作者：资金曲线模块
版本：1.0.0
"""

import os
import sys
import json
import time
import logging
import argparse
import requests
import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 设置非交互式后端

# 配置中文字体 - macOS优化
# matplotlib.rcParams['font.sans-serif'] = [
#     'PingFang SC',           # macOS 系统字体
#     'Hiragino Sans GB',      # macOS 中文字体  
#     'STHeiti',               # 华文黑体
#     'Arial Unicode MS',      # Arial Unicode
#     'DejaVu Sans',           # 默认字体
#     'sans-serif'             # 系统默认
# ]
# matplotlib.rcParams['axes.unicode_minus'] = False
# matplotlib.rcParams['font.size'] = 10

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib import rcParams
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
import base64
from io import BytesIO

# 配置中文字体 - macOS优化
# plt.rcParams['font.sans-serif'] = [
#     'PingFang SC',           # macOS 系统字体
#     'Hiragino Sans GB',      # macOS 中文字体  
#     'STHeiti',               # 华文黑体
#     'Arial Unicode MS',      # Arial Unicode
#     'DejaVu Sans',           # 默认字体
#     'sans-serif'             # 系统默认
# ]

# plt.rcParams['axes.unicode_minus'] = False
# plt.rcParams['font.size'] = 10

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# =============================================================================
# 内置配置
# =============================================================================

# 默认配置字典
DEFAULT_CONFIG = {
    "wechat": {
        "webhook_url": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=1134e7a5-e25d-4d5a-bc48-98578df7d24e",
        "mentioned_list": [],
        "mentioned_mobile_list": [],
        "enable_notification": True,
        "max_message_length": 4096
    },
    "analysis": {
        "data_dir": None,
        "results_subdir": "子策略回测结果",
        "strategy_filter": [],
        "time_range_days": None,
        "min_data_points": 100,
        "enable_comparison": True,
        "max_strategies_in_report": 10,
        "enable_charts": True,
        "chart_dpi": 300
    }
}

# =============================================================================
# 配置类
# =============================================================================

@dataclass
class WeChatConfig:
    """企业微信配置"""
    webhook_url: str = ""  # 企业微信机器人webhook URL
    mentioned_list: List[str] = None  # @的用户列表
    mentioned_mobile_list: List[str] = None  # @的手机号列表
    enable_notification: bool = True  # 是否启用通知
    max_message_length: int = 4096  # 消息最大长度
    
    def __post_init__(self):
        if self.mentioned_list is None:
            self.mentioned_list = []
        if self.mentioned_mobile_list is None:
            self.mentioned_mobile_list = []
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'WeChatConfig':
        """从字典创建配置对象"""
        return cls(
            webhook_url=config_dict.get('webhook_url', ''),
            mentioned_list=config_dict.get('mentioned_list', []),
            mentioned_mobile_list=config_dict.get('mentioned_mobile_list', []),
            enable_notification=config_dict.get('enable_notification', True),
            max_message_length=config_dict.get('max_message_length', 4096)
        )
    
    @classmethod
    def from_default(cls) -> 'WeChatConfig':
        """从默认配置创建配置对象"""
        return cls.from_dict(DEFAULT_CONFIG['wechat'])

@dataclass
class AnalysisConfig:
    """分析配置类"""
    data_dir: str = None  # 数据目录
    results_subdir: str = "回测结果"  # 回测结果子目录
    strategy_filter: List[str] = None  # 策略过滤列表，None表示分析所有策略
    time_range_days: int = None  # 分析时间范围（天数），None表示全部时间
    min_data_points: int = 100  # 最少数据点数
    enable_comparison: bool = True  # 是否启用多策略对比
    max_strategies_in_report: int = 10  # 报告中最多显示的策略数量
    enable_charts: bool = True  # 是否启用图表生成
    chart_dpi: int = 300  # 图表DPI设置
    
    def __post_init__(self):
        if self.strategy_filter is None:
            self.strategy_filter = []
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'AnalysisConfig':
        """从字典创建配置对象"""
        return cls(
            data_dir=config_dict.get('data_dir'),
            results_subdir=config_dict.get('results_subdir', '回测结果'),
            strategy_filter=config_dict.get('strategy_filter', []),
            time_range_days=config_dict.get('time_range_days'),
            min_data_points=config_dict.get('min_data_points', 100),
            enable_comparison=config_dict.get('enable_comparison', True),
            max_strategies_in_report=config_dict.get('max_strategies_in_report', 10)
        )
    
    @classmethod
    def from_default(cls) -> 'AnalysisConfig':
        """从默认配置创建配置对象"""
        return cls.from_dict(DEFAULT_CONFIG['analysis'])

# =============================================================================
# 核心分析类（复用原版逻辑）
# =============================================================================

class FundCurveManager:
    """资金曲线管理器 - 核心功能实现（复用原版逻辑）"""
    
    def __init__(self, data_dir: str = None, results_subdir: str = "回测结果"):
        """初始化资金曲线管理器"""
        # 自动检测数据目录
        if data_dir is None:
            search_paths = [
                Path.cwd(),
                Path.cwd().parent,
                Path(__file__).parent,
                Path(__file__).parent.parent
            ]
            
            for search_path in search_paths:
                potential_data_dir = search_path / "data"
                if potential_data_dir.exists():
                    data_dir = str(search_path)
                    break
            
            if data_dir is None:
                data_dir = str(Path.cwd())
        
        # 判断data_dir是否已经是data目录本身
        data_path = Path(data_dir)
        if data_path.name == "data" and data_path.exists():
            # 如果传入的就是data目录，直接使用
            self.data_dir = data_path
            self.project_root = data_path.parent
        else:
            # 否则将其作为项目根目录，拼接data子目录
            self.project_root = data_path
            self.data_dir = self.project_root / "data"
        
        self.backtest_results_dir = self.data_dir / results_subdir
        
        # 确保目录存在
        self.backtest_results_dir.mkdir(parents=True, exist_ok=True)
        
        logging.info(f"数据目录: {self.data_dir}")
        logging.info(f"回测结果目录: {self.backtest_results_dir}")
    
    def find_equity_files(self) -> Dict[str, str]:
        """查找所有资金曲线文件"""
        equity_files = {}
        
        if not self.backtest_results_dir.exists():
            return equity_files
        
        # 遍历所有子目录查找资金曲线.csv文件
        for root, dirs, files in os.walk(self.backtest_results_dir):
            for file in files:
                if file == "资金曲线.csv":
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, self.backtest_results_dir)
                    equity_files[rel_path] = file_path
        
        return equity_files
    
    def calculate_annual_return(self, final_equity: float, initial_equity: float, days: int) -> float:
        """计算年化收益率"""
        if days > 0:
            return (final_equity / initial_equity) ** (365 / days) - 1
        return 0
    
    def calculate_max_drawdown(self, equity_series: pd.Series) -> float:
        """计算最大回撤"""
        cummax = equity_series.cummax()
        drawdown = (equity_series - cummax) / cummax
        return drawdown.min()
    

    
    def calculate_annual_volatility(self, daily_returns: pd.Series) -> float:
        """计算年化波动率"""
        return daily_returns.std() * (252 ** 0.5)
    
    def get_strategy_data(self, file_path: str, start_time: str = None, 
                         end_time: str = None) -> Dict[str, Any]:
        """获取指定策略的资金曲线数据"""
        try:
            if not os.path.exists(file_path):
                return {"success": False, "message": "文件不存在"}
            
            # 读取CSV文件
            df = pd.read_csv(file_path)
            
            # 检查必要的列
            if 'candle_begin_time' not in df.columns or '净值' not in df.columns:
                return {"success": False, "message": "文件格式不正确，缺少必要的列"}
            
            # 转换时间列
            df['candle_begin_time'] = pd.to_datetime(df['candle_begin_time'])
            if df['candle_begin_time'].dt.tz is not None:
                df['candle_begin_time'] = df['candle_begin_time'].dt.tz_localize(None)
            
            # 时间范围过滤
            if start_time:
                start_datetime = pd.to_datetime(start_time)
                df = df[df['candle_begin_time'] >= start_datetime]
            
            if end_time:
                end_datetime = pd.to_datetime(end_time)
                df = df[df['candle_begin_time'] <= end_datetime]
            
            if df.empty:
                return {"success": False, "message": "指定时间范围内没有数据"}
            
            # 重新计算净值，从1开始
            initial_value = df['净值'].iloc[0]
            df['重置净值'] = df['净值'] / initial_value
            
            # 计算关键指标
            final_equity = df['重置净值'].iloc[-1]
            days = (df['candle_begin_time'].iloc[-1] - df['candle_begin_time'].iloc[0]).days
            
            # 年化收益率
            annual_return = self.calculate_annual_return(final_equity, 1.0, days)
            
            # 最大回撤计算
            df['净值_cummax'] = df['重置净值'].cummax()
            df['drawdown'] = (df['重置净值'] - df['净值_cummax']) / df['净值_cummax']
            max_drawdown = df['drawdown'].min()
            current_drawdown = df['drawdown'].iloc[-1]
            
            # 找到最大回撤的开始和结束时间
            max_dd_end_idx = df['drawdown'].idxmin()
            max_dd_end_time = df.loc[max_dd_end_idx, 'candle_begin_time']
            
            # 找到最大回撤开始时间（净值达到高点的时间）
            temp_df = df.loc[:max_dd_end_idx]
            max_dd_start_idx = temp_df['重置净值'].idxmax()
            max_dd_start_time = df.loc[max_dd_start_idx, 'candle_begin_time']
            
            # 计算回撤持续天数
            max_dd_duration = (max_dd_end_time - max_dd_start_time).total_seconds() / (24 * 3600)
            
            # 收益率标准差
            df['daily_return'] = df['重置净值'].pct_change()
            annual_volatility = self.calculate_annual_volatility(df['daily_return'].dropna())
            

            
            # 计算最长不创新高时间
            df['is_new_high'] = df['重置净值'] >= df['重置净值'].cummax()
            df['high_group'] = (df['is_new_high'] != df['is_new_high'].shift()).cumsum()
            
            # 找出每个非新高组的开始和结束时间
            no_new_high_periods = []
            for group in df[~df['is_new_high']]['high_group'].unique():
                group_data = df[df['high_group'] == group]
                if not group_data['is_new_high'].any():  # 确保这是一个非新高组
                    start_time_period = group_data['candle_begin_time'].min()
                    end_time_period = group_data['candle_begin_time'].max()
                    duration = (end_time_period - start_time_period).total_seconds() / (24 * 3600)
                    no_new_high_periods.append((start_time_period, end_time_period, duration))
            
            # 找出最长不创新高时间
            if no_new_high_periods:
                longest_no_new_high_duration = max(period[2] for period in no_new_high_periods)
                longest_period = max(no_new_high_periods, key=lambda x: x[2])
                longest_no_new_high_start_time = longest_period[0].strftime('%Y-%m-%d %H:%M:%S')
                longest_no_new_high_end_time = longest_period[1].strftime('%Y-%m-%d %H:%M:%S')
            else:
                longest_no_new_high_duration = 0
                longest_no_new_high_start_time = "无"
                longest_no_new_high_end_time = "无"
            
            # 找出最新一次新高时间
            if df['is_new_high'].any():
                latest_high_idx = df[df['is_new_high']].index[-1]
                latest_high_time = df.loc[latest_high_idx, 'candle_begin_time']
                latest_high_time_str = latest_high_time.strftime('%Y-%m-%d %H:%M:%S')
                days_since_high = (df['candle_begin_time'].iloc[-1] - latest_high_time).total_seconds() / (24 * 3600)
            else:
                latest_high_time_str = "无"
                days_since_high = days
            
            # 计算距离最大回撤空间
            max_dd_pct = abs(max_drawdown) * 100
            current_dd_pct = abs(current_drawdown) * 100
            
            if current_dd_pct < 100:  # 避免除零错误
                drawdown_space = (100 - max_dd_pct) / (100 - current_dd_pct) - 1
            else:
                drawdown_space = 0
            
            # ========== 新增指标计算 ==========
            
            # 1. 历史最长不创新高时间（已有，但需要确保正确性）
            # 这部分代码已经在上面实现了
            
            # 2. 历史最大回撤时间（已有，但需要确保正确性）
            # 这部分代码已经在上面实现了
            
            # 3. 年度最大回撤分析
            yearly_drawdown_analysis = self._calculate_yearly_drawdown_analysis(df)
            
            # 计算所有回撤事件（用于找到历史最长回撤）
            all_drawdown_events = self._calculate_all_drawdown_events(df)
            
            # 找到历史最长回撤事件
            if all_drawdown_events:
                longest_drawdown_event = max(all_drawdown_events, key=lambda x: x['duration_days'])
                hist_longest_drawdown_duration = longest_drawdown_event['duration_days']
                hist_longest_drawdown_start = longest_drawdown_event['start_time']
                hist_longest_drawdown_end = longest_drawdown_event['end_time']
                hist_longest_drawdown_value = longest_drawdown_event['drawdown_pct']
            else:
                hist_longest_drawdown_duration = 0
                hist_longest_drawdown_start = "无"
                hist_longest_drawdown_end = "无"
                hist_longest_drawdown_value = 0
            
            # 计算统计指标
            statistics = {
                "总收益率": round((final_equity - 1) * 100, 2),
                "年化收益率": round(annual_return * 100, 2),
                "最大回撤": round(abs(max_drawdown) * 100, 2),
                "当前回撤": round(abs(current_drawdown) * 100, 2),
                "距离最大回撤空间": round(drawdown_space * 100, 2),
                "收益率标准差": round(annual_volatility, 3),

                "最大回撤持续天数": round(max_dd_duration, 1),
                "最长不创新高天数": round(longest_no_new_high_duration, 1),
                "距离前高天数": round(days_since_high, 1),
                "最新一次新高时间": latest_high_time_str,
                "最大回撤开始时间": max_dd_start_time.strftime('%Y-%m-%d %H:%M:%S'),
                "最大回撤结束时间": max_dd_end_time.strftime('%Y-%m-%d %H:%M:%S'),
                "最长不创新高开始时间": longest_no_new_high_start_time,
                "最长不创新高结束时间": longest_no_new_high_end_time,
                # ========== 新增指标 ==========
                "历史最长回撤持续天数": round(hist_longest_drawdown_duration, 1),
                "历史最长回撤开始时间": hist_longest_drawdown_start,
                "历史最长回撤结束时间": hist_longest_drawdown_end,
                "历史最长回撤幅度": round(hist_longest_drawdown_value, 2),
                "年度最大回撤分析": yearly_drawdown_analysis,
                "交易天数": days,
                "数据点数": len(df),
                "开始时间": df['candle_begin_time'].iloc[0].strftime('%Y-%m-%d %H:%M:%S'),
                "结束时间": df['candle_begin_time'].iloc[-1].strftime('%Y-%m-%d %H:%M:%S')
            }
            
            return {
                "success": True,
                "statistics": statistics,
                "data": df  # 添加DataFrame数据
            }
            
        except Exception as e:
            return {"success": False, "message": f"读取策略数据失败: {str(e)}"}
    
    def _calculate_yearly_drawdown_analysis(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算年度最大回撤分析"""
        try:
            # 添加年份列
            df_copy = df.copy()
            df_copy['year'] = df_copy['candle_begin_time'].dt.year
            
            yearly_analysis = {}
            
            for year in sorted(df_copy['year'].unique()):
                year_data = df_copy[df_copy['year'] == year].copy()
                
                if len(year_data) < 2:
                    continue
                
                # 重新计算该年度的净值（从年初开始重置为1）
                year_initial_value = year_data['重置净值'].iloc[0]
                year_data['年度净值'] = year_data['重置净值'] / year_initial_value
                
                # 计算该年度的回撤
                year_data['年度净值_cummax'] = year_data['年度净值'].cummax()
                year_data['年度drawdown'] = (year_data['年度净值'] - year_data['年度净值_cummax']) / year_data['年度净值_cummax']
                
                # 找到该年度最大回撤
                year_max_drawdown = year_data['年度drawdown'].min()
                year_current_drawdown = year_data['年度drawdown'].iloc[-1]
                
                # 计算该年度最长不创新高时间
                year_data['is_new_high'] = year_data['年度净值'] >= year_data['年度净值'].cummax()
                year_data['high_group'] = (year_data['is_new_high'] != year_data['is_new_high'].shift()).cumsum()
                
                # 找出该年度每个非新高组的开始和结束时间
                year_no_new_high_periods = []
                for group in year_data[~year_data['is_new_high']]['high_group'].unique():
                    group_data = year_data[year_data['high_group'] == group]
                    if not group_data['is_new_high'].any():  # 确保这是一个非新高组
                        start_time_period = group_data['candle_begin_time'].min()
                        end_time_period = group_data['candle_begin_time'].max()
                        duration = (end_time_period - start_time_period).total_seconds() / (24 * 3600)
                        year_no_new_high_periods.append((start_time_period, end_time_period, duration))
                
                # 找出该年度最长不创新高时间
                if year_no_new_high_periods:
                    year_longest_no_new_high_duration = max(period[2] for period in year_no_new_high_periods)
                    year_longest_period = max(year_no_new_high_periods, key=lambda x: x[2])
                    year_longest_no_new_high_start_time = year_longest_period[0].strftime('%Y-%m-%d %H:%M:%S')
                    year_longest_no_new_high_end_time = year_longest_period[1].strftime('%Y-%m-%d %H:%M:%S')
                else:
                    year_longest_no_new_high_duration = 0
                    year_longest_no_new_high_start_time = "无"
                    year_longest_no_new_high_end_time = "无"
                
                # 计算该年度距离前高天数
                if year_data['is_new_high'].any():
                    latest_high_idx = year_data[year_data['is_new_high']].index[-1]
                    latest_high_time = year_data.loc[latest_high_idx, 'candle_begin_time']
                    year_end_time = year_data['candle_begin_time'].iloc[-1]
                    year_days_since_high = (year_end_time - latest_high_time).total_seconds() / (24 * 3600)
                else:
                    # 如果该年度没有新高，则距离前高天数为该年度的总天数
                    year_start_time = year_data['candle_begin_time'].iloc[0]
                    year_end_time = year_data['candle_begin_time'].iloc[-1]
                    year_days_since_high = (year_end_time - year_start_time).total_seconds() / (24 * 3600)
                
                if year_max_drawdown < 0:  # 有回撤发生
                    # 找到最大回撤的结束时间
                    max_dd_end_idx = year_data['年度drawdown'].idxmin()
                    max_dd_end_time = year_data.loc[max_dd_end_idx, 'candle_begin_time']
                    
                    # 找到最大回撤开始时间（该年度净值达到高点的时间）
                    temp_year_data = year_data.loc[:max_dd_end_idx]
                    max_dd_start_idx = temp_year_data['年度净值'].idxmax()
                    max_dd_start_time = year_data.loc[max_dd_start_idx, 'candle_begin_time']
                    
                    # 计算回撤持续天数
                    max_dd_duration = (max_dd_end_time - max_dd_start_time).total_seconds() / (24 * 3600)
                    
                    # 计算年度距离最大回撤空间
                    year_max_dd_pct = abs(year_max_drawdown) * 100
                    year_current_dd_pct = abs(year_current_drawdown) * 100
                    
                    if year_current_dd_pct < 100:  # 避免除零错误
                        year_drawdown_space = ((100 - year_max_dd_pct) / (100 - year_current_dd_pct) - 1) * 100
                    else:
                        year_drawdown_space = 0
                    
                    yearly_analysis[str(year)] = {
                        "年度最大回撤": round(abs(year_max_drawdown) * 100, 2),
                        "年度当前回撤": round(abs(year_current_drawdown) * 100, 2),
                        "年度距离最大回撤空间": round(year_drawdown_space, 2),
                        "回撤开始时间": max_dd_start_time.strftime('%Y-%m-%d %H:%M:%S'),
                        "回撤结束时间": max_dd_end_time.strftime('%Y-%m-%d %H:%M:%S'),
                        "回撤持续天数": round(max_dd_duration, 1),
                        "年度收益率": round((year_data['年度净值'].iloc[-1] - 1) * 100, 2),
                        "年初净值": round(year_initial_value, 4),
                        "年末净值": round(year_data['重置净值'].iloc[-1], 4),
                        "年度最长不创新高天数": round(year_longest_no_new_high_duration, 1),
                        "年度不创新高开始时间": year_longest_no_new_high_start_time,
                        "年度不创新高结束时间": year_longest_no_new_high_end_time,
                        "年度距离前高天数": round(year_days_since_high, 1)
                    }
                else:
                    # 计算年度距离最大回撤空间（无回撤情况）
                    year_current_dd_pct = abs(year_current_drawdown) * 100
                    
                    if year_current_dd_pct < 100:  # 避免除零错误
                        year_drawdown_space = ((100 - 0) / (100 - year_current_dd_pct) - 1) * 100  # 最大回撤为0
                    else:
                        year_drawdown_space = 0
                    
                    yearly_analysis[str(year)] = {
                        "年度最大回撤": 0.0,
                        "年度当前回撤": round(abs(year_current_drawdown) * 100, 2),
                        "年度距离最大回撤空间": round(year_drawdown_space, 2),
                        "回撤开始时间": "无",
                        "回撤结束时间": "无",
                        "回撤持续天数": 0.0,
                        "年度收益率": round((year_data['年度净值'].iloc[-1] - 1) * 100, 2),
                        "年初净值": round(year_initial_value, 4),
                        "年末净值": round(year_data['重置净值'].iloc[-1], 4),
                        "年度最长不创新高天数": round(year_longest_no_new_high_duration, 1),
                        "年度不创新高开始时间": year_longest_no_new_high_start_time,
                        "年度不创新高结束时间": year_longest_no_new_high_end_time,
                        "年度距离前高天数": round(year_days_since_high, 1)
                    }
            
            return yearly_analysis
            
        except Exception as e:
            logging.warning(f"计算年度回撤分析失败: {str(e)}")
            return {}
    
    def _calculate_all_drawdown_events(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """计算所有回撤事件"""
        try:
            df_copy = df.copy()
            
            # 找到所有新高点
            df_copy['is_new_high'] = df_copy['重置净值'] >= df_copy['重置净值'].cummax()
            
            # 标记回撤区间
            df_copy['high_group'] = (df_copy['is_new_high'] != df_copy['is_new_high'].shift()).cumsum()
            
            drawdown_events = []
            
            # 遍历每个组，找出回撤事件
            for group in df_copy['high_group'].unique():
                group_data = df_copy[df_copy['high_group'] == group]
                
                # 如果这个组包含新高，跳过
                if group_data['is_new_high'].any():
                    continue
                
                # 这是一个回撤区间
                if len(group_data) > 0:
                    # 找到回撤开始时间（前一个新高点）
                    start_idx = group_data.index[0] - 1
                    if start_idx >= 0 and start_idx in df_copy.index:
                        start_time = df_copy.loc[start_idx, 'candle_begin_time']
                        start_equity = df_copy.loc[start_idx, '重置净值']
                    else:
                        continue
                    
                    # 找到回撤结束时间和最低点
                    end_time = group_data['candle_begin_time'].iloc[-1]
                    min_equity_idx = group_data['重置净值'].idxmin()
                    min_equity = group_data.loc[min_equity_idx, '重置净值']
                    min_equity_time = group_data.loc[min_equity_idx, 'candle_begin_time']
                    
                    # 计算回撤幅度和持续时间
                    drawdown_pct = abs((min_equity - start_equity) / start_equity) * 100
                    duration_days = (end_time - start_time).total_seconds() / (24 * 3600)
                    
                    drawdown_events.append({
                        "start_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
                        "end_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
                        "min_equity_time": min_equity_time.strftime('%Y-%m-%d %H:%M:%S'),
                        "drawdown_pct": drawdown_pct,
                        "duration_days": duration_days,
                        "start_equity": start_equity,
                        "min_equity": min_equity
                    })
            
            return drawdown_events
            
        except Exception as e:
            logging.warning(f"计算回撤事件失败: {str(e)}")
            return []
    
    def get_available_strategies(self) -> Dict[str, Any]:
        """获取所有可用的策略列表"""
        try:
            equity_files = self.find_equity_files()
            
            if not equity_files:
                return {
                    "success": True,
                    "strategies": [],
                    "message": "未找到任何资金曲线文件"
                }
            
            strategies = []
            for rel_path, file_path in equity_files.items():
                try:
                    # 读取文件基本信息
                    file_stat = os.stat(file_path)
                    
                    # 快速读取文件头尾获取基本信息
                    df_head = pd.read_csv(file_path, nrows=1)
                    df_tail = pd.read_csv(file_path).tail(1)
                    
                    if 'candle_begin_time' in df_head.columns and '净值' in df_head.columns:
                        start_time = pd.to_datetime(df_head['candle_begin_time'].iloc[0])
                        end_time = pd.to_datetime(df_tail['candle_begin_time'].iloc[0])
                        
                        # 计算总收益率
                        initial_value = df_head['净值'].iloc[0]
                        final_value = df_tail['净值'].iloc[0]
                        total_return = (final_value / initial_value - 1) * 100
                        
                        # 获取数据点数
                        data_points = len(pd.read_csv(file_path))
                        
                        strategy_name = os.path.dirname(rel_path) or os.path.basename(rel_path)
                        
                        strategies.append({
                            "name": strategy_name,
                            "file_path": file_path,
                            "total_return": round(total_return, 2),
                            "data_points": data_points,
                            "start_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
                            "end_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
                            "file_size": file_stat.st_size
                        })
                
                except Exception as e:
                    logging.warning(f"处理文件 {file_path} 时出错: {str(e)}")
                    continue
            
            # 按总收益率降序排序
            strategies.sort(key=lambda x: x['total_return'], reverse=True)
            
            return {
                "success": True,
                "strategies": strategies,
                "total_count": len(strategies)
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"获取策略列表失败: {str(e)}"
            }

# =============================================================================
# 图表生成器
# =============================================================================

class ChartGenerator:
    """图表生成器类"""
    
    def __init__(self, dpi: int = 300, save_dir: str = None):
        """初始化图表生成器
        
        Args:
            dpi: 图表分辨率，默认300
            save_dir: 图片保存目录，默认为当前目录下的charts文件夹
        """
        self.dpi = dpi
        self.max_image_size_mb = 1.5  # 企业微信图片大小限制（MB）
        
        # 设置图片保存目录
        if save_dir is None:
            self.save_dir = Path.cwd() / "charts"
        else:
            self.save_dir = Path(save_dir)
        
        # 创建保存目录
        self.save_dir.mkdir(parents=True, exist_ok=True)
        logging.info(f"图片保存目录: {self.save_dir}")
        
        # 中文字体已在文件顶部全局配置
        
        # 设置图表样式
        plt.style.use('seaborn-v0_8-whitegrid')
    
    def clear_saved_images(self) -> bool:
        """清空保存的图片文件
        
        Returns:
            bool: 清空是否成功
        """
        try:
            if not self.save_dir.exists():
                logging.info("图片保存目录不存在，无需清空")
                return True
            
            # 获取目录下所有PNG文件
            png_files = list(self.save_dir.glob("*.png"))
            
            if not png_files:
                logging.info("没有找到需要清空的图片文件")
                return True
            
            # 删除所有PNG文件
            deleted_count = 0
            for png_file in png_files:
                try:
                    png_file.unlink()
                    deleted_count += 1
                    logging.debug(f"已删除图片文件: {png_file}")
                except Exception as e:
                    logging.warning(f"删除图片文件失败 {png_file}: {str(e)}")
            
            logging.info(f"成功清空 {deleted_count} 个图片文件")
            return True
            
        except Exception as e:
            logging.error(f"清空图片文件时出错: {str(e)}")
            return False
    
    def _save_image_to_local(self, fig, filename: str, format='png', quality=85) -> str:
        """保存图片到本地文件
        
        Args:
            fig: matplotlib图表对象
            filename: 文件名（不含扩展名）
            format: 图片格式，'png'或'jpeg'
            quality: JPEG质量（1-100）
            
        Returns:
            保存的图片文件路径
        """
        import re
        
        # 清理文件名，移除或替换不安全的字符
        safe_filename = re.sub(r'[<>:"/\\|?*\u4e00-\u9fff]', '_', filename)  # 替换中文和特殊字符
        safe_filename = re.sub(r'_+', '_', safe_filename)  # 将多个下划线合并为一个
        safe_filename = safe_filename.strip('_')  # 移除首尾下划线
        
        # 如果清理后文件名为空，使用默认名称
        if not safe_filename or safe_filename.replace('_', '') == '':
            safe_filename = 'chart'
        
        # 确保保存目录存在
        self.save_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成带时间戳的文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_path = self.save_dir / f"{safe_filename}_{timestamp}.png"
        
        # 记录保存路径用于调试
        logging.info(f"准备保存图片到: {file_path}")
        
        try:
                        # 保存前再次确保字体设置正确 - 使用直接路径方式
            try:
                import matplotlib.font_manager as fm
                
                # 尝试多个可能的Noto Sans SC字体文件
                noto_font_paths = [
                    '/usr/share/fonts/google-noto-cjk/NotoSansSC-Regular.otf',
                    '/usr/share/fonts/google-noto-cjk/NotoSansSC-Medium.otf',
                    '/usr/share/fonts/google-noto-cjk/NotoSansSC-Bold.otf'
                ]
                
                chinese_font = None
                for font_path in noto_font_paths:
                    if os.path.exists(font_path):
                        chinese_font = fm.FontProperties(fname=font_path)
                        print(f"资金曲线图使用字体文件: {font_path}")
                        break
                
                if chinese_font:
                    # 重建字体缓存并设置字体
                    fm.fontManager.__init__()
                    plt.rcParams['font.sans-serif'] = ['Noto Sans SC']
                    plt.rcParams['font.family'] = ['sans-serif']
                else:
                    print("未找到Noto Sans SC字体文件，使用默认字体")
                    plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
                    
            except Exception as e:
                print(f"字体设置失败: {str(e)}")
                plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False
            
            # 保存为PNG格式到本地
            fig.savefig(file_path, format='png', dpi=self.dpi, bbox_inches='tight')
            
            # 检查文件大小
            file_size = file_path.stat().st_size
            size_mb = file_size / (1024 * 1024)
            
            logging.info(f"图片已保存到本地: {file_path}, 大小: {size_mb:.2f}MB")
            
            return str(file_path)
            
        except Exception as e:
            logging.error(f"保存图片失败: {str(e)}")
            return None
    
    def generate_equity_curve_chart(self, df: pd.DataFrame, strategy_name: str, 
                                   statistics: Dict[str, Any]) -> str:
        """生成资金曲线图
        
        Args:
            df: 包含净值数据的DataFrame
            strategy_name: 策略名称
            statistics: 统计数据
            
        Returns:
            base64编码的图片字符串
        """
        try:
            # 创建图表
            fig, ax = plt.subplots(figsize=(12, 8))
            
            # 绘制净值曲线
            ax.plot(df['candle_begin_time'], df['重置净值'], 
                   linewidth=2, color='#2E86AB', label='净值曲线')
            
            # 标记最大回撤区域
            max_dd_start = statistics.get('最大回撤开始时间')
            max_dd_end = statistics.get('最大回撤结束时间')
            
            if max_dd_start and max_dd_end and max_dd_start != '无':
                try:
                    start_time = pd.to_datetime(max_dd_start)
                    end_time = pd.to_datetime(max_dd_end)
                    
                    # 找到对应的净值数据
                    mask = (df['candle_begin_time'] >= start_time) & (df['candle_begin_time'] <= end_time)
                    drawdown_data = df[mask]
                    
                    if not drawdown_data.empty:
                        ax.fill_between(drawdown_data['candle_begin_time'], 
                                      drawdown_data['重置净值'], 
                                      alpha=0.3, color='red', 
                                      label=f'最大回撤区域 ({statistics.get("最大回撤", 0):.2f}%)')
                except Exception as e:
                    logging.warning(f"绘制最大回撤区域失败: {str(e)}")
            
            # 设置标题和标签
            ax.set_title(f'{strategy_name} - 资金曲线图', fontsize=16, fontweight='bold', pad=20)
            ax.set_xlabel('时间', fontsize=12)
            ax.set_ylabel('净值', fontsize=12)
            
            # 格式化x轴日期
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            ax.xaxis.set_major_locator(mdates.MonthLocator(interval=1))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
            
            # 添加网格
            ax.grid(True, alpha=0.3)
            
            # 添加图例
            ax.legend(loc='upper left')
            
            # 添加统计信息文本框
            stats_text = f"""总收益率: {statistics.get('总收益率', 0):.2f}%
年化收益率: {statistics.get('年化收益率', 0):.2f}%
最大回撤: {statistics.get('最大回撤', 0):.2f}%
夏普比率: {statistics.get('夏普比率', 0):.3f}"""
            
            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
                   verticalalignment='top', bbox=dict(boxstyle='round', 
                   facecolor='wheat', alpha=0.8), fontsize=10)
            
            # 调整布局
            plt.tight_layout()
            
            # 保存图片到本地
            image_path = self._save_image_to_local(fig, f"equity_curve_{strategy_name}")
            
            plt.close(fig)
            return image_path
            
        except Exception as e:
            logging.error(f"生成资金曲线图失败: {str(e)}")
            plt.close('all')
            return None
    
    def generate_drawdown_chart(self, df: pd.DataFrame, strategy_name: str, 
                               statistics: Dict[str, Any]) -> str:
        """生成回撤曲线图
        
        Args:
            df: 包含净值数据的DataFrame
            strategy_name: 策略名称
            statistics: 统计数据
            
        Returns:
            base64编码的图片字符串
        """
        try:
            # 计算回撤数据
            df_copy = df.copy()
            df_copy['净值_cummax'] = df_copy['重置净值'].cummax()
            df_copy['drawdown'] = (df_copy['重置净值'] - df_copy['净值_cummax']) / df_copy['净值_cummax'] * 100
            
            # 创建图表
            fig, ax = plt.subplots(figsize=(12, 6))
            
            # 绘制回撤曲线
            ax.fill_between(df_copy['candle_begin_time'], df_copy['drawdown'], 0,
                           alpha=0.7, color='red', label='回撤百分比')
            ax.plot(df_copy['candle_begin_time'], df_copy['drawdown'], 
                   linewidth=1.5, color='darkred')
            
            # 标记最大回撤点（仅用于定位，不显示在图例中）
            max_dd_idx = df_copy['drawdown'].idxmin()
            max_dd_time = df_copy.loc[max_dd_idx, 'candle_begin_time']
            max_dd_value = df_copy.loc[max_dd_idx, 'drawdown']
            
            ax.scatter([max_dd_time], [max_dd_value], color='red', s=100, 
                      zorder=5)
            
            # 设置标题和标签
            ax.set_title(f'{strategy_name} - 回撤分析图', fontsize=16, fontweight='bold', pad=20)
            ax.set_xlabel('时间', fontsize=12)
            ax.set_ylabel('回撤 (%)', fontsize=12)
            
            # 格式化x轴日期
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            ax.xaxis.set_major_locator(mdates.MonthLocator(interval=1))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
            
            # 设置y轴范围
            ax.set_ylim(min(df_copy['drawdown'].min() * 1.1, -1), 1)
            
            # 添加网格
            ax.grid(True, alpha=0.3)
            
            # 添加图例（调整位置避免与右下角文本框重叠）
            ax.legend(loc='upper right')
            
            # 添加统计信息文本框
            yearly_analysis = statistics.get('年度最大回撤分析', {})
            
            # 构建基础统计信息
            stats_text = f"""最大回撤: {statistics.get('最大回撤', 0):.2f}%
当前回撤: {statistics.get('当前回撤', 0):.2f}%
距离最大回撤空间: {statistics.get('距离最大回撤空间', 0):.2f}%"""
            
            # 添加年度距离最大回撤空间信息
            if yearly_analysis:
                stats_text += "\n\n年度距离最大回撤空间:"
                for year in sorted(yearly_analysis.keys()):
                    year_data = yearly_analysis[year]
                    year_space = year_data.get('年度距离最大回撤空间', 0)
                    stats_text += f"\n{year}年: {year_space:.2f}%"
            
            # 在图表右侧添加最大回撤点信息
            max_dd_text = f"""最大回撤点: {statistics.get('最大回撤', 0):.2f}%"""
            
            # 添加年度最大回撤点信息
            if yearly_analysis:
                max_dd_text += "\n\n年度最大回撤点:"
                for year in sorted(yearly_analysis.keys()):
                    year_data = yearly_analysis[year]
                    year_max_dd = year_data.get('年度最大回撤', 0)
                    max_dd_text += f"\n{year}年: {year_max_dd:.2f}%"
            
            # 添加左侧统计信息文本框
            ax.text(0.02, 0.02, stats_text, transform=ax.transAxes, 
                   verticalalignment='bottom', bbox=dict(boxstyle='round', 
                   facecolor='lightblue', alpha=0.8), fontsize=9)
            
            # 添加右侧最大回撤点信息文本框（优化样式）
            ax.text(0.98, 0.02, max_dd_text, transform=ax.transAxes, 
                   verticalalignment='bottom', horizontalalignment='right',
                   bbox=dict(boxstyle='round,pad=0.5', facecolor='#FFE4E1', 
                            edgecolor='#DC143C', linewidth=1, alpha=0.9), 
                   fontsize=10, fontweight='normal', color='#8B0000')
            
            # 调整布局
            plt.tight_layout()
            
            # 保存图片到本地
            image_path = self._save_image_to_local(fig, f"drawdown_{strategy_name}")
            
            plt.close(fig)
            return image_path
            
        except Exception as e:
            logging.error(f"生成回撤曲线图失败: {str(e)}")
            plt.close('all')
            return None
    
    def generate_comparison_chart(self, strategies_data: List[Dict[str, Any]], 
                                 chart_type: str = 'equity') -> str:
        """生成多策略对比图
        
        Args:
            strategies_data: 策略数据列表
            chart_type: 图表类型 ('equity' 或 'drawdown')
            
        Returns:
            base64编码的图片字符串
        """
        try:
            # 创建图表
            fig, ax = plt.subplots(figsize=(14, 8))
            
            colors = ['#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#6A994E', 
                     '#577590', '#F8961E', '#F9844A', '#90E0EF', '#023047']
            
            for i, strategy_data in enumerate(strategies_data[:10]):  # 最多显示10个策略
                strategy_name = strategy_data['name']
                file_path = strategy_data['file_path']
                color = colors[i % len(colors)]
                
                try:
                    # 读取数据
                    df = pd.read_csv(file_path)
                    df['candle_begin_time'] = pd.to_datetime(df['candle_begin_time'])
                    
                    # 重置净值
                    initial_value = df['净值'].iloc[0]
                    df['重置净值'] = df['净值'] / initial_value
                    
                    if chart_type == 'equity':
                        # 绘制净值曲线
                        ax.plot(df['candle_begin_time'], df['重置净值'], 
                               linewidth=2, color=color, label=strategy_name, alpha=0.8)
                    else:
                        # 计算并绘制回撤曲线
                        df['净值_cummax'] = df['重置净值'].cummax()
                        df['drawdown'] = (df['重置净值'] - df['净值_cummax']) / df['净值_cummax'] * 100
                        ax.plot(df['candle_begin_time'], df['drawdown'], 
                               linewidth=2, color=color, label=strategy_name, alpha=0.8)
                        
                except Exception as e:
                    logging.warning(f"处理策略 {strategy_name} 数据失败: {str(e)}")
                    continue
            
            # 设置标题和标签
            title = '多策略资金曲线对比' if chart_type == 'equity' else '多策略回撤对比'
            ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
            ax.set_xlabel('时间', fontsize=12)
            
            if chart_type == 'equity':
                ax.set_ylabel('净值', fontsize=12)
            else:
                ax.set_ylabel('回撤 (%)', fontsize=12)
                ax.set_ylim(None, 1)
            
            # 格式化x轴日期
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            ax.xaxis.set_major_locator(mdates.MonthLocator(interval=2))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
            
            # 添加网格
            ax.grid(True, alpha=0.3)
            
            # 添加图例
            ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
            
            # 调整布局
            plt.tight_layout()
            
            # 保存图片到本地
            image_path = self._save_image_to_local(fig, f"comparison_{chart_type}")
            
            plt.close(fig)
            return image_path
            
        except Exception as e:
            logging.error(f"生成对比图失败: {str(e)}")
            plt.close('all')
            return None

# =============================================================================
# 企业微信通知器
# =============================================================================

class WeChatNotifier:
    """企业微信通知器"""
    
    def __init__(self, config: WeChatConfig):
        """初始化企业微信通知器"""
        self.config = config
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'FundCurveAnalyzer/1.0'
        })
    
    def send_text_message(self, content: str, mentioned_list: List[str] = None, 
                         mentioned_mobile_list: List[str] = None) -> bool:
        """发送文本消息到企业微信"""
        if not self.config.enable_notification or not self.config.webhook_url:
            logging.warning("企业微信通知未启用或webhook URL未配置")
            return False
        
        # 处理消息长度限制
        if len(content) > self.config.max_message_length:
            content = content[:self.config.max_message_length - 50] + "\n\n[消息过长，已截断]"
        
        # 构建消息体
        message_data = {
            "msgtype": "text",
            "text": {
                "content": content
            }
        }
        
        # 添加@用户
        if mentioned_list or mentioned_mobile_list:
            message_data["text"]["mentioned_list"] = mentioned_list or self.config.mentioned_list
            message_data["text"]["mentioned_mobile_list"] = mentioned_mobile_list or self.config.mentioned_mobile_list
        
        try:
            response = self.session.post(
                self.config.webhook_url,
                json=message_data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    logging.info("企业微信消息发送成功")
                    return True
                else:
                    logging.error(f"企业微信消息发送失败: {result.get('errmsg', '未知错误')}")
                    return False
            else:
                logging.error(f"企业微信API请求失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            logging.error(f"发送企业微信消息时出错: {str(e)}")
            return False
    
    def send_markdown_message(self, content: str) -> bool:
        """发送Markdown格式消息到企业微信，支持长消息分段发送"""
        if not self.config.enable_notification or not self.config.webhook_url:
            logging.warning("企业微信通知未启用或webhook URL未配置")
            return False
        
        # 如果消息长度超过限制，分段发送（使用更保守的限制）
        if len(content) > 3000:  # 使用更保守的限制，确保不会超过企业微信的实际限制
            return self._send_long_markdown_message(content)
        else:
            return self._send_single_markdown_message(content)
    
    def _send_single_markdown_message(self, content: str) -> bool:
        """发送单条Markdown消息"""
        # 构建消息体
        message_data = {
            "msgtype": "markdown",
            "markdown": {
                "content": content
            }
        }
        
        try:
            response = self.session.post(
                self.config.webhook_url,
                json=message_data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    logging.info("企业微信Markdown消息发送成功")
                    return True
                else:
                    logging.error(f"企业微信Markdown消息发送失败: {result.get('errmsg', '未知错误')}")
                    return False
            else:
                logging.error(f"企业微信API请求失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            logging.error(f"发送企业微信Markdown消息时出错: {str(e)}")
            return False
    
    def _send_long_markdown_message(self, content: str) -> bool:
        """分段发送长Markdown消息"""
        # 使用更保守的长度限制，考虑到企业微信可能按字节计算长度
        max_length = 1500  # 非常保守的长度限制，预留足够空间
        segments = self._split_markdown_content(content, max_length)
        
        success_count = 0
        total_segments = len(segments)
        
        for i, segment in enumerate(segments, 1):
            # 添加简短的分段标识
            segment_header = f"**[{i}/{total_segments}]**\n\n"
            
            # 确保最终内容不超过限制，使用更保守的计算
            available_length = 3000 - len(segment_header)  # 使用更保守的固定值
            if len(segment) > available_length:
                segment = segment[:available_length] + "\n\n[内容过长已截断]"
            
            segment_content = segment_header + segment
            
            # 添加详细的调试信息
            logging.info(f"第{i}段消息长度: {len(segment_content)}")
            
            # 最终安全检查，使用更严格的限制
            if len(segment_content) > 3200:
                logging.error(f"第{i}段消息仍然超长: {len(segment_content)} > 3200")
                # 强制截断到更安全的长度
                segment_content = segment_content[:3000] + "\n[强制截断]"
                logging.info(f"强制截断后第{i}段消息长度: {len(segment_content)}")
            
            if self._send_single_markdown_message(segment_content):
                success_count += 1
                logging.info(f"第{i}/{total_segments}段发送成功 (长度: {len(segment_content)})")
                # 分段之间添加短暂延迟，避免发送过快
                if i < total_segments:
                    time.sleep(1)
            else:
                logging.error(f"发送第{i}段消息失败 (长度: {len(segment_content)})")
        
        if success_count == total_segments:
            logging.info(f"长消息分{total_segments}段发送完成")
            return True
        else:
            logging.error(f"长消息发送部分失败: {success_count}/{total_segments}段成功")
            return False
    
    def _split_markdown_content(self, content: str, max_length: int) -> List[str]:
        """智能分割Markdown内容，尽量保持格式完整性"""
        if len(content) <= max_length:
            return [content]
        
        segments = []
        current_segment = ""
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            # 检查添加当前行是否会超过长度限制
            if current_segment:
                test_segment = current_segment + '\n' + line
            else:
                test_segment = line
            
            if len(test_segment) <= max_length:
                current_segment = test_segment
            else:
                # 如果当前段不为空，保存它
                if current_segment:
                    segments.append(current_segment)
                    current_segment = line
                else:
                    # 单行过长，强制分割
                    if len(line) > max_length:
                        # 按字符强制分割长行
                        while len(line) > max_length:
                            segments.append(line[:max_length])
                            line = line[max_length:]
                        if line:  # 如果还有剩余内容
                            current_segment = line
                    else:
                        current_segment = line
        
        # 添加最后一段
        if current_segment:
            segments.append(current_segment)
        
        return segments
    
    def send_image(self, image_base64: str, image_md5: str = None) -> bool:
        """发送图片到企业微信群
        
        Args:
            image_base64: base64编码的图片数据
            image_md5: 图片的MD5值（可选）
            
        Returns:
            bool: 发送是否成功
        """
        if not self.config.enable_notification or not self.config.webhook_url:
            logging.warning("企业微信通知未启用或webhook URL未配置")
            return False
        
        try:
            # 如果没有提供MD5，则计算MD5
            if not image_md5:
                import hashlib
                image_bytes = base64.b64decode(image_base64)
                image_md5 = hashlib.md5(image_bytes).hexdigest()
            else:
                image_bytes = base64.b64decode(image_base64)
            
            # 记录图片大小信息
            image_size_mb = len(image_bytes) / (1024 * 1024)
            logging.info(f"准备发送图片: 大小 {image_size_mb:.2f}MB, MD5: {image_md5[:8]}...")
            
            # 检查图片大小限制
            if image_size_mb > 2.0:  # 企业微信图片大小限制通常为2MB
                logging.warning(f"图片大小 {image_size_mb:.2f}MB 可能超过企业微信限制")
            
            # 构建消息体
            message_data = {
                "msgtype": "image",
                "image": {
                    "base64": image_base64,
                    "md5": image_md5
                }
            }
            
            response = self.session.post(
                self.config.webhook_url,
                json=message_data,
                timeout=60  # 图片上传可能需要更长时间
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    logging.info("企业微信图片发送成功")
                    return True
                else:
                    logging.error(f"企业微信图片发送失败: {result.get('errmsg', '未知错误')}")
                    return False
            else:
                logging.error(f"企业微信图片API请求失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            logging.error(f"发送企业微信图片时出错: {str(e)}")
            return False
    
    def send_chart_with_description(self, image_base64: str, description: str, 
                                   strategy_name: str = None) -> bool:
        """发送图表和描述信息
        
        Args:
            image_base64: base64编码的图片数据
            description: 图表描述信息
            strategy_name: 策略名称（可选）
            
        Returns:
            bool: 发送是否成功
        """
        success = True
        
        # 先发送描述信息
        if description:
            if strategy_name:
                full_description = f"**{strategy_name}**\n\n{description}"
            else:
                full_description = description
            
            if not self.send_markdown_message(full_description):
                logging.warning("图表描述信息发送失败")
                success = False
        
        # 再发送图片
        if not self.send_image(image_base64):
            logging.error("图表图片发送失败")
            success = False
        
        return success
    
    def send_image_from_file(self, image_path: str) -> bool:
        """从本地文件发送图片到企业微信群
        
        Args:
            image_path: 本地图片文件路径
            
        Returns:
            bool: 发送是否成功
        """
        if not self.config.enable_notification or not self.config.webhook_url:
            logging.warning("企业微信通知未启用或webhook URL未配置")
            return False
        
        try:
            # 检查文件是否存在
            if not os.path.exists(image_path):
                logging.error(f"图片文件不存在: {image_path}")
                return False
            
            # 读取图片文件并转换为base64
            with open(image_path, 'rb') as f:
                image_bytes = f.read()
            
            image_base64 = base64.b64encode(image_bytes).decode('utf-8')
            
            # 计算MD5
            import hashlib
            image_md5 = hashlib.md5(image_bytes).hexdigest()
            
            # 记录图片信息
            image_size_mb = len(image_bytes) / (1024 * 1024)
            logging.info(f"准备发送本地图片: {image_path}, 大小: {image_size_mb:.2f}MB, MD5: {image_md5[:8]}...")
            
            # 检查图片大小限制
            if image_size_mb > 2.0:  # 企业微信图片大小限制通常为2MB
                logging.warning(f"图片大小 {image_size_mb:.2f}MB 可能超过企业微信限制")
            
            # 构建消息体
            message_data = {
                "msgtype": "image",
                "image": {
                    "base64": image_base64,
                    "md5": image_md5
                }
            }
            
            response = self.session.post(
                self.config.webhook_url,
                json=message_data,
                timeout=60  # 图片上传可能需要更长时间
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    logging.info(f"企业微信图片发送成功: {image_path}")
                    return True
                else:
                    logging.error(f"企业微信图片发送失败: {result.get('errmsg', '未知错误')}")
                    return False
            else:
                logging.error(f"企业微信图片API请求失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            logging.error(f"发送企业微信图片时出错: {str(e)}")
            return False
    
    def send_chart_with_description_from_file(self, image_path: str, description: str, 
                                            strategy_name: str = None) -> bool:
        """从本地文件发送图表和描述信息
        
        Args:
            image_path: 本地图片文件路径
            description: 图表描述信息
            strategy_name: 策略名称（可选）
            
        Returns:
            bool: 发送是否成功
        """
        success = True
        
        # 先发送描述信息
        if description:
            if strategy_name:
                full_description = f"**{strategy_name}**\n\n{description}"
            else:
                full_description = description
            
            if not self.send_markdown_message(full_description):
                logging.warning("图表描述信息发送失败")
                success = False
        
        # 再发送图片
        if not self.send_image_from_file(image_path):
            logging.error("图表图片发送失败")
            success = False
        
        return success

# =============================================================================
# 报告格式化类
# =============================================================================

class ReportFormatter:
    """报告格式化器"""
    
    @staticmethod
    def format_single_strategy_report(strategy_name: str, statistics: Dict[str, Any]) -> str:
        """格式化单策略报告"""
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 判断表现等级
        total_return = statistics.get('总收益率', 0)
        max_drawdown = statistics.get('最大回撤', 0)
        
        if total_return > 50 and max_drawdown < 20:
            performance_level = "🔥 优秀"
        elif total_return > 20 and max_drawdown < 30:
            performance_level = "✅ 良好"
        elif total_return > 0 and max_drawdown < 50:
            performance_level = "⚠️ 一般"
        else:
            performance_level = "❌ 较差"
        
        report = f"""# 📊 策略分析报告

**策略名称**: {strategy_name}
**分析时间**: {current_time}
**表现等级**: {performance_level}

## 📈 收益指标
- **总收益率**: {statistics.get('总收益率', 0):.2g}%

## ⚠️ 风险指标
- **最大回撤**: {statistics.get('最大回撤', 0):.2g}%
- **当前回撤**: {statistics.get('当前回撤', 0):.2g}%
- **距离最大回撤空间**: {statistics.get('距离最大回撤空间', 0):.2g}%
- **收益率标准差**: {statistics.get('收益率标准差', 0):.2g}


## ⏰ 时间指标
- **最大回撤持续天数**: {statistics.get('最大回撤持续天数', 0):.0f}天
- **最长不创新高天数**: {statistics.get('最长不创新高天数', 0):.0f}天
- **距离前高天数**: {statistics.get('距离前高天数', 0):.0f}天
- **历史最长回撤持续天数**: {statistics.get('历史最长回撤持续天数', 0):.0f}天

## 📅 关键时间点
- **最新一次新高**: {statistics.get('最新一次新高时间', '无')}
- **最大回撤期间**: {statistics.get('最大回撤开始时间', '无')} ~ {statistics.get('最大回撤结束时间', '无')}
- **最长不创新高期间**: {statistics.get('最长不创新高开始时间', '无')} ~ {statistics.get('最长不创新高结束时间', '无')}
- **历史最长回撤期间**: {statistics.get('历史最长回撤开始时间', '无')} ~ {statistics.get('历史最长回撤结束时间', '无')}
- **历史最长回撤幅度**: {statistics.get('历史最长回撤幅度', 0):.2g}%
- **数据时间范围**: {statistics.get('开始时间', '无')} ~ {statistics.get('结束时间', '无')}
- **交易天数**: {statistics.get('交易天数', 0)}天
- **数据点数**: {statistics.get('数据点数', 0)}个
"""
        
        # 添加年度最大回撤分析
        yearly_analysis = statistics.get('年度最大回撤分析', {})
        if yearly_analysis:
            report += "\n## 📊 年度最大回撤分析\n"
            for year, data in sorted(yearly_analysis.items()):
                report += f"\n**{year}年**\n"
                report += f"- 年度收益率: {data.get('年度收益率', 0):.2g}%\n"
                report += f"- 最大回撤: {data.get('年度最大回撤', 0):.2g}%\n"
                if data.get('回撤开始时间') != '无':
                    report += f"- 回撤期间: {data.get('回撤开始时间', '无')} ~ {data.get('回撤结束时间', '无')}\n"
                    report += f"- 回撤持续: {data.get('回撤持续天数', 0):.0f}天\n"
                else:
                    report += "- 回撤期间: 无回撤\n"
        
        return report
    
    @staticmethod
    def format_strategy_ranking_report(strategies_data: List[Dict[str, Any]], 
                                     title: str = "年度策略分析") -> str:
        """格式化年度策略分析报告"""
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        report = f"""# 📅 {title}

**分析时间**: {current_time}
**策略数量**: {len(strategies_data)}个

"""
        
        # 收集所有年份信息
        all_years = set()
        for strategy in strategies_data:
            yearly_analysis = strategy['statistics'].get('年度最大回撤分析', {})
            all_years.update(yearly_analysis.keys())
        
        # 按年份排序，并添加"全部"选项
        sorted_years = sorted(all_years)
        
        # 为每个策略生成报告
        for strategy in strategies_data:
            strategy_name = strategy['name']
            yearly_analysis = strategy['statistics'].get('年度最大回撤分析', {})
            stats = strategy['statistics']
            
            report += f"""## {strategy_name}

| 所属年份 | 总收益率 | 最大回撤 | 当前回撤 | 距离最大回撤空间 | 最长回撤天数 | 回撤开始时间 | 回撤结束时间 | 最长不创新高 | 不创新高开始时间 | 不创新高结束时间 | 距离前高天数 |
|---------|---------|---------|---------|----------------|-------------|-------------|-------------|-------------|----------------|----------------|-------------|
"""
            
            # 添加各年份数据
            for year in sorted_years:
                year_data = yearly_analysis.get(year, {})
                
                # 获取年度数据
                annual_return = year_data.get('年度收益率', 0)
                max_drawdown = year_data.get('年度最大回撤', 0)
                current_drawdown = year_data.get('年度当前回撤', 0)
                drawdown_start = year_data.get('回撤开始时间', 'N/A')
                drawdown_end = year_data.get('回撤结束时间', 'N/A')
                drawdown_days = year_data.get('回撤持续天数', 0)
                
                # 获取年度最长不创新高信息
                longest_no_new_high = year_data.get('年度最长不创新高天数', 0)
                no_new_high_start = year_data.get('年度不创新高开始时间', 'N/A')
                no_new_high_end = year_data.get('年度不创新高结束时间', 'N/A')
                
                # 格式化时间
                if drawdown_start != 'N/A' and isinstance(drawdown_start, str):
                    drawdown_start = drawdown_start[:10]  # 只显示日期部分
                if drawdown_end != 'N/A' and isinstance(drawdown_end, str):
                    drawdown_end = drawdown_end[:10]
                if no_new_high_start != 'N/A' and isinstance(no_new_high_start, str):
                    no_new_high_start = no_new_high_start[:10]
                if no_new_high_end != 'N/A' and isinstance(no_new_high_end, str):
                    no_new_high_end = no_new_high_end[:10]
                
                # 获取年度距离前高天数
                days_since_high_year = year_data.get('年度距离前高天数', 0)
                
                # 计算年度距离最大回撤空间
                if current_drawdown < 100:  # 避免除零错误
                    year_drawdown_space = ((100 - max_drawdown) / (100 - current_drawdown) - 1) * 100 if max_drawdown > 0 else ((100 - 0) / (100 - current_drawdown) - 1) * 100
                else:
                    year_drawdown_space = 0
                
                report += f"| {year}年 | {annual_return:.2f}% | {max_drawdown:.2f}% | {current_drawdown:.2f}% | {year_drawdown_space:.2f}% | {drawdown_days:.1f}天 | {drawdown_start} | {drawdown_end} | {longest_no_new_high:.1f}天 | {no_new_high_start} | {no_new_high_end} | {days_since_high_year:.1f}天 |\n"
            
            # 添加全部时间段的统计
            # 格式化时间
            drawdown_start = stats.get('最大回撤开始时间', 'N/A')
            drawdown_end = stats.get('最大回撤结束时间', 'N/A')
            no_new_high_start = stats.get('最长不创新高开始时间', 'N/A')
            no_new_high_end = stats.get('最长不创新高结束时间', 'N/A')
            
            if drawdown_start != 'N/A' and isinstance(drawdown_start, str):
                drawdown_start = drawdown_start[:10]
            if drawdown_end != 'N/A' and isinstance(drawdown_end, str):
                drawdown_end = drawdown_end[:10]
            if no_new_high_start != 'N/A' and isinstance(no_new_high_start, str):
                no_new_high_start = no_new_high_start[:10]
            if no_new_high_end != 'N/A' and isinstance(no_new_high_end, str):
                no_new_high_end = no_new_high_end[:10]
            
            report += f"| 全部 | {stats.get('总收益率', 0):.2f}% | {stats.get('最大回撤', 0):.2f}% | {stats.get('当前回撤', 0):.2f}% | {stats.get('距离最大回撤空间', 0):.2f}% | {stats.get('最大回撤持续天数', 0):.1f}天 | {drawdown_start} | {drawdown_end} | {stats.get('最长不创新高天数', 0):.1f}天 | {no_new_high_start} | {no_new_high_end} | {stats.get('距离前高天数', 0):.1f}天 |\n"
            
            report += "\n"
        
        return report
    
    @staticmethod
    def format_comparison_report(strategies_data: List[Dict[str, Any]]) -> str:
        """格式化多策略对比报告"""
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        report = f"""# 📊 策略对比分析

**分析时间**: {current_time}
**对比策略**: {len(strategies_data)}个

## 📈 关键指标对比

| 策略名称 | 总收益率 | 最大回撤 | 距离最大回撤空间 | 最长回撤天数 | 最长不创新高 |
|---------|---------|---------|----------------|-------------|-------------|
"""
        
        for strategy in strategies_data:
            stats = strategy['statistics']
            name = strategy['name'][:8] + ('...' if len(strategy['name']) > 8 else '')
            
            report += f"| {name} | {stats.get('总收益率', 0):.2f}% | {stats.get('最大回撤', 0):.2f}% | {stats.get('距离最大回撤空间', 0):.0f}% | {stats.get('历史最长回撤持续天数', 0):.1f}天 | {stats.get('最长不创新高天数', 0):.1f}天 |\n"
        
        # 添加统计摘要
        total_returns = [s['statistics'].get('总收益率', 0) for s in strategies_data]
        max_drawdowns = [s['statistics'].get('最大回撤', 0) for s in strategies_data]
        longest_drawdown_days = [s['statistics'].get('历史最长回撤持续天数', 0) for s in strategies_data]
        longest_no_new_high_days = [s['statistics'].get('最长不创新高天数', 0) for s in strategies_data]
        
        report += f"""

## 📊 统计摘要

- **最佳收益**: {max(total_returns):.2f}% ({strategies_data[total_returns.index(max(total_returns))]['name']})
- **最小回撤**: {min(max_drawdowns):.2f}% ({strategies_data[max_drawdowns.index(min(max_drawdowns))]['name']})
- **最短回撤期**: {min(longest_drawdown_days):.1f}天 ({strategies_data[longest_drawdown_days.index(min(longest_drawdown_days))]['name']})
- **最短不创新高期**: {min(longest_no_new_high_days):.1f}天 ({strategies_data[longest_no_new_high_days.index(min(longest_no_new_high_days))]['name']})
- **平均收益**: {np.mean(total_returns):.2f}%
- **平均回撤**: {np.mean(max_drawdowns):.2f}%
"""
        
        return report
    
    @staticmethod
    def format_alert_report(strategy_name: str, statistics: Dict[str, Any], 
                          alert_conditions: Dict[str, Any]) -> str:
        """格式化告警报告"""
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        alerts = []
        
        # 检查各种告警条件
        if statistics.get('当前回撤', 0) > alert_conditions.get('max_current_drawdown', 20):
            alerts.append(f"⚠️ 当前回撤过大: {statistics.get('当前回撤', 0):.2f}%")
        
        if statistics.get('距离前高天数', 0) > alert_conditions.get('max_days_since_high', 30):
            alerts.append(f"⚠️ 长期未创新高: {statistics.get('距离前高天数', 0):.1f}天")
        

        
        if not alerts:
            return None
        
        report = f"""# 🚨 策略告警

**策略名称**: {strategy_name}
**告警时间**: {current_time}

## ⚠️ 告警详情

"""
        
        for alert in alerts:
            report += f"- {alert}\n"
        
        report += f"""

## 📊 当前状态
- **总收益率**: {statistics.get('总收益率', 0):.2f}%
- **最大回撤**: {statistics.get('最大回撤', 0):.2f}%
- **当前回撤**: {statistics.get('当前回撤', 0):.2f}%
- **距离前高天数**: {statistics.get('距离前高天数', 0):.1f}天

"""
        
        return report

# =============================================================================
# 主分析器类
# =============================================================================

class FundCurveAnalyzer:
    """资金曲线分析器主类"""
    
    def __init__(self, wechat_config: WeChatConfig, analysis_config: AnalysisConfig):
        """初始化分析器"""
        self.wechat_config = wechat_config
        self.analysis_config = analysis_config
        
        # 初始化组件
        self.fund_manager = FundCurveManager(
            data_dir=analysis_config.data_dir,
            results_subdir=analysis_config.results_subdir
        )
        self.notifier = WeChatNotifier(wechat_config)
        self.formatter = ReportFormatter()
        # 设置图片保存目录
        charts_dir = os.path.join(os.path.dirname(__file__), 'charts')
        self.chart_generator = ChartGenerator(dpi=analysis_config.chart_dpi, save_dir=charts_dir)
        
        # 设置日志
        self._setup_logging()
    
    def _setup_logging(self):
        """设置日志"""
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        
        # 清除现有的处理器
        for handler in logging.root.handlers[:]:
            logging.root.removeHandler(handler)
            
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('fund_curve_analyzer.log', encoding='utf-8', mode='w')
            ],
            force=True
        )
        
        logging.info("日志系统已初始化")
    
    def analyze_single_strategy(self, strategy_name: str, send_notification: bool = True) -> bool:
        """分析单个策略"""
        try:
            # 获取策略列表
            strategies_result = self.fund_manager.get_available_strategies()
            if not strategies_result['success']:
                logging.error(f"获取策略列表失败: {strategies_result['message']}")
                return False
            
            # 查找指定策略
            target_strategy = None
            for strategy in strategies_result['strategies']:
                if strategy['name'] == strategy_name:
                    target_strategy = strategy
                    break
            
            if not target_strategy:
                logging.error(f"未找到策略: {strategy_name}")
                return False
            
            # 计算时间范围
            start_time = None
            if self.analysis_config.time_range_days:
                start_time = (datetime.now() - timedelta(days=self.analysis_config.time_range_days)).strftime('%Y-%m-%d')
            
            # 获取策略数据
            strategy_data = self.fund_manager.get_strategy_data(
                target_strategy['file_path'],
                start_time=start_time
            )
            
            if not strategy_data['success']:
                logging.error(f"获取策略数据失败: {strategy_data['message']}")
                return False
            
            # 检查数据点数
            if strategy_data['statistics']['数据点数'] < self.analysis_config.min_data_points:
                logging.warning(f"策略 {strategy_name} 数据点数不足: {strategy_data['statistics']['数据点数']}")
                return False
            
            # 生成报告
            report = self.formatter.format_single_strategy_report(
                strategy_name,
                strategy_data['statistics']
            )
            
            # 发送通知
            if send_notification:
                # 发送文本报告
                success = self.notifier.send_markdown_message(report)
                if success:
                    logging.info(f"策略 {strategy_name} 分析报告发送成功")
                else:
                    logging.error(f"策略 {strategy_name} 分析报告发送失败")
                
                # 生成并发送图表
                chart_success = self._generate_and_send_charts(
                    strategy_data['dataframe'], 
                    strategy_name, 
                    strategy_data['statistics']
                )
                
                # 发送完成后清空保存的图片
                if self.analysis_config.enable_charts:
                    try:
                        clear_success = self.chart_generator.clear_saved_images()
                        if clear_success:
                            logging.info("图片文件清空完成")
                        else:
                            logging.warning("图片文件清空失败")
                    except Exception as e:
                        logging.error(f"清空图片文件时出错: {str(e)}")
                
                return success and chart_success
            else:
                print(report)
                return True
                
        except Exception as e:
            logging.error(f"分析策略 {strategy_name} 时出错: {str(e)}")
            return False
    
    def _generate_and_send_charts(self, df: pd.DataFrame, strategy_name: str, 
                                 statistics: Dict[str, Any]) -> bool:
        """生成并发送图表
        
        Args:
            df: 策略数据DataFrame
            strategy_name: 策略名称
            statistics: 统计数据
            
        Returns:
            bool: 是否成功
        """
        # 检查是否启用图表生成
        if not self.analysis_config.enable_charts:
            logging.info("图表生成已禁用，跳过图表生成")
            return True
        
        try:
            chart_success = True
            logging.info(f"开始生成 {strategy_name} 的图表")
            
            # 生成资金曲线图
            logging.info(f"正在生成 {strategy_name} 的资金曲线图")
            equity_chart = self.chart_generator.generate_equity_curve_chart(
                df, strategy_name, statistics
            )
            logging.info(f"资金曲线图生成结果: {equity_chart is not None}，路径: {equity_chart}")
            
            if equity_chart:
                description = f"📈 **{strategy_name} - 资金曲线图**\n\n" + \
                            f"总收益率: {statistics.get('总收益率', 0):.2f}% | " + \
                            f"年化收益率: {statistics.get('年化收益率', 0):.2f}% | " + \
                            f"最大回撤: {statistics.get('最大回撤', 0):.2f}%"
                
                if not self.notifier.send_chart_with_description_from_file(
                    equity_chart, description, strategy_name
                ):
                    logging.error(f"发送 {strategy_name} 资金曲线图失败")
                    chart_success = False
                else:
                    logging.info(f"发送 {strategy_name} 资金曲线图成功")
            else:
                logging.error(f"生成 {strategy_name} 资金曲线图失败")
                chart_success = False
            
            # 生成回撤曲线图
            drawdown_chart = self.chart_generator.generate_drawdown_chart(
                df, strategy_name, statistics
            )
            
            if drawdown_chart:
                description = f"📉 **{strategy_name} - 回撤分析图**\n\n" + \
                            f"最大回撤: {statistics.get('最大回撤', 0):.2f}% | " + \
                            f"当前回撤: {statistics.get('当前回撤', 0):.2f}% | " + \
                            f"回撤持续: {statistics.get('最大回撤持续天数', 0):.1f}天"
                
                if not self.notifier.send_chart_with_description_from_file(
                    drawdown_chart, description, strategy_name
                ):
                    logging.error(f"发送 {strategy_name} 回撤曲线图失败")
                    chart_success = False
                else:
                    logging.info(f"发送 {strategy_name} 回撤曲线图成功")
            else:
                logging.error(f"生成 {strategy_name} 回撤曲线图失败")
                chart_success = False
            
            return chart_success
            
        except Exception as e:
            logging.error(f"生成和发送 {strategy_name} 图表时出错: {str(e)}")
            return False
    
    def analyze_all_strategies(self, send_notification: bool = True) -> bool:
        """分析所有策略并生成排行榜"""
        try:
            # 获取策略列表
            strategies_result = self.fund_manager.get_available_strategies()
            if not strategies_result['success']:
                logging.error(f"获取策略列表失败: {strategies_result['message']}")
                return False
            
            strategies = strategies_result['strategies']
            
            # 应用策略过滤
            if self.analysis_config.strategy_filter:
                strategies = [s for s in strategies if s['name'] in self.analysis_config.strategy_filter]
            
            if not strategies:
                logging.warning("没有找到符合条件的策略")
                return False
            
            # 计算时间范围
            start_time = None
            if self.analysis_config.time_range_days:
                start_time = (datetime.now() - timedelta(days=self.analysis_config.time_range_days)).strftime('%Y-%m-%d')
            
            # 分析每个策略
            analyzed_strategies = []
            for strategy in strategies:
                try:
                    strategy_data = self.fund_manager.get_strategy_data(
                        strategy['file_path'],
                        start_time=start_time
                    )
                    
                    if strategy_data['success']:
                        # 检查数据点数
                        if strategy_data['statistics']['数据点数'] >= self.analysis_config.min_data_points:
                            analyzed_strategies.append({
                                'name': strategy['name'],
                                'file_path': strategy['file_path'],  # 添加文件路径，用于图表生成
                                'statistics': strategy_data['statistics']
                            })
                        else:
                            logging.warning(f"策略 {strategy['name']} 数据点数不足")
                    else:
                        logging.warning(f"策略 {strategy['name']} 分析失败: {strategy_data['message']}")
                        
                except Exception as e:
                    logging.warning(f"分析策略 {strategy['name']} 时出错: {str(e)}")
                    continue
            
            if not analyzed_strategies:
                logging.error("没有成功分析的策略")
                return False
            
            # 按总收益率排序
            analyzed_strategies.sort(
                key=lambda x: x['statistics'].get('总收益率', 0),
                reverse=True
            )
            
            # 限制报告中的策略数量
            display_strategies = analyzed_strategies[:self.analysis_config.max_strategies_in_report]
            
            # 生成排行榜报告
            report = self.formatter.format_strategy_ranking_report(
                display_strategies,
                "策略收益率排行榜"
            )
            
            # 如果启用对比功能且有多个策略，生成对比报告
            if self.analysis_config.enable_comparison and len(display_strategies) > 1:
                comparison_report = self.formatter.format_comparison_report(display_strategies[:5])
                report += "\n\n" + comparison_report
            
            # 发送通知
            if send_notification:
                success = self.notifier.send_markdown_message(report)
                if success:
                    logging.info(f"策略排行榜报告发送成功，包含 {len(display_strategies)} 个策略")
                else:
                    logging.error("策略排行榜报告发送失败")
                    return False
                
                # 如果启用图表功能且有多个策略，生成并发送对比图表
                if self.analysis_config.enable_charts and len(display_strategies) > 1:
                    try:
                        logging.info("开始生成策略对比图表")
                        
                        # 生成资金曲线对比图
                        equity_chart_path = self.chart_generator.generate_comparison_chart(
                            display_strategies[:5], 
                            chart_type='equity'
                        )
                        
                        if equity_chart_path:
                            logging.info(f"资金曲线对比图生成成功: {equity_chart_path}")
                            chart_success = self.notifier.send_chart_with_description_from_file(
                                equity_chart_path,
                                "📈 策略资金曲线对比图"
                            )
                            if chart_success:
                                logging.info("资金曲线对比图发送成功")
                            else:
                                logging.error("资金曲线对比图发送失败")
                        else:
                            logging.error("资金曲线对比图生成失败")
                        
                        # 为每个策略单独生成回撤图
                        logging.info("开始为每个策略生成单独的回撤图")
                        for strategy in display_strategies[:5]:
                            try:
                                strategy_name = strategy['name']
                                # 读取策略数据
                                strategy_data = self.fund_manager.get_strategy_data(strategy['file_path'])
                                if strategy_data['success']:
                                    df = strategy_data['data']
                                    
                                    # 生成单个策略的回撤图
                                    drawdown_chart_path = self.chart_generator.generate_drawdown_chart(
                                        df, 
                                        strategy_name, 
                                        strategy_data['statistics']
                                    )
                                    
                                    if drawdown_chart_path:
                                        logging.info(f"策略 {strategy_name} 回撤图生成成功: {drawdown_chart_path}")
                                        
                                        # 构建描述信息
                                        description = f"📉 **{strategy_name} - 回撤分析图**\n\n" + \
                                                    f"最大回撤: {strategy_data['statistics']['最大回撤']:.2f}%\n" + \
                                                    f"当前回撤: {strategy_data['statistics']['当前回撤']:.2f}%"
                                        
                                        chart_success = self.notifier.send_chart_with_description_from_file(
                                            drawdown_chart_path,
                                            description
                                        )
                                        if chart_success:
                                            logging.info(f"策略 {strategy_name} 回撤图发送成功")
                                        else:
                                            logging.error(f"策略 {strategy_name} 回撤图发送失败")
                                    else:
                                        logging.error(f"策略 {strategy_name} 回撤图生成失败")
                                else:
                                    logging.error(f"读取策略 {strategy_name} 数据失败: {strategy_data['message']}")
                            except Exception as e:
                                logging.error(f"为策略 {strategy.get('name', 'Unknown')} 生成回撤图时出错: {str(e)}")
                            
                    except Exception as e:
                        logging.error(f"生成对比图表时出错: {str(e)}")
                
                # 发送完成后清空保存的图片
                if self.analysis_config.enable_charts:
                    try:
                        clear_success = self.chart_generator.clear_saved_images()
                        if clear_success:
                            logging.info("图片文件清空完成")
                        else:
                            logging.warning("图片文件清空失败")
                    except Exception as e:
                        logging.error(f"清空图片文件时出错: {str(e)}")
                
                return success
            else:
                print(report)
                return True
                
        except Exception as e:
            logging.error(f"分析所有策略时出错: {str(e)}")
            return False
    
    def check_alerts(self, alert_conditions: Dict[str, Any] = None, 
                    send_notification: bool = True) -> bool:
        """检查告警条件"""
        if alert_conditions is None:
            alert_conditions = {
                'max_current_drawdown': 20,  # 当前回撤超过20%告警
                'max_days_since_high': 30,   # 超过30天未创新高告警
    
            }
        
        try:
            # 获取策略列表
            strategies_result = self.fund_manager.get_available_strategies()
            if not strategies_result['success']:
                logging.error(f"获取策略列表失败: {strategies_result['message']}")
                return False
            
            strategies = strategies_result['strategies']
            
            # 应用策略过滤
            if self.analysis_config.strategy_filter:
                strategies = [s for s in strategies if s['name'] in self.analysis_config.strategy_filter]
            
            alert_reports = []
            
            # 检查每个策略
            for strategy in strategies:
                try:
                    strategy_data = self.fund_manager.get_strategy_data(strategy['file_path'])
                    
                    if strategy_data['success']:
                        alert_report = self.formatter.format_alert_report(
                            strategy['name'],
                            strategy_data['statistics'],
                            alert_conditions
                        )
                        
                        if alert_report:
                            alert_reports.append(alert_report)
                            
                except Exception as e:
                    logging.warning(f"检查策略 {strategy['name']} 告警时出错: {str(e)}")
                    continue
            
            # 发送告警通知
            if alert_reports and send_notification:
                for report in alert_reports:
                    success = self.notifier.send_markdown_message(report)
                    if success:
                        logging.info("告警报告发送成功")
                    else:
                        logging.error("告警报告发送失败")
                        return False
                return True
            elif alert_reports:
                for report in alert_reports:
                    print(report)
                    print("\n" + "="*50 + "\n")
                return True
            else:
                logging.info("没有触发告警的策略")
                return True
                
        except Exception as e:
            logging.error(f"检查告警时出错: {str(e)}")
            return False

# =============================================================================
# 配置管理
# =============================================================================

def load_config_from_file(config_file: str) -> Tuple[WeChatConfig, AnalysisConfig]:
    """从配置文件加载配置"""
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        wechat_config = WeChatConfig.from_dict(config_data.get('wechat', {}))
        analysis_config = AnalysisConfig.from_dict(config_data.get('analysis', {}))
        
        return wechat_config, analysis_config
        
    except Exception as e:
        logging.error(f"加载配置文件失败: {str(e)}")
        raise

def load_config_from_default() -> Tuple[WeChatConfig, AnalysisConfig]:
    """从内置默认配置加载配置"""
    wechat_config = WeChatConfig.from_default()
    analysis_config = AnalysisConfig.from_default()
    return wechat_config, analysis_config

def load_config_with_overrides(**overrides) -> Tuple[WeChatConfig, AnalysisConfig]:
    """从默认配置加载并应用覆盖参数
    
    Args:
        **overrides: 配置覆盖参数，支持以下格式：
            - webhook_url: 企业微信webhook URL
            - enable_notification: 是否启用通知
            - data_dir: 数据目录
            - min_data_points: 最少数据点数
            等等...
    
    Returns:
        Tuple[WeChatConfig, AnalysisConfig]: 配置对象元组
    """
    # 从默认配置开始
    config_data = DEFAULT_CONFIG.copy()
    
    # 分离微信和分析配置的覆盖参数
    wechat_overrides = {}
    analysis_overrides = {}
    
    wechat_keys = {'webhook_url', 'mentioned_list', 'mentioned_mobile_list', 
                   'enable_notification', 'max_message_length'}
    analysis_keys = {'data_dir', 'results_subdir', 'strategy_filter', 
                    'time_range_days', 'min_data_points', 'enable_comparison', 
                    'max_strategies_in_report'}
    
    for key, value in overrides.items():
        if key in wechat_keys:
            wechat_overrides[key] = value
        elif key in analysis_keys:
            analysis_overrides[key] = value
        else:
            logging.warning(f"未知的配置参数: {key}")
    
    # 应用覆盖参数
    config_data['wechat'].update(wechat_overrides)
    config_data['analysis'].update(analysis_overrides)
    
    # 创建配置对象
    wechat_config = WeChatConfig.from_dict(config_data['wechat'])
    analysis_config = AnalysisConfig.from_dict(config_data['analysis'])
    
    return wechat_config, analysis_config

def create_default_config_file(config_file: str):
    """创建默认配置文件"""
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(DEFAULT_CONFIG, f, ensure_ascii=False, indent=2)
    
    print(f"默认配置文件已创建: {config_file}")
    print("请编辑配置文件后重新运行")

def get_config_template() -> Dict[str, Any]:
    """获取配置模板"""
    return DEFAULT_CONFIG.copy()

def validate_config(wechat_config: WeChatConfig, analysis_config: AnalysisConfig) -> List[str]:
    """验证配置有效性
    
    Returns:
        List[str]: 验证错误列表，空列表表示配置有效
    """
    errors = []
    
    # 验证企业微信配置
    if wechat_config.enable_notification:
        if not wechat_config.webhook_url:
            errors.append("启用通知时必须设置webhook_url")
        elif "YOUR_KEY_HERE" in wechat_config.webhook_url:
            errors.append("请替换webhook_url中的YOUR_KEY_HERE为实际的key")
    
    # 验证分析配置
    if analysis_config.min_data_points < 1:
        errors.append("min_data_points必须大于0")
    
    if analysis_config.max_strategies_in_report < 1:
        errors.append("max_strategies_in_report必须大于0")
    
    if analysis_config.time_range_days is not None and analysis_config.time_range_days < 1:
        errors.append("time_range_days必须大于0或为None")
    
    return errors

# =============================================================================
# 命令行接口
# =============================================================================

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='资金曲线分析器 - 企业微信版')
    
    # 基本参数
    parser.add_argument('--config', '-c', help='配置文件路径（可选，不指定则使用内置配置）')
    parser.add_argument('--mode', '-m', choices=['single', 'all', 'alert', 'test'], 
                       default='all', help='运行模式')
    parser.add_argument('--strategy', '-s', help='指定策略名称（单策略模式）')
    parser.add_argument('--no-send', action='store_true', help='不发送通知，仅打印到控制台')
    parser.add_argument('--create-config', action='store_true', help='创建默认配置文件')
    
    # 配置覆盖参数
    parser.add_argument('--webhook-url', help='企业微信webhook URL')
    parser.add_argument('--data-dir', help='数据目录路径')
    parser.add_argument('--min-data-points', type=int, help='最少数据点数')
    parser.add_argument('--max-strategies', type=int, help='报告中最多显示的策略数量')
    parser.add_argument('--time-range-days', type=int, help='分析时间范围（天数）')
    parser.add_argument('--results-subdir', help='回测结果子目录名称')
    
    # 调试和信息参数
    parser.add_argument('--show-config', action='store_true', help='显示当前配置并退出')
    parser.add_argument('--validate-config', action='store_true', help='验证配置有效性')
    parser.add_argument('--list-strategies', action='store_true', help='列出所有可用策略')
    
    args = parser.parse_args()
    
    # 创建默认配置文件
    if args.create_config:
        config_file = args.config or 'config.json'
        create_default_config_file(config_file)
        return
    
    try:
        # 加载配置
        if args.config and os.path.exists(args.config):
            # 从配置文件加载
            wechat_config, analysis_config = load_config_from_file(args.config)
            print(f"✅ 从配置文件加载配置: {args.config}")
        else:
            # 使用内置默认配置
            if args.config:
                print(f"⚠️ 配置文件不存在: {args.config}，使用内置默认配置")
            else:
                print("📋 使用内置默认配置")
            
            # 构建配置覆盖参数
            overrides = {}
            if args.webhook_url:
                overrides['webhook_url'] = args.webhook_url
            if args.data_dir:
                overrides['data_dir'] = args.data_dir
            if args.min_data_points is not None:
                overrides['min_data_points'] = args.min_data_points
            if args.max_strategies is not None:
                overrides['max_strategies_in_report'] = args.max_strategies
            if args.time_range_days is not None:
                overrides['time_range_days'] = args.time_range_days
            if args.results_subdir:
                overrides['results_subdir'] = args.results_subdir
            if args.no_send:
                overrides['enable_notification'] = False
            
            # 加载配置并应用覆盖
            if overrides:
                wechat_config, analysis_config = load_config_with_overrides(**overrides)
                print(f"📝 应用了 {len(overrides)} 个配置覆盖参数")
            else:
                wechat_config, analysis_config = load_config_from_default()
        
        # 显示配置
        if args.show_config:
            print("\n📋 当前配置:")
            print("\n企业微信配置:")
            print(f"  webhook_url: {wechat_config.webhook_url[:50]}..." if len(wechat_config.webhook_url) > 50 else f"  webhook_url: {wechat_config.webhook_url}")
            print(f"  enable_notification: {wechat_config.enable_notification}")
            print(f"  mentioned_list: {wechat_config.mentioned_list}")
            print(f"  max_message_length: {wechat_config.max_message_length}")
            
            print("\n分析配置:")
            print(f"  data_dir: {analysis_config.data_dir}")
            print(f"  results_subdir: {analysis_config.results_subdir}")
            print(f"  min_data_points: {analysis_config.min_data_points}")
            print(f"  max_strategies_in_report: {analysis_config.max_strategies_in_report}")
            print(f"  time_range_days: {analysis_config.time_range_days}")
            print(f"  strategy_filter: {analysis_config.strategy_filter}")
            print(f"  enable_comparison: {analysis_config.enable_comparison}")
            return
        
        # 验证配置
        if args.validate_config:
            errors = validate_config(wechat_config, analysis_config)
            if errors:
                print("❌ 配置验证失败:")
                for error in errors:
                    print(f"  - {error}")
                sys.exit(1)
            else:
                print("✅ 配置验证通过")
                return
        
        # 创建分析器
        analyzer = FundCurveAnalyzer(wechat_config, analysis_config)
        
        # 列出策略
        if args.list_strategies:
            strategies_result = analyzer.fund_manager.get_available_strategies()
            if strategies_result['success']:
                strategies = strategies_result['strategies']
                print(f"\n📊 找到 {len(strategies)} 个策略:")
                for i, strategy in enumerate(strategies, 1):
                    print(f"  {i:2d}. {strategy['name']} (收益: {strategy['total_return']:+.2f}%)")
            else:
                print(f"❌ 获取策略列表失败: {strategies_result.get('message', '未知错误')}")
            return
        
        # 根据模式执行不同操作
        print(f"\n🚀 开始执行: {args.mode} 模式")
        
        if args.mode == 'single':
            if not args.strategy:
                print("❌ 单策略模式需要指定策略名称")
                return
            success = analyzer.analyze_single_strategy(args.strategy, not args.no_send)
        elif args.mode == 'all':
            success = analyzer.analyze_all_strategies(not args.no_send)
        elif args.mode == 'alert':
            success = analyzer.check_alerts(send_notification=not args.no_send)
        elif args.mode == 'test':
            # 测试模式：发送测试消息
            test_message = f"📊 资金曲线分析器测试消息\n\n发送时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n配置来源: {'配置文件' if args.config and os.path.exists(args.config) else '内置配置'}"
            if wechat_config.enable_notification:
                success = analyzer.wechat_notifier.send_markdown_message(test_message)
                if success:
                    print("✅ 测试消息发送成功")
                else:
                    print("❌ 测试消息发送失败")
            else:
                print("📝 测试消息（通知已禁用）:")
                print(test_message)
                success = True
        
        if success:
            print(f"✅ 任务执行成功: {args.mode}")
        else:
            print(f"❌ 任务执行失败: {args.mode}")
            sys.exit(1)
            
    except Exception as e:
        logging.error(f"程序执行出错: {str(e)}")
        print(f"❌ 程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()