"""
邢不行｜策略分享会
仓位管理实盘框架

版权所有 ©️ 邢不行
微信: xbx1717

本代码仅供个人学习使用，未经授权不得复制、修改或用于商业用途。

Author: 邢不行
定时任务脚本
"""
import os
import subprocess
import sys
import time
import traceback
from datetime import datetime, timedelta
import requests
import json

# ====================================================================================================
# ** 脚本配置区域 **
# ====================================================================================================

# 运行模式配置
# - 'hourly': 每小时整点10分运行一次
# - 'five_min': 每隔5分钟运行一次
RUN_MODE = 'hourly'  # 默认每5分钟运行一次

# 企业微信webhook配置
# 不填则不发送通知
# 例如: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_KEY
WEBHOOK_URL = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=1134e7a5-e25d-4d5a-bc48-98578df7d24e'

# Python解释器路径配置
# 不填则使用当前环境的Python
# 例如: /root/anaconda3/envs/py312/bin/python
PYTHON_PATH = '/root/anaconda3/envs/py312/bin/python'

# ====================================================================================================
# ** 脚本实现区域 **
# ====================================================================================================

# 定义运行模式常量
MODE_HOURLY = "hourly"  # 每小时整点10分运行
MODE_FIVE_MIN = "five_min"  # 每隔5分钟运行一次

# 获取当前文件所在目录的绝对路径
current_dir = os.path.dirname(os.path.abspath(__file__))
startup_script = os.path.join(current_dir, "独立资金曲线分析器_企业微信版.py")

# 企业微信通知函数
def send_wechat_work_msg(content, url):
    """
    发送企业微信通知
    :param content: 通知内容
    :param url: webhook地址
    :return: None
    """
    if not url:
        print('未配置wechat_webhook_url，不发送信息', flush=True)
        return
    try:
        data = {
            "msgtype": "text",
            "text": {
                "content": content + '\n' + datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        }
        r = requests.post(url, data=json.dumps(data), timeout=10)
        print(f'调用企业微信接口返回： {r.text}', flush=True)
        print('成功发送企业微信', flush=True)
    except Exception as e:
        print(f"发送企业微信失败:{e}", flush=True)
        print(traceback.format_exc(), flush=True)

# 执行独立资金曲线分析器_企业微信版脚本
def run_startup_script(python_path=None):
    """
    执行独立资金曲线分析器_企业微信版.py脚本并返回执行状态
    :param python_path: 指定Python解释器路径，如果为None则使用当前Python解释器
    :return: 是否成功执行
    """
    try:
        # 使用指定的Python解释器或当前解释器
        python_executable = python_path if python_path else sys.executable
        
        print(f"开始执行 独立资金曲线分析器_企业微信版.py, 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", flush=True)
        print(f"使用Python解释器: {python_executable}", flush=True)

        result = subprocess.run(
            [python_executable, startup_script],
            check=True,
            capture_output=True,
            text=True
        )
        print(f"执行完成，返回码: {result.returncode}", flush=True)
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        error_msg = f"执行独立资金曲线分析器_企业微信版.py失败，错误码: {e.returncode}, 错误信息: {e.stderr}"
        print(error_msg, flush=True)
        return False, error_msg
    except Exception as e:
        error_msg = f"执行独立资金曲线分析器_企业微信版.py时出现异常: {str(e)}"
        print(error_msg, flush=True)
        print(traceback.format_exc(), flush=True)
        return False, error_msg

# 计算下一次执行时间
def get_next_run_time(mode):
    """
    根据运行模式计算下一次执行时间
    :param mode: 运行模式
    :return: 下一次执行时间
    """
    now = datetime.now()
    
    if mode == MODE_HOURLY:
        # 每小时的40分钟执行
        if now.minute < 40:
            # 如果当前分钟小于40，则在当前小时的40分执行
            next_run = now.replace(minute=40, second=0, microsecond=0)
        else:
            # 否则在下一个小时的40分执行
            next_run = (now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)).replace(minute=40)
    else:  # MODE_FIVE_MIN
        # 每隔5分钟执行
        # 计算下一个5分钟的整点时间
        minutes = now.minute
        remainder = minutes % 5
        if remainder == 0 and now.second == 0:
            minutes += 5
        else:
            minutes = minutes - remainder + 5
            
        next_run = now.replace(minute=minutes % 60, second=0, microsecond=0)
        if minutes >= 60:
            next_run = next_run + timedelta(hours=1)
            
    return next_run

def main():
    # 使用脚本内的配置
    mode = RUN_MODE
    webhook_url = WEBHOOK_URL
    python_path = PYTHON_PATH if PYTHON_PATH else None

    print(f"定时任务启动，运行模式: {mode}", flush=True)
    print(f"企业微信通知: {'已配置' if webhook_url else '未配置'}", flush=True)
    print(f"Python解释器: {python_path if python_path else '当前环境'}", flush=True)
    
    try:
        while True:
            next_run = get_next_run_time(mode)
            wait_seconds = (next_run - datetime.now()).total_seconds()
            
            if wait_seconds < 0:
                wait_seconds = 0
                
            print(f"下次执行时间: {next_run.strftime('%Y-%m-%d %H:%M:%S')}, 等待 {wait_seconds:.2f} 秒", flush=True)
            
            if wait_seconds > 0:
                time.sleep(wait_seconds)
            
            # 执行脚本
            success, message = run_startup_script(python_path)
            
            # 发送通知
            if webhook_url:
                status = "成功" if success else "失败"
                notify_message = f"独立资金曲线分析器_企业微信版.py 执行{status}\n时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                if not success:
                    notify_message += f"\n错误信息: {message}"
                send_wechat_work_msg(notify_message, webhook_url)
            
            # 如果是每5分钟执行，且执行时间过短，则等待一会儿再进入下一个循环
            # 防止同一个5分钟区间内多次执行
            if mode == MODE_FIVE_MIN:
                time.sleep(1)  # 稍微等待一下，防止立即进入下一个循环
                
    except KeyboardInterrupt:
        print("程序被手动终止", flush=True)
    except Exception as e:
        error_msg = f"运行定时任务时出现错误: {str(e)}"
        print(error_msg, flush=True)
        print(traceback.format_exc(), flush=True)
        if webhook_url:
            send_wechat_work_msg(f"定时任务发生错误: {error_msg}", webhook_url)

if __name__ == "__main__":
    main() 