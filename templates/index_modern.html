{% extends "base_modern.html" %}

{% block title %}程序控制 - 量化交易系统{% endblock %}
{% block page_title %}程序控制{% endblock %}

{% block extra_css %}
<style>
/* Google认证模态框样式 */
#googleAuthModal {
    z-index: 1060; /* 确保在其他元素之上 */
}

#googleAuthModal .modal-dialog {
    margin: 1.75rem auto;
    max-width: 500px;
}

#googleAuthModal .modal-content {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

#googleAuthModal .modal-header {
    border-bottom: 1px solid #dee2e6;
    border-radius: 15px 15px 0 0;
}

#googleAuthModal .modal-body {
    padding: 2rem;
}

#googleAuthModal .modal-footer {
    border-top: 1px solid #dee2e6;
    border-radius: 0 0 15px 15px;
}

/* 确保模态框显示时页面布局完全稳定 */
body.modal-open {
    overflow: hidden !important;
    padding-right: 0 !important; /* 防止滚动条导致的布局跳动 */
    margin-right: 0 !important;
    width: 100% !important;
}

/* 防止模态框影响页面布局 */
.modal {
    padding-right: 0 !important;
}

/* 强制页面布局稳定 - 防止模态框显示时的布局跳动 */
html {
    overflow-x: hidden !important;
    scroll-behavior: auto !important;
}

body {
    transition: none !important; /* 禁用可能导致布局跳动的过渡效果 */
    overflow-x: hidden !important;
}

/* 防止Bootstrap模态框影响页面布局 */
body.modal-open {
    padding-right: 0 !important;
    margin-right: 0 !important;
    overflow: hidden !important;
}

/* 确保页面容器在模态框显示时保持稳定 */
.container-fluid,
.main-content,
.sidebar {
    padding-right: 0 !important;
    margin-right: 0 !important;
    transition: none !important;
    box-sizing: border-box !important;
}

/* 防止模态框影响滚动条 */
.modal-open .sidebar,
.modal-open .main-content,
.modal-open .container-fluid {
    padding-right: 0 !important;
    margin-right: 0 !important;
    width: auto !important;
}

/* 强制所有元素使用border-box */
*, *::before, *::after {
    box-sizing: border-box !important;
}

/* 日志样式优化 */
.log-viewer {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
}

.log-entry {
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.log-entry:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.log-message {
    color: #212529 !important; /* 深色文字，确保清晰可读 */
    font-weight: 500;
    margin-left: 8px;
}

/* 不同级别日志的消息颜色优化 */
.log-entry .text-danger + .log-message {
    color: #721c24 !important; /* 错误日志消息使用深红色 */
}

.log-entry .text-warning + .log-message {
    color: #856404 !important; /* 警告日志消息使用深黄色 */
}

.log-entry .text-info + .log-message {
    color: #055160 !important; /* 信息日志消息使用深蓝色 */
}

.log-entry .text-success + .log-message {
    color: #0f5132 !important; /* 成功日志消息使用深绿色 */
}

.log-entry .text-muted + .log-message {
    color: #495057 !important; /* 调试日志消息使用中等灰色 */
}

/* 时间戳样式优化 */
.log-entry .text-muted.small {
    color: #6c757d !important;
    font-size: 11px;
    font-weight: 400;
}

/* 防止模态框背景影响页面 */
.modal-backdrop {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    z-index: 1040 !important;
    width: 100vw !important;
    height: 100vh !important;
}

/* 确保模态框本身不影响布局 */
.modal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    z-index: 1050 !important;
    width: 100% !important;
    height: 100% !important;
    overflow-x: hidden !important;
    overflow-y: auto !important;
}

/* 模态框背景 */
.modal-backdrop {
    background-color: rgba(0, 0, 0, 0.6);
}

/* 认证方式选择标签 */
.auth-method-tabs {
    display: flex;
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 1rem;
}

.auth-method-tab {
    flex: 1;
    padding: 0.75rem 1rem;
    text-align: center;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
    color: #6c757d;
}

.auth-method-tab:hover {
    background-color: #f8f9fa;
    color: #495057;
}

.auth-method-tab.active {
    color: #0d6efd;
    border-bottom-color: #0d6efd;
    background-color: #f8f9fa;
}

.auth-method-content {
    display: none;
}

.auth-method-content.active {
    display: block;
}

/* 验证码输入框样式 */
.verification-code-input {
    text-align: center;
    font-size: 1.2rem;
    letter-spacing: 0.5rem;
    font-weight: 600;
}

/* 错误信息样式 */
.modal-error {
    margin-bottom: 1rem;
}

.modal-error .error-message {
    font-weight: 500;
}

/* 系统监控样式 */
.resource-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 12px;
    padding: 1.25rem;
    height: 100%;
    transition: all 0.3s ease;
}

.resource-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.resource-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    color: #495057;
}

.resource-header i {
    font-size: 1.2rem;
    margin-right: 0.5rem;
    color: #6c757d;
}

.resource-title {
    font-weight: 600;
    font-size: 0.9rem;
}

.resource-content {
    text-align: center;
}

.resource-value {
    margin-bottom: 0.75rem;
}

.value-number {
    font-size: 2rem;
    font-weight: 700;
    color: #495057;
}

.value-unit {
    font-size: 1rem;
    color: #6c757d;
    margin-left: 0.25rem;
}

.value-text {
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
}

.resource-progress {
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    margin-bottom: 0.75rem;
}

.resource-progress .progress-bar {
    background: linear-gradient(90deg, #28a745 0%, #20c997 50%, #17a2b8 100%);
    border-radius: 4px;
    transition: width 0.6s ease;
}

.resource-progress .progress-bar.bg-warning {
    background: linear-gradient(90deg, #ffc107 0%, #fd7e14 100%);
}

.resource-progress .progress-bar.bg-danger {
    background: linear-gradient(90deg, #dc3545 0%, #e83e8c 100%);
}

.resource-details {
    font-size: 0.8rem;
    color: #6c757d;
}

/* 紧凑版系统监控样式 */
.resource-card-compact {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
}

.resource-header-compact {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.resource-title-compact {
    font-weight: 600;
    font-size: 0.85rem;
    flex: 1;
}

.value-number-compact {
    font-size: 1.2rem;
    font-weight: 700;
    color: #495057;
}

.value-unit-compact {
    font-size: 0.8rem;
    color: #6c757d;
    margin-left: 0.2rem;
}

.value-text-compact {
    font-size: 0.9rem;
    font-weight: 600;
    color: #495057;
}

.resource-progress-compact {
    height: 6px;
    background-color: #e9ecef;
    border-radius: 3px;
    margin-bottom: 0.5rem;
}

.resource-progress-compact .progress-bar {
    background: linear-gradient(90deg, #28a745 0%, #20c997 50%, #17a2b8 100%);
    border-radius: 3px;
    transition: width 0.6s ease;
}

.resource-progress-compact .progress-bar.bg-warning {
    background: linear-gradient(90deg, #ffc107 0%, #fd7e14 100%);
}

.resource-progress-compact .progress-bar.bg-danger {
    background: linear-gradient(90deg, #dc3545 0%, #e83e8c 100%);
}

.resource-details-compact {
    font-size: 0.75rem;
    color: #6c757d;
}
</style>
{% endblock %}

{% block content %}

<!-- 主要控制区域 -->
<div class="row g-4 mb-4">
    <!-- 程序控制卡片 -->
    <div class="col-lg-6">
        <div class="modern-card">
            <div class="modern-card-header">
                <h5 class="mb-0">
                    <i class="bi bi-play-circle"></i>
                    程序控制
                </h5>
            </div>
            <div class="modern-card-body">
                <div class="mb-4">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span class="fw-semibold">运行状态:</span>
                        <span id="status-badge" class="status-badge status-danger">
                            <i class="bi bi-stop-fill"></i>
                            {{ status.status_text if status else '已停止' }}
                        </span>
                    </div>

                    <div id="process-info" class="mb-3" style="display: none;">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="fw-semibold">进程ID:</span>
                            <span id="process-pid" class="text-muted">-</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="fw-semibold">启动时间:</span>
                            <span id="start-time" class="text-muted">-</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="fw-semibold">内存使用:</span>
                            <span id="memory-usage" class="text-muted">-</span>
                        </div>
                    </div>
                </div>

                <div class="d-grid gap-2">
                    <button id="start-btn" class="modern-btn modern-btn-success" onclick="startTrading()">
                        <i class="bi bi-play-fill"></i>
                        启动交易程序
                    </button>

                    <!-- 调度调试按钮组 -->
                    <div class="row g-2" {% if not global_config.get('is_debug') %}style="display: none;"{% endif %}>
                        <div class="col-4">
                            <button id="schedule-debug-btn" class="modern-btn modern-btn-info w-100" onclick="startScheduleDebug()"
                                    {% if not global_config.get('is_debug') %}disabled title="仅在调试模式下可用"{% endif %}>
                                <i class="bi bi-clock-fill"></i>
                                调度调试
                            </button>
                        </div>
                        <div class="col-4">
                            <button id="edit-script-btn" class="modern-btn modern-btn-outline w-100" onclick="toggleScriptEditor(true)"
                                    {% if not global_config.get('is_debug') %}disabled title="仅在调试模式下可用"{% endif %}>
                                <i class="bi bi-pencil-square"></i>
                                编辑调度脚本
                            </button>
                        </div>
                        <div class="col-4">
                            <button id="view-script-btn" class="modern-btn modern-btn-outline w-100" onclick="toggleScriptEditor(false)"
                                    {% if not global_config.get('is_debug') %}disabled title="仅在调试模式下可用"{% endif %}>
                                <i class="bi bi-eye"></i>
                                查看脚本
                            </button>
                        </div>
                    </div>



                    <button id="stop-btn" class="modern-btn modern-btn-danger" onclick="stopTrading()" style="display: none;">
                        <i class="bi bi-stop-fill"></i>
                        停止交易程序
                    </button>
                </div>

                <div id="operation-status" class="mt-3" style="display: none;">
                    <div class="alert alert-info border-0 rounded-3">
                        <i class="bi bi-info-circle"></i>
                        <span id="operation-message">操作进行中...</span>
                    </div>
                </div>
            </div>
        </div>


    </div>



    <!-- 调试模式控制卡片 -->
    <div class="col-lg-6">
        <div class="modern-card">
            <div class="modern-card-header">
                <h5 class="mb-0">
                    <i class="bi bi-bug"></i>
                    调试模式控制
                </h5>
            </div>
            <div class="modern-card-body">
                <div class="mb-4">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span class="fw-semibold">当前模式:</span>
                        <span id="debug-mode-badge" class="status-badge status-warning">
                            <i class="bi bi-bug-fill"></i>
                            {{ '调试模式' if global_config.get('is_debug') else '生产模式' }}
                        </span>
                    </div>

                    <div class="mb-3">
                        <p class="text-muted small mb-0">
                            <i class="bi bi-info-circle"></i>
                            调试模式下会输出详细日志信息，生产模式下仅输出关键信息
                        </p>
                    </div>
                </div>

                <div class="d-grid gap-2">
                    <button id="toggle-debug-btn" class="modern-btn modern-btn-outline" onclick="toggleDebugMode()">
                        <i class="bi bi-arrow-repeat"></i>
                        切换到{{ '生产模式' if global_config.get('is_debug') else '调试模式' }}
                    </button>
                </div>

                <div id="debug-operation-status" class="mt-3" style="display: none;">
                    <div class="alert alert-info border-0 rounded-3">
                        <i class="bi bi-info-circle"></i>
                        <span id="debug-operation-message">模式切换中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 调度脚本备份管理和系统监控 -->
<div class="row g-4 mb-4">
    <!-- 调度脚本备份管理 -->
    <div class="col-lg-6">
        <div class="modern-card h-100" {% if not global_config.get('is_debug') %}style="display: none;"{% endif %}>
            <div class="modern-card-header">
                <h5 class="mb-0">
                    <i class="bi bi-archive"></i>
                    调度脚本备份管理
                </h5>
            </div>
            <div class="modern-card-body">
                <div class="mb-3">
                    <p class="text-muted small mb-3">
                        <i class="bi bi-info-circle"></i>
                        自动保存最近3个备份版本，支持查看、恢复和删除操作
                    </p>

                    <div class="d-flex gap-2 mb-3">
                        <button class="btn btn-outline-primary btn-sm" onclick="loadBackupList()">
                            <i class="bi bi-arrow-clockwise"></i>
                            刷新
                        </button>
                        <button class="btn btn-outline-warning btn-sm" onclick="cleanupOldBackups()">
                            <i class="bi bi-trash3"></i>
                            清理旧备份
                        </button>
                    </div>
                </div>

                <div id="backup-list-container">
                    <div class="text-center text-muted py-3">
                        <i class="bi bi-hourglass-split"></i>
                        正在加载备份列表...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 系统监控 -->
    <div class="col-lg-6">
        <div class="modern-card h-100">
            <div class="modern-card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-speedometer2"></i>
                    系统监控
                </h5>
                <button class="btn btn-sm btn-outline-primary" onclick="refreshSystemResources()">
                    <i class="bi bi-arrow-clockwise"></i>
                    刷新
                </button>
            </div>
            <div class="modern-card-body">
                <div class="row g-2">
                    <!-- CPU使用情况 -->
                    <div class="col-12">
                        <div class="resource-card-compact">
                            <div class="resource-header-compact">
                                <i class="bi bi-cpu"></i>
                                <span class="resource-title-compact">CPU使用情况</span>
                                <span id="cpu-percent" class="value-number-compact">-</span>
                                <span class="value-unit-compact">%</span>
                            </div>
                            <div class="progress resource-progress-compact">
                                <div id="cpu-progress" class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div class="resource-details-compact">
                                <small class="text-muted">
                                    核心数: <span id="cpu-cores">-</span> (逻辑: <span id="cpu-logical">-</span>)
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- 内存使用情况 -->
                    <div class="col-12">
                        <div class="resource-card-compact">
                            <div class="resource-header-compact">
                                <i class="bi bi-memory"></i>
                                <span class="resource-title-compact">内存使用情况</span>
                                <span id="memory-percent" class="value-number-compact">-</span>
                                <span class="value-unit-compact">%</span>
                            </div>
                            <div class="progress resource-progress-compact">
                                <div id="memory-progress" class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div class="resource-details-compact">
                                <small class="text-muted">
                                    已用: <span id="memory-used">-</span>GB / 总计: <span id="memory-total">-</span>GB
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- 虚拟内存使用情况 -->
                    <div class="col-12">
                        <div class="resource-card-compact">
                            <div class="resource-header-compact">
                                <i class="bi bi-hdd-stack"></i>
                                <span class="resource-title-compact">虚拟内存</span>
                                <span id="swap-percent" class="value-number-compact" style="display: none;">-</span>
                                <span class="value-unit-compact" style="display: none;">%</span>
                                <span id="swap-status" class="value-text-compact">检查中...</span>
                            </div>
                            <div id="swap-details" style="display: none;">
                                <div class="progress resource-progress-compact">
                                    <div id="swap-progress" class="progress-bar" role="progressbar" style="width: 0%"></div>
                                </div>
                                <div class="resource-details-compact">
                                    <small class="text-muted">
                                        已用: <span id="swap-used">-</span>GB / 总计: <span id="swap-total">-</span>GB
                                    </small>
                                </div>
                            </div>
                            <div id="swap-unavailable" style="display: none;">
                                <div class="text-center text-muted py-1">
                                    <small>未配置虚拟内存</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 日志查看区域 -->
<div class="row g-4">
    <div class="col-12">
        <div class="modern-card">
            <div class="modern-card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-file-text"></i>
                    实时日志
                </h5>
                <div class="d-flex gap-2 flex-wrap">
                    <select id="log-level-filter" class="form-select form-select-sm" style="width: auto;" onchange="filterLogs()">
                        <option value="all">全部级别</option>
                        <option value="error">错误</option>
                        <option value="warning">警告</option>
                        <option value="info">信息</option>
                        <option value="debug">调试</option>
                    </select>

                    <div class="btn-group" role="group">
                        <input type="radio" class="btn-check" name="log-mode" id="log-mode-normal" value="normal" checked onchange="changeLogMode()">
                        <label class="btn btn-outline-secondary btn-sm" for="log-mode-normal">
                            <i class="bi bi-filter"></i> 智能
                        </label>

                        <input type="radio" class="btn-check" name="log-mode" id="log-mode-verbose" value="verbose" onchange="changeLogMode()">
                        <label class="btn btn-outline-info btn-sm" for="log-mode-verbose">
                            <i class="bi bi-list-ul"></i> 详细
                        </label>

                        <input type="radio" class="btn-check" name="log-mode" id="log-mode-silent" value="silent" onchange="changeLogMode()">
                        <label class="btn btn-outline-warning btn-sm" for="log-mode-silent">
                            <i class="bi bi-volume-mute"></i> 静默
                        </label>
                    </div>

                    <button class="btn btn-sm btn-outline-primary" onclick="refreshLogs()">
                        <i class="bi bi-arrow-clockwise"></i>
                        刷新
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="downloadLogs()">
                        <i class="bi bi-download"></i>
                        下载
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="clearConsoleLogs()" title="清空控制台显示的日志">
                        <i class="bi bi-eraser"></i>
                        清空
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="clearLogFiles()" title="清理日志文件，重置为0">
                        <i class="bi bi-trash"></i>
                        清理
                    </button>
                </div>
            </div>
            <div class="modern-card-body">
                <div class="mb-3">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-search"></i>
                        </span>
                        <input type="text" id="log-search" class="form-control" placeholder="搜索日志内容..." onkeyup="searchLogs()">
                        <button class="btn btn-outline-secondary" onclick="clearSearch()">
                            <i class="bi bi-x"></i>
                        </button>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">
                            <i class="bi bi-info-circle"></i>
                            <span id="log-mode-description">智能模式：自动过滤重复和无关日志，突出显示重要信息</span>
                        </small>
                    </div>
                </div>

                <div id="log-container" class="log-viewer" style="height: 400px; overflow-y: auto; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 0.375rem; padding: 1rem;">
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-file-text"></i>
                        <p class="mb-0 mt-2">暂无日志数据</p>
                        <small>程序启动后将显示实时日志</small>
                    </div>
                </div>

                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="auto-scroll" checked>
                        <label class="form-check-label" for="auto-scroll">
                            自动滚动到底部
                        </label>
                    </div>
                    <div class="text-muted small">
                        <span id="log-count">0</span> 条日志
                        <span class="mx-2">|</span>
                        最后更新: <span id="log-last-update">-</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- 传递服务器端数据到JavaScript -->
<script type="application/json" id="server-data">
{{ {
    'status': status
} | tojson | safe }}
</script>

<script>
    let logUpdateInterval;
    let logs = [];
    let filteredLogs = [];
    let googleAuthInProgress = false; // 防止重复处理Google认证

    // 页面加载时初始化
    $(document).ready(function() {
        // 初始化系统状态
        try {
            const serverData = JSON.parse(document.getElementById('server-data').textContent);
            if (serverData && serverData.status) {
                updateSystemStatus(serverData.status);
            }
        } catch (e) {
            console.log('初始化状态数据时出错:', e);
        }

        // 初始化日志查看
        initLogViewer();

        // 初始化日志设置
        initLogSettings();

        // 初始化WebSocket连接
        initWebSocket();

        // 每5秒更新日志（作为备用）
        logUpdateInterval = setInterval(updateLogs, 5000);

        // 初始化系统资源监控
        initSystemResourcesMonitoring();

        // 初始化认证系统（如果存在）
        if (typeof initializeAuth === 'function') {
            initializeAuth();
            window.authInitialized = true;
        }

        // 页面加载时立即检查认证状态
        setTimeout(function() {
            if (typeof checkSessionStatus === 'function') {
                checkSessionStatus();
            }
        }, 1000);
    });

    // 初始化日志查看器
    function initLogViewer() {
        // 模拟一些初始日志
        logs = [
            { level: 'info', time: new Date().toLocaleString(), message: '系统初始化完成', count: 1 },
            { level: 'info', time: new Date().toLocaleString(), message: '等待程序启动...', count: 1 }
        ];
        filteredLogs = [...logs];
        renderLogs();
    }

    // 初始化日志设置
    function initLogSettings() {
        $.ajax({
            url: '/api/log-settings',
            method: 'GET',
            success: function(response) {
                if (response.success && response.settings) {
                    updateLogModeDisplay(response.settings);
                }
            },
            error: function() {
                // 使用默认设置
            }
        });
    }

    // 初始化WebSocket连接
    function initWebSocket() {
        const socket = io();

        // 监听状态更新
        socket.on('status_update', function(data) {
            updateSystemStatus(data);
        });

        // 监听日志更新
        socket.on('log_update', function(data) {
            // 使用服务器端处理的日志数据
            const newLog = {
                level: data.level || 'info',
                time: data.timestamp,
                message: data.message,
                count: data.count || 1
            };

            logs.push(newLog);

            // 只保留最近200条日志
            if (logs.length > 200) {
                logs = logs.slice(-200);
            }

            filterLogs();
        });

        // 监听日志设置更新
        socket.on('log_settings_updated', function(data) {
            updateLogModeDisplay(data);
        });

        // 连接状态处理
        socket.on('connect', function() {
            logs.push({
                level: 'info',
                time: new Date().toLocaleString(),
                message: 'WebSocket连接已建立'
            });
            filterLogs();
        });

        socket.on('disconnect', function() {
            logs.push({
                level: 'warning',
                time: new Date().toLocaleString(),
                message: 'WebSocket连接已断开，正在尝试重连...'
            });
            filterLogs();
        });
    }

    // 渲染日志
    function renderLogs() {
        const container = document.getElementById('log-container');
        const autoScroll = document.getElementById('auto-scroll').checked;

        if (filteredLogs.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="bi bi-file-text"></i>
                    <p class="mb-0 mt-2">暂无日志数据</p>
                    <small>程序启动后将显示实时日志</small>
                </div>
            `;
            return;
        }

        let html = '';
        filteredLogs.forEach(log => {
            const levelClass = {
                'error': 'text-danger',
                'warning': 'text-warning',
                'info': 'text-info',
                'debug': 'text-muted',
                'success': 'text-success'
            }[log.level] || 'text-dark';

            const levelIcon = {
                'error': 'bi-x-circle-fill',
                'warning': 'bi-exclamation-triangle-fill',
                'info': 'bi-info-circle-fill',
                'debug': 'bi-bug-fill',
                'success': 'bi-check-circle-fill'
            }[log.level] || 'bi-circle-fill';

            // 显示重复计数
            const countBadge = log.count && log.count > 1 ?
                `<span class="badge bg-secondary ms-1">${log.count}x</span>` : '';

            html += `
                <div class="log-entry mb-2">
                    <span class="text-muted small">[${log.time}]</span>
                    <span class="${levelClass}">
                        <i class="bi ${levelIcon}"></i>
                        [${log.level.toUpperCase()}]
                    </span>
                    <span class="log-message">${log.message}</span>
                    ${countBadge}
                </div>
            `;
        });

        container.innerHTML = html;

        // 更新日志计数
        document.getElementById('log-count').textContent = filteredLogs.length;
        document.getElementById('log-last-update').textContent = new Date().toLocaleString();

        // 自动滚动到底部
        if (autoScroll) {
            container.scrollTop = container.scrollHeight;
        }
    }
    
    // 切换日志模式
    function changeLogMode() {
        const mode = document.querySelector('input[name="log-mode"]:checked').value;
        const verbose_mode = mode === 'verbose';
        const silent_mode = mode === 'silent';

        // 立即更新描述
        updateModeDescription(mode);

        $.ajax({
            url: '/api/log-settings',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                verbose_mode: verbose_mode,
                silent_mode: silent_mode
            }),
            success: function(response) {
                if (response.success) {
                    showMessage(`已切换到${getModeDisplayName(mode)}模式`, 'success');
                    // 重新加载日志
                    refreshLogs();
                } else {
                    showMessage('切换日志模式失败: ' + response.message, 'danger');
                }
            },
            error: function() {
                showMessage('切换日志模式失败，请检查网络连接', 'danger');
            }
        });
    }

    // 获取模式显示名称
    function getModeDisplayName(mode) {
        const names = {
            'normal': '智能过滤',
            'verbose': '详细显示',
            'silent': '静默'
        };
        return names[mode] || mode;
    }

    // 更新日志模式显示
    function updateLogModeDisplay(data) {
        let mode = 'normal';
        if (data.verbose_mode) {
            document.getElementById('log-mode-verbose').checked = true;
            mode = 'verbose';
        } else if (data.silent_mode) {
            document.getElementById('log-mode-silent').checked = true;
            mode = 'silent';
        } else {
            document.getElementById('log-mode-normal').checked = true;
            mode = 'normal';
        }

        // 更新描述文字
        updateModeDescription(mode);
    }

    // 更新模式描述
    function updateModeDescription(mode) {
        const descriptions = {
            'normal': '智能模式：自动过滤重复和无关日志，突出显示重要信息',
            'verbose': '详细模式：显示所有日志信息，包括调试和系统内部消息',
            'silent': '静默模式：仅显示错误和警告信息，隐藏常规操作日志'
        };

        const descElement = document.getElementById('log-mode-description');
        if (descElement) {
            descElement.textContent = descriptions[mode] || descriptions['normal'];
        }
    }

    // 恢复日志更新定时器
    function resumeLogUpdates() {
        if (!logUpdateInterval) {
            console.log('恢复日志更新定时器');
            logUpdateInterval = setInterval(updateLogs, 5000);
        }
        // 重置Google认证状态
        googleAuthInProgress = false;
    }

    // 暂停日志更新定时器
    function pauseLogUpdates() {
        if (logUpdateInterval) {
            console.log('暂停日志更新定时器');
            clearInterval(logUpdateInterval);
            logUpdateInterval = null;
        }
    }

    // 更新日志
    function updateLogs() {
        const mode = document.querySelector('input[name="log-mode"]:checked').value;
        const params = new URLSearchParams();

        if (mode === 'verbose') {
            params.append('verbose', 'true');
        } else if (mode === 'silent') {
            params.append('silent', 'true');
        }

        $.ajax({
            url: '/api/logs?' + params.toString(),
            method: 'GET',
            success: function(response) {
                if (response.success && response.logs) {
                    // 合并新日志，避免重复
                    const newLogs = response.logs.filter(newLog =>
                        !logs.some(existingLog =>
                            existingLog.time === newLog.time &&
                            existingLog.message === newLog.message
                        )
                    );

                    logs = logs.concat(newLogs);

                    // 只保留最近100条日志
                    if (logs.length > 100) {
                        logs = logs.slice(-100);
                    }

                    filterLogs();
                }
            },
            error: function(xhr, status, error) {
                if (xhr.status === 401) {
                    // 会话过期，跳转到登录页面
                    console.log('会话已过期，跳转登录页面');
                    window.location.href = '/auth/login';
                    return;
                } else if (xhr.status === 403) {
                    // Google认证超时，显示认证模态框
                    console.log('收到403错误，响应内容:', xhr.responseJSON);

                    if (xhr.responseJSON && xhr.responseJSON.google_auth_required) {
                        console.log('Google认证超时，暂停日志更新并显示认证模态框');

                        // 防止重复处理
                        if (googleAuthInProgress) {
                            console.log('Google认证已在进行中，跳过重复处理');
                            return;
                        }
                        googleAuthInProgress = true;

                        // 暂停日志更新定时器，防止重复请求
                        pauseLogUpdates();

                        // 直接调用showGoogleAuthModal，避免依赖全局错误处理
                        console.log('直接调用showGoogleAuthModal');
                        if (typeof showGoogleAuthModal === 'function') {
                            // 保存原始请求信息
                            window.pendingRequest = {
                                url: '/api/logs?' + params.toString(),
                                method: 'GET',
                                data: null
                            };
                            showGoogleAuthModal();
                        } else if (typeof handleGoogleAuthRequired === 'function') {
                            handleGoogleAuthRequired({
                                url: '/api/logs?' + params.toString(),
                                method: 'GET',
                                data: null
                            });
                        }
                    } else {
                        console.log('403错误但没有google_auth_required字段');
                    }
                    return;
                }

                // 如果API调用失败，添加模拟日志
                if (Math.random() > 0.8) {
                    const levels = ['info', 'warning', 'error', 'debug'];
                    const messages = [
                        '数据更新完成',
                        '检查账户状态',
                        '网络连接正常',
                        '处理交易信号',
                        '保存配置文件',
                        '清理临时文件'
                    ];

                    const newLog = {
                        level: levels[Math.floor(Math.random() * levels.length)],
                        time: new Date().toLocaleString(),
                        message: messages[Math.floor(Math.random() * messages.length)]
                    };

                    logs.push(newLog);

                    // 只保留最近100条日志
                    if (logs.length > 100) {
                        logs = logs.slice(-100);
                    }

                    filterLogs();
                }
            }
        });
    }

    // 过滤日志
    function filterLogs() {
        const levelFilter = document.getElementById('log-level-filter').value;
        const searchText = document.getElementById('log-search').value.toLowerCase();

        filteredLogs = logs.filter(log => {
            const levelMatch = levelFilter === 'all' || log.level === levelFilter;
            const textMatch = searchText === '' || log.message.toLowerCase().includes(searchText);
            return levelMatch && textMatch;
        });

        renderLogs();
    }

    // 搜索日志
    function searchLogs() {
        filterLogs();
    }

    // 清除搜索
    function clearSearch() {
        document.getElementById('log-search').value = '';
        filterLogs();
    }

    // 下载日志
    function downloadLogs() {
        const logText = filteredLogs.map(log =>
            `[${log.time}] [${log.level.toUpperCase()}] ${log.message}`
        ).join('\n');

        const blob = new Blob([logText], { type: 'text/plain' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `trading_logs_${new Date().toISOString().slice(0, 10)}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

        showMessage('日志文件下载完成', 'success');
    }

    // 清空控制台日志
    function clearConsoleLogs() {
        if (!confirm('确定要清空控制台显示的日志吗？\n\n这将清空当前页面显示的所有日志，但不会删除日志文件。')) {
            return;
        }

        // 清空日志数组
        logs = [];
        filteredLogs = [];

        // 重新渲染日志容器
        renderLogs();

        showMessage('控制台日志已清空', 'success');
    }

    // 清理日志文件
    function clearLogFiles() {
        // 显示加载状态
        showOperationStatus('正在获取日志文件信息...', 'info');

        // 先获取日志文件信息
        $.ajax({
            url: '/api/log-files-info',
            method: 'GET',
            success: function(response) {
                hideOperationStatus();

                if (response.success) {
                    let confirmMessage = '确定要清理日志文件吗？\n\n';

                    if (response.files && response.files.length > 0) {
                        confirmMessage += '当前日志文件占用空间：\n';
                        response.files.forEach(file => {
                            const sizeMB = (file.size / 1024 / 1024).toFixed(2);
                            confirmMessage += `• ${file.name}: ${sizeMB} MB\n`;
                        });
                        confirmMessage += `\n总占用空间: ${(response.total_size / 1024 / 1024).toFixed(2)} MB\n\n`;
                        confirmMessage += '清理后所有日志文件将重置为0字节。\n此操作不可恢复！';
                    } else {
                        confirmMessage += '没有找到日志文件，无需清理。';
                        alert(confirmMessage);
                        return;
                    }

                    if (!confirm(confirmMessage)) {
                        return;
                    }

                    // 执行清理
                    performLogClear();
                } else {
                    showMessage('获取日志文件信息失败: ' + response.message, 'danger');
                }
            },
            error: function(xhr) {
                hideOperationStatus();
                let errorMsg = '获取日志文件信息失败，请检查网络连接';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMsg = '获取日志文件信息失败: ' + xhr.responseJSON.message;
                }
                showMessage(errorMsg, 'danger');
            }
        });
    }

    // 执行日志清理
    function performLogClear() {
        // 显示加载状态
        showOperationStatus('正在清理日志文件...', 'info');

        $.ajax({
            url: '/api/clear-logs',
            method: 'POST',
            success: function(response) {
                if (response.success) {
                    // 显示清理结果
                    let message = '日志文件清理完成！\n\n';
                    if (response.cleared_files && response.cleared_files.length > 0) {
                        message += '已清理的文件：\n';
                        response.cleared_files.forEach(file => {
                            const sizeMB = (file.size_before / 1024 / 1024).toFixed(2);
                            message += `• ${file.name}: ${sizeMB} MB → 0 MB\n`;
                        });
                        message += `\n总共释放空间: ${(response.total_size_freed / 1024 / 1024).toFixed(2)} MB`;
                    } else {
                        message += '没有找到需要清理的日志文件。';
                    }

                    alert(message);
                    showMessage('日志文件清理完成', 'success');

                    // 清空控制台显示的日志
                    logs = [];
                    filteredLogs = [];
                    renderLogs();
                } else {
                    showMessage('清理日志文件失败: ' + response.message, 'danger');
                }
                hideOperationStatus();
            },
            error: function(xhr) {
                let errorMsg = '清理日志文件失败，请检查网络连接';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMsg = '清理日志文件失败: ' + xhr.responseJSON.message;
                }
                showMessage(errorMsg, 'danger');
                hideOperationStatus();
            }
        });
    }

    // 启动交易程序
    function startTrading() {
        showOperationStatus('正在启动交易程序...', 'info');
        disableButtons();

        // 添加日志
        logs.push({
            level: 'info',
            time: new Date().toLocaleString(),
            message: '正在启动交易程序...'
        });
        filterLogs();

        $.ajax({
            url: '/api/start',
            method: 'POST',
            success: function(response) {
                hideOperationStatus();
                if (response.success) {
                    showMessage(response.message, 'success');
                    logs.push({
                        level: 'info',
                        time: new Date().toLocaleString(),
                        message: '交易程序启动成功'
                    });
                    // 立即获取并更新状态，确保界面同步
                    refreshStatusSilent();
                } else {
                    showMessage(response.message, 'danger');
                    logs.push({
                        level: 'error',
                        time: new Date().toLocaleString(),
                        message: '交易程序启动失败: ' + response.message
                    });
                }
                filterLogs();
                enableButtons();
            },
            error: function() {
                hideOperationStatus();
                showMessage('启动请求失败，请检查网络连接', 'danger');
                logs.push({
                    level: 'error',
                    time: new Date().toLocaleString(),
                    message: '启动请求失败，网络连接异常'
                });
                filterLogs();
                enableButtons();
            }
        });
    }
    
    // 停止交易程序
    function stopTrading() {
        if (!confirm('确定要停止交易程序吗？这将中断所有正在进行的交易操作。')) {
            return;
        }

        showOperationStatus('正在停止交易程序...', 'warning');
        disableButtons();

        // 添加日志
        logs.push({
            level: 'warning',
            time: new Date().toLocaleString(),
            message: '正在停止交易程序...'
        });
        filterLogs();

        $.ajax({
            url: '/api/stop',
            method: 'POST',
            success: function(response) {
                hideOperationStatus();
                if (response.success) {
                    showMessage(response.message, 'success');
                    logs.push({
                        level: 'info',
                        time: new Date().toLocaleString(),
                        message: '交易程序已停止'
                    });
                    // 立即获取并更新状态，确保界面同步
                    refreshStatusSilent();
                } else {
                    showMessage(response.message, 'danger');
                    logs.push({
                        level: 'error',
                        time: new Date().toLocaleString(),
                        message: '停止交易程序失败: ' + response.message
                    });
                }
                filterLogs();
                enableButtons();
            },
            error: function() {
                hideOperationStatus();
                showMessage('停止请求失败，请检查网络连接', 'danger');
                logs.push({
                    level: 'error',
                    time: new Date().toLocaleString(),
                    message: '停止请求失败，网络连接异常'
                });
                filterLogs();
                enableButtons();
            }
        });
    }

    // 切换调试模式
    function toggleDebugMode() {
        const currentMode = document.getElementById('debug-mode-badge').textContent.includes('调试模式');
        const newMode = !currentMode;
        const confirmMessage = `确定要切换到${newMode ? '调试模式' : '生产模式'}吗？\n\n${newMode ? '调试模式会输出详细的日志信息，便于问题排查。' : '生产模式仅输出关键信息，提高系统性能。'}`;

        if (!confirm(confirmMessage)) {
            return;
        }

        showDebugOperationStatus('正在切换模式...', 'info');

        // 添加日志
        logs.push({
            level: 'info',
            time: new Date().toLocaleString(),
            message: `正在切换到${newMode ? '调试模式' : '生产模式'}...`
        });
        filterLogs();

        $.ajax({
            url: '/api/toggle-debug',
            method: 'POST',
            data: { debug_mode: newMode },
            success: function(response) {
                hideDebugOperationStatus();
                if (response.success) {
                    showMessage(response.message, 'success');
                    updateDebugModeDisplay(newMode);
                    logs.push({
                        level: 'info',
                        time: new Date().toLocaleString(),
                        message: `已切换到${newMode ? '调试模式' : '生产模式'}`
                    });
                } else {
                    showMessage(response.message, 'danger');
                    logs.push({
                        level: 'error',
                        time: new Date().toLocaleString(),
                        message: '模式切换失败: ' + response.message
                    });
                }
                filterLogs();
            },
            error: function() {
                hideDebugOperationStatus();
                showMessage('模式切换请求失败，请检查网络连接', 'danger');
                logs.push({
                    level: 'error',
                    time: new Date().toLocaleString(),
                    message: '模式切换请求失败，网络连接异常'
                });
                filterLogs();
            }
        });
    }

    // 更新调试模式显示
    function updateDebugModeDisplay(isDebug) {
        const badge = document.getElementById('debug-mode-badge');
        const button = document.getElementById('toggle-debug-btn');
        const scheduleDebugBtn = document.getElementById('schedule-debug-btn');
        const editScriptBtn = document.getElementById('edit-script-btn');
        const viewScriptBtn = document.getElementById('view-script-btn');
        const scheduleButtonGroup = scheduleDebugBtn.closest('.row');

        if (isDebug) {
            badge.className = 'status-badge status-warning';
            badge.innerHTML = '<i class="bi bi-bug-fill"></i> 调试模式';
            button.innerHTML = '<i class="bi bi-arrow-repeat"></i> 切换到生产模式';
            scheduleDebugBtn.disabled = false;
            scheduleDebugBtn.title = '';
            editScriptBtn.disabled = false;
            editScriptBtn.title = '';
            viewScriptBtn.disabled = false;
            viewScriptBtn.title = '';
            scheduleButtonGroup.style.display = 'flex';
        } else {
            badge.className = 'status-badge status-success';
            badge.innerHTML = '<i class="bi bi-shield-check-fill"></i> 生产模式';
            button.innerHTML = '<i class="bi bi-arrow-repeat"></i> 切换到调试模式';
            scheduleDebugBtn.disabled = true;
            scheduleDebugBtn.title = '仅在调试模式下可用';
            editScriptBtn.disabled = true;
            editScriptBtn.title = '仅在调试模式下可用';
            viewScriptBtn.disabled = true;
            viewScriptBtn.title = '仅在调试模式下可用';
            scheduleButtonGroup.style.display = 'none';
            // 隐藏脚本编辑模态框
            hideScriptEditor();
        }
    }

    // 切换脚本编辑器显示状态
    function toggleScriptEditor(isEditMode) {
        const modalTitle = document.getElementById('modal-script-title');
        const modalControls = document.getElementById('modal-script-controls');
        const modalFooterControls = document.getElementById('modal-footer-controls');
        const scriptEditor = document.getElementById('schedule-script-editor');
        const scriptViewer = document.getElementById('schedule-script-viewer');
        const helpText = document.getElementById('modal-script-help-text');

        if (isEditMode) {
            // 编辑模式
            modalTitle.textContent = '调度调试脚本编辑';
            modalControls.style.display = 'block';
            modalFooterControls.style.display = 'block';
            scriptEditor.style.display = 'block';
            scriptViewer.style.display = 'none';
            helpText.textContent = '编辑 schedule_startup.py 脚本内容，支持Python语法。保存后可通过调度调试按钮运行。';

            // 加载内容到编辑器
            loadScheduleScriptToEditor();
        } else {
            // 查看模式
            modalTitle.textContent = '调度调试脚本查看';
            modalControls.style.display = 'none';
            modalFooterControls.style.display = 'none';
            scriptEditor.style.display = 'none';
            scriptViewer.style.display = 'block';
            helpText.textContent = '查看 schedule_startup.py 脚本内容，具备Python语法高亮显示。';

            // 加载内容到查看器
            loadScheduleScriptToViewer();
        }

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('scheduleScriptModal'));
        modal.show();
    }

    // 隐藏脚本编辑器（兼容性函数）
    function hideScriptEditor() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('scheduleScriptModal'));
        if (modal) {
            modal.hide();
        }
    }

    // 加载调度脚本内容到编辑器
    function loadScheduleScriptToEditor() {
        $.ajax({
            url: '/api/schedule-script',
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    document.getElementById('schedule-script-editor').value = response.content;
                    showMessage('脚本内容加载成功', 'success');
                } else {
                    showMessage('加载脚本失败: ' + response.message, 'danger');
                    document.getElementById('schedule-script-editor').value = '// 加载失败: ' + response.message;
                }
            },
            error: function() {
                showMessage('加载脚本请求失败，请检查网络连接', 'danger');
                document.getElementById('schedule-script-editor').value = '// 网络连接失败，无法加载脚本内容';
            }
        });
    }

    // 加载调度脚本内容到查看器
    function loadScheduleScriptToViewer() {
        $.ajax({
            url: '/api/schedule-script',
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    const codeContent = document.getElementById('script-code-content');
                    codeContent.textContent = response.content;

                    // 如果有Prism.js，则进行语法高亮
                    if (typeof Prism !== 'undefined') {
                        // 移除之前的高亮类
                        codeContent.className = 'language-python';
                        // 重新高亮
                        Prism.highlightElement(codeContent);
                    }

                    showMessage('脚本内容加载成功', 'success');
                } else {
                    showMessage('加载脚本失败: ' + response.message, 'danger');
                    document.getElementById('script-code-content').textContent = '// 加载失败: ' + response.message;
                }
            },
            error: function() {
                showMessage('加载脚本请求失败，请检查网络连接', 'danger');
                document.getElementById('script-code-content').textContent = '// 网络连接失败，无法加载脚本内容';
            }
        });
    }

    // 兼容旧的函数名
    function loadScheduleScript() {
        loadScheduleScriptToEditor();
    }

    // 保存调度脚本内容
    function saveScheduleScript() {
        const content = document.getElementById('schedule-script-editor').value;

        // 询问备注信息
        const comment = prompt('请输入此次保存的备注信息（可选）:', '');
        if (comment === null) {
            return; // 用户取消
        }

        $.ajax({
            url: '/api/schedule-script',
            method: 'POST',
            data: {
                content: content,
                comment: comment || ''
            },
            success: function(response) {
                if (response.success) {
                    showMessage('脚本保存成功', 'success');
                    logs.push({
                        level: 'info',
                        time: new Date().toLocaleString(),
                        message: '调度脚本已保存' + (comment ? ` (备注: ${comment})` : '')
                    });

                    // 刷新备份列表
                    const isDebugMode = document.getElementById('debug-mode-badge').textContent.includes('调试模式');
                    if (isDebugMode) {
                        loadBackupList();
                    }
                } else {
                    showMessage('保存脚本失败: ' + response.message, 'danger');
                    logs.push({
                        level: 'error',
                        time: new Date().toLocaleString(),
                        message: '调度脚本保存失败: ' + response.message
                    });
                }
                filterLogs();
            },
            error: function() {
                showMessage('保存脚本请求失败，请检查网络连接', 'danger');
                logs.push({
                    level: 'error',
                    time: new Date().toLocaleString(),
                    message: '调度脚本保存请求失败，网络连接异常'
                });
                filterLogs();
            }
        });
    }

    // 启动调度调试
    function startScheduleDebug() {
        // 检查是否在调试模式
        const isDebugMode = document.getElementById('debug-mode-badge').textContent.includes('调试模式');
        if (!isDebugMode) {
            showMessage('调度调试仅在调试模式下可用', 'warning');
            return;
        }

        showOperationStatus('正在启动调度调试...', 'info');
        disableButtons();

        // 添加日志
        logs.push({
            level: 'info',
            time: new Date().toLocaleString(),
            message: '正在启动调度调试...'
        });
        filterLogs();

        $.ajax({
            url: '/api/schedule-debug',
            method: 'POST',
            success: function(response) {
                hideOperationStatus();
                if (response.success) {
                    showMessage(response.message, 'success');
                    logs.push({
                        level: 'info',
                        time: new Date().toLocaleString(),
                        message: '调度调试启动成功'
                    });
                    // 立即获取并更新状态，确保界面同步
                    refreshStatusSilent();
                } else {
                    showMessage(response.message, 'danger');
                    logs.push({
                        level: 'error',
                        time: new Date().toLocaleString(),
                        message: '调度调试启动失败: ' + response.message
                    });
                }
                filterLogs();
                enableButtons();
            },
            error: function() {
                hideOperationStatus();
                showMessage('调度调试启动请求失败，请检查网络连接', 'danger');
                logs.push({
                    level: 'error',
                    time: new Date().toLocaleString(),
                    message: '调度调试启动请求失败，网络连接异常'
                });
                filterLogs();
                enableButtons();
            }
        });
    }
    
    // 刷新状态
    function refreshStatus() {
        $.ajax({
            url: '/api/status',
            method: 'GET',
            success: function(data) {
                updateSystemStatus(data);
                showMessage('状态已刷新', 'info');
                logs.push({
                    level: 'info',
                    time: new Date().toLocaleString(),
                    message: '系统状态已刷新'
                });
                filterLogs();
            },
            error: function(xhr, status, error) {
                if (xhr.status === 401) {
                    // 会话过期，跳转到登录页面
                    window.location.href = '/auth/login';
                    return;
                } else if (xhr.status === 403) {
                    // Google认证超时，显示认证模态框

                    if (xhr.responseJSON && xhr.responseJSON.google_auth_required) {
                        console.log('状态刷新时Google认证超时，暂停日志更新');

                        // 防止重复处理
                        if (googleAuthInProgress) {
                            console.log('Google认证已在进行中，跳过重复处理');
                            return;
                        }
                        googleAuthInProgress = true;

                        pauseLogUpdates();

                        // 直接调用showGoogleAuthModal
                        console.log('状态刷新直接调用showGoogleAuthModal');
                        if (typeof showGoogleAuthModal === 'function') {
                            // 保存原始请求信息
                            window.pendingRequest = {
                                url: '/api/status',
                                method: 'GET',
                                data: null
                            };
                            showGoogleAuthModal();
                        } else if (typeof handleGoogleAuthRequired === 'function') {
                            handleGoogleAuthRequired({
                                url: '/api/status',
                                method: 'GET',
                                data: null
                            });
                        }
                    } else {
                        console.log('状态刷新403错误但没有google_auth_required字段');
                    }
                    return;
                }

                showMessage('刷新状态失败', 'danger');
                logs.push({
                    level: 'error',
                    time: new Date().toLocaleString(),
                    message: '刷新状态失败'
                });
                filterLogs();
            }
        });
    }

    // 静默刷新状态（用于启动/停止操作后的自动更新）
    function refreshStatusSilent() {
        $.ajax({
            url: '/api/status',
            method: 'GET',
            success: function(data) {
                updateSystemStatus(data);
            },
            error: function(xhr, status, error) {
                if (xhr.status === 401) {
                    // 会话过期，跳转到登录页面
                    console.log('会话已过期，跳转登录页面');
                    window.location.href = '/auth/login';
                    return;
                } else if (xhr.status === 403) {
                    // Google认证超时，显示认证模态框
                    console.log('静默状态刷新收到403错误，响应内容:', xhr.responseJSON);

                    if (xhr.responseJSON && xhr.responseJSON.google_auth_required) {
                        console.log('静默状态刷新时Google认证超时，暂停日志更新');

                        // 防止重复处理
                        if (googleAuthInProgress) {
                            console.log('Google认证已在进行中，跳过重复处理');
                            return;
                        }
                        googleAuthInProgress = true;

                        pauseLogUpdates();

                        // 直接调用showGoogleAuthModal
                        console.log('静默状态刷新直接调用showGoogleAuthModal');
                        if (typeof showGoogleAuthModal === 'function') {
                            // 保存原始请求信息
                            window.pendingRequest = {
                                url: '/api/status',
                                method: 'GET',
                                data: null
                            };
                            showGoogleAuthModal();
                        } else if (typeof handleGoogleAuthRequired === 'function') {
                            handleGoogleAuthRequired({
                                url: '/api/status',
                                method: 'GET',
                                data: null
                            });
                        }
                    } else {
                        console.log('静默状态刷新403错误但没有google_auth_required字段');
                    }
                    return;
                }
                // 静默刷新状态失败
            }
        });
    }

    // 刷新日志
    function refreshLogs() {
        logs.push({
            level: 'info',
            time: new Date().toLocaleString(),
            message: '手动刷新日志'
        });
        filterLogs();
        showMessage('日志已刷新', 'info');
    }

    // 更新系统状态
    function updateSystemStatus(data) {
        // 更新状态徽章
        const statusBadge = document.getElementById('status-badge');

        if (data.is_running) {
            statusBadge.className = 'status-badge status-success';
            statusBadge.innerHTML = '<i class="bi bi-play-fill"></i> 运行中';

            // 显示进程信息
            document.getElementById('process-info').style.display = 'block';
            document.getElementById('process-pid').textContent = data.pid || '-';
            document.getElementById('start-time').textContent = data.start_time || '-';

            if (data.memory_mb !== undefined) {
                document.getElementById('memory-usage').textContent = Math.round(data.memory_mb) + ' MB';
            }

            // 显示停止按钮，隐藏启动按钮
            document.getElementById('start-btn').style.display = 'none';
            document.getElementById('stop-btn').style.display = 'block';
        } else {
            statusBadge.className = 'status-badge status-danger';
            statusBadge.innerHTML = '<i class="bi bi-stop-fill"></i> 已停止';

            // 隐藏进程信息
            document.getElementById('process-info').style.display = 'none';

            // 显示启动按钮，隐藏停止按钮
            document.getElementById('start-btn').style.display = 'block';
            document.getElementById('stop-btn').style.display = 'none';
        }
    }
    
    // 显示操作状态
    function showOperationStatus(message, type) {
        const statusDiv = document.getElementById('operation-status');
        const messageSpan = document.getElementById('operation-message');
        const alertDiv = statusDiv.querySelector('.alert');

        alertDiv.className = `alert alert-${type} border-0 rounded-3`;
        messageSpan.textContent = message;
        statusDiv.style.display = 'block';
    }

    // 隐藏操作状态
    function hideOperationStatus() {
        document.getElementById('operation-status').style.display = 'none';
    }

    // 显示调试操作状态
    function showDebugOperationStatus(message, type) {
        const statusDiv = document.getElementById('debug-operation-status');
        const messageSpan = document.getElementById('debug-operation-message');
        const alertDiv = statusDiv.querySelector('.alert');

        alertDiv.className = `alert alert-${type} border-0 rounded-3`;
        messageSpan.textContent = message;
        statusDiv.style.display = 'block';
    }

    // 隐藏调试操作状态
    function hideDebugOperationStatus() {
        document.getElementById('debug-operation-status').style.display = 'none';
    }

    // 禁用按钮
    function disableButtons() {
        document.getElementById('start-btn').disabled = true;
        document.getElementById('stop-btn').disabled = true;
        document.getElementById('toggle-debug-btn').disabled = true;
    }

    // 启用按钮
    function enableButtons() {
        document.getElementById('start-btn').disabled = false;
        document.getElementById('stop-btn').disabled = false;
        document.getElementById('toggle-debug-btn').disabled = false;
    }

    // ==================== 备份管理功能 ====================
    let currentBackupFilename = '';

    // 页面加载时初始化备份列表
    $(document).ready(function() {
        // 如果是调试模式，加载备份列表
        const isDebugMode = document.getElementById('debug-mode-badge').textContent.includes('调试模式');
        if (isDebugMode) {
            loadBackupList();
        }
    });

    // 加载备份列表
    function loadBackupList() {
        const container = document.getElementById('backup-list-container');
        container.innerHTML = `
            <div class="text-center text-muted py-3">
                <i class="bi bi-hourglass-split"></i>
                正在加载备份列表...
            </div>
        `;

        $.ajax({
            url: '/api/schedule-script/backups',
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    displayBackupList(response.backups);
                } else {
                    container.innerHTML = `
                        <div class="text-center text-danger py-3">
                            <i class="bi bi-exclamation-triangle"></i>
                            加载备份列表失败: ${response.message}
                        </div>
                    `;
                }
            },
            error: function() {
                container.innerHTML = `
                    <div class="text-center text-danger py-3">
                        <i class="bi bi-wifi-off"></i>
                        网络连接失败，无法加载备份列表
                    </div>
                `;
            }
        });
    }

    // 显示备份列表
    function displayBackupList(backups) {
        const container = document.getElementById('backup-list-container');

        if (backups.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted py-3">
                    <i class="bi bi-inbox"></i>
                    暂无备份文件
                </div>
            `;
            return;
        }

        let html = `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th width="5%">
                                <input type="checkbox" id="select-all-backups" onchange="toggleAllBackups(this)">
                            </th>
                            <th width="25%">创建时间</th>
                            <th width="15%">文件大小</th>
                            <th width="30%">备注</th>
                            <th width="25%">操作</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        backups.forEach(backup => {
            const comment = backup.comment || '无备注';
            html += `
                <tr>
                    <td>
                        <input type="checkbox" class="backup-checkbox" value="${backup.filename}">
                    </td>
                    <td>
                        <div class="d-flex align-items-center">
                            <i class="bi bi-clock-history me-2 text-info"></i>
                            <span class="fw-semibold">${backup.formatted_time}</span>
                        </div>
                    </td>
                    <td>
                        <span class="badge bg-light text-dark">${backup.size_str}</span>
                    </td>
                    <td>
                        <span class="text-muted small">${comment}</span>
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            <button class="btn btn-outline-primary" onclick="viewBackup('${backup.filename}')" title="查看内容">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button class="btn btn-outline-warning" onclick="showRestoreConfirm('${backup.filename}', '${backup.formatted_time}')" title="恢复此版本">
                                <i class="bi bi-arrow-counterclockwise"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteBackup('${backup.filename}')" title="删除备份">
                                <i class="bi bi-trash3"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div>
                    <button class="btn btn-outline-danger btn-sm" onclick="deleteSelectedBackups()" disabled id="delete-selected-btn">
                        <i class="bi bi-trash3"></i>
                        删除选中
                    </button>
                </div>
                <div class="text-muted small">
                    共 ${backups.length} 个备份文件
                </div>
            </div>
        `;

        container.innerHTML = html;
    }

    // 切换全选
    function toggleAllBackups(checkbox) {
        const checkboxes = document.querySelectorAll('.backup-checkbox');
        checkboxes.forEach(cb => cb.checked = checkbox.checked);
        updateDeleteSelectedButton();
    }

    // 更新删除选中按钮状态
    function updateDeleteSelectedButton() {
        const selectedCheckboxes = document.querySelectorAll('.backup-checkbox:checked');
        const deleteBtn = document.getElementById('delete-selected-btn');
        if (deleteBtn) {
            deleteBtn.disabled = selectedCheckboxes.length === 0;
        }
    }

    // 监听复选框变化
    $(document).on('change', '.backup-checkbox', function() {
        updateDeleteSelectedButton();

        // 更新全选复选框状态
        const allCheckboxes = document.querySelectorAll('.backup-checkbox');
        const checkedCheckboxes = document.querySelectorAll('.backup-checkbox:checked');
        const selectAllCheckbox = document.getElementById('select-all-backups');

        if (selectAllCheckbox) {
            if (checkedCheckboxes.length === 0) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = false;
            } else if (checkedCheckboxes.length === allCheckboxes.length) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = true;
            } else {
                selectAllCheckbox.indeterminate = true;
            }
        }
    });

    // 查看备份内容
    function viewBackup(filename) {
        currentBackupFilename = filename;

        // 更新模态框标题
        document.getElementById('backup-view-title').textContent = `备份文件查看 - ${filename}`;
        document.getElementById('backup-filename').textContent = filename;

        // 显示加载状态
        document.getElementById('backup-code-content').textContent = '正在加载备份内容...';

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('backupViewModal'));
        modal.show();

        // 加载备份内容
        $.ajax({
            url: '/api/schedule-script/backups',
            method: 'POST',
            data: {
                action: 'view',
                filename: filename
            },
            success: function(response) {
                if (response.success) {
                    document.getElementById('backup-code-content').textContent = response.content;
                    // 重新应用语法高亮
                    if (typeof Prism !== 'undefined') {
                        Prism.highlightAll();
                    }
                } else {
                    document.getElementById('backup-code-content').textContent = `加载失败: ${response.message}`;
                }
            },
            error: function() {
                document.getElementById('backup-code-content').textContent = '网络连接失败，无法加载备份内容';
            }
        });
    }

    // 显示恢复确认对话框
    function showRestoreConfirm(filename, formattedTime) {
        currentBackupFilename = filename;

        document.getElementById('restore-filename').textContent = filename;
        document.getElementById('restore-time').textContent = `创建时间: ${formattedTime}`;

        const modal = new bootstrap.Modal(document.getElementById('backupRestoreConfirmModal'));
        modal.show();
    }

    // 从备份查看模态框中恢复备份
    function restoreBackup() {
        if (!currentBackupFilename) {
            showMessage('未选择备份文件', 'warning');
            return;
        }

        // 关闭查看模态框，显示确认模态框
        const viewModal = bootstrap.Modal.getInstance(document.getElementById('backupViewModal'));
        if (viewModal) {
            viewModal.hide();
        }

        // 获取备份信息并显示确认对话框
        const backups = []; // 这里应该从当前显示的列表中获取
        const backup = backups.find(b => b.filename === currentBackupFilename);
        const formattedTime = backup ? backup.formatted_time : '未知时间';

        showRestoreConfirm(currentBackupFilename, formattedTime);
    }

    // 确认恢复备份
    function confirmRestoreBackup() {
        if (!currentBackupFilename) {
            showMessage('未选择备份文件', 'warning');
            return;
        }

        // 关闭确认模态框
        const confirmModal = bootstrap.Modal.getInstance(document.getElementById('backupRestoreConfirmModal'));
        if (confirmModal) {
            confirmModal.hide();
        }

        showOperationStatus('正在恢复备份...', 'info');

        $.ajax({
            url: '/api/schedule-script/backups',
            method: 'POST',
            data: {
                action: 'restore',
                filename: currentBackupFilename
            },
            success: function(response) {
                hideOperationStatus();
                if (response.success) {
                    showMessage(response.message, 'success');
                    logs.push({
                        level: 'info',
                        time: new Date().toLocaleString(),
                        message: `备份恢复成功: ${currentBackupFilename}`
                    });
                    // 刷新备份列表
                    loadBackupList();
                } else {
                    showMessage(response.message, 'danger');
                    logs.push({
                        level: 'error',
                        time: new Date().toLocaleString(),
                        message: `备份恢复失败: ${response.message}`
                    });
                }
                filterLogs();
            },
            error: function() {
                hideOperationStatus();
                showMessage('备份恢复请求失败，请检查网络连接', 'danger');
                logs.push({
                    level: 'error',
                    time: new Date().toLocaleString(),
                    message: '备份恢复请求失败，网络连接异常'
                });
                filterLogs();
            }
        });
    }

    // 删除单个备份
    function deleteBackup(filename) {
        if (!confirm(`确定要删除备份文件 "${filename}" 吗？此操作不可撤销。`)) {
            return;
        }

        $.ajax({
            url: '/api/schedule-script/backups',
            method: 'DELETE',
            contentType: 'application/json',
            data: JSON.stringify({
                filenames: [filename]
            }),
            success: function(response) {
                if (response.success) {
                    showMessage(response.message, 'success');
                    logs.push({
                        level: 'info',
                        time: new Date().toLocaleString(),
                        message: `备份删除成功: ${filename}`
                    });
                    // 刷新备份列表
                    loadBackupList();
                } else {
                    showMessage(response.message, 'danger');
                    logs.push({
                        level: 'error',
                        time: new Date().toLocaleString(),
                        message: `备份删除失败: ${response.message}`
                    });
                }
                filterLogs();
            },
            error: function() {
                showMessage('备份删除请求失败，请检查网络连接', 'danger');
                logs.push({
                    level: 'error',
                    time: new Date().toLocaleString(),
                    message: '备份删除请求失败，网络连接异常'
                });
                filterLogs();
            }
        });
    }

    // 删除选中的备份
    function deleteSelectedBackups() {
        const selectedCheckboxes = document.querySelectorAll('.backup-checkbox:checked');
        if (selectedCheckboxes.length === 0) {
            showMessage('请选择要删除的备份文件', 'warning');
            return;
        }

        const filenames = Array.from(selectedCheckboxes).map(cb => cb.value);
        const fileList = filenames.join(', ');

        if (!confirm(`确定要删除以下 ${filenames.length} 个备份文件吗？\n\n${fileList}\n\n此操作不可撤销。`)) {
            return;
        }

        $.ajax({
            url: '/api/schedule-script/backups',
            method: 'DELETE',
            contentType: 'application/json',
            data: JSON.stringify({
                filenames: filenames
            }),
            success: function(response) {
                if (response.success) {
                    showMessage(response.message, 'success');
                    logs.push({
                        level: 'info',
                        time: new Date().toLocaleString(),
                        message: `批量删除备份成功: ${filenames.length} 个文件`
                    });
                    // 刷新备份列表
                    loadBackupList();
                } else {
                    showMessage(response.message, 'danger');
                    logs.push({
                        level: 'error',
                        time: new Date().toLocaleString(),
                        message: `批量删除备份失败: ${response.message}`
                    });
                }
                filterLogs();
            },
            error: function() {
                showMessage('批量删除备份请求失败，请检查网络连接', 'danger');
                logs.push({
                    level: 'error',
                    time: new Date().toLocaleString(),
                    message: '批量删除备份请求失败，网络连接异常'
                });
                filterLogs();
            }
        });
    }

    // 清理旧备份
    function cleanupOldBackups() {
        const keepCount = prompt('请输入要保留的备份数量（默认保留3个最新备份）:', '3');
        if (keepCount === null) {
            return; // 用户取消
        }

        const count = parseInt(keepCount);
        if (isNaN(count) || count < 0) {
            showMessage('请输入有效的数字', 'warning');
            return;
        }

        if (!confirm(`确定要清理旧备份吗？将保留最新的 ${count} 个备份，删除其余备份文件。`)) {
            return;
        }

        $.ajax({
            url: '/api/schedule-script/backups',
            method: 'POST',
            data: {
                action: 'cleanup',
                keep_count: count
            },
            success: function(response) {
                if (response.success) {
                    showMessage(response.message, 'success');
                    logs.push({
                        level: 'info',
                        time: new Date().toLocaleString(),
                        message: `备份清理成功: 保留 ${count} 个最新备份`
                    });
                    // 刷新备份列表
                    loadBackupList();
                } else {
                    showMessage(response.message, 'danger');
                    logs.push({
                        level: 'error',
                        time: new Date().toLocaleString(),
                        message: `备份清理失败: ${response.message}`
                    });
                }
                filterLogs();
            },
            error: function() {
                showMessage('备份清理请求失败，请检查网络连接', 'danger');
                logs.push({
                    level: 'error',
                    time: new Date().toLocaleString(),
                    message: '备份清理请求失败，网络连接异常'
                });
                filterLogs();
            }
        });
    }

    // Google认证检查已移至auth.js统一处理

    // Google认证模态框已移至auth.js统一处理

    // Google认证相关函数已移至auth.js统一处理

    // ========== 系统资源监控相关函数 ==========

    let resourceUpdateInterval;

    // 初始化系统资源监控
    function initSystemResourcesMonitoring() {
        // 立即获取一次资源信息
        refreshSystemResources();

        // 每10秒自动更新一次
        resourceUpdateInterval = setInterval(refreshSystemResources, 10000);
    }

    // 刷新系统资源信息
    function refreshSystemResources() {
        $.ajax({
            url: '/api/system/resources',
            method: 'GET',
            success: function(data) {
                updateResourceDisplay(data);
            },
            error: function(xhr, status, error) {
                console.error('获取系统资源信息失败:', error);
                showResourceError();
            }
        });
    }

    // 更新资源显示
    function updateResourceDisplay(data) {
        // 更新CPU信息
        if (data.cpu) {
            const cpuPercent = Math.round(data.cpu.percent);
            document.getElementById('cpu-percent').textContent = cpuPercent;
            document.getElementById('cpu-cores').textContent = data.cpu.count;
            document.getElementById('cpu-logical').textContent = data.cpu.count_logical;

            const cpuProgress = document.getElementById('cpu-progress');
            cpuProgress.style.width = cpuPercent + '%';

            // 根据使用率设置颜色
            cpuProgress.className = 'progress-bar';
            if (cpuPercent >= 80) {
                cpuProgress.classList.add('bg-danger');
            } else if (cpuPercent >= 60) {
                cpuProgress.classList.add('bg-warning');
            }
        }

        // 更新内存信息
        if (data.memory) {
            const memoryPercent = Math.round(data.memory.percent);
            document.getElementById('memory-percent').textContent = memoryPercent;
            document.getElementById('memory-used').textContent = data.memory.used_gb;
            document.getElementById('memory-total').textContent = data.memory.total_gb;

            const memoryProgress = document.getElementById('memory-progress');
            memoryProgress.style.width = memoryPercent + '%';

            // 根据使用率设置颜色
            memoryProgress.className = 'progress-bar';
            if (memoryPercent >= 85) {
                memoryProgress.classList.add('bg-danger');
            } else if (memoryPercent >= 70) {
                memoryProgress.classList.add('bg-warning');
            }
        }

        // 更新虚拟内存信息
        if (data.swap) {
            const swapAvailable = data.swap.available;
            const swapStatus = document.getElementById('swap-status');
            const swapPercent = document.getElementById('swap-percent');
            const swapDetails = document.getElementById('swap-details');
            const swapUnavailable = document.getElementById('swap-unavailable');

            if (swapAvailable && data.swap.total_gb > 0) {
                // 有虚拟内存
                const percentValue = Math.round(data.swap.percent);

                // 显示百分比
                swapStatus.style.display = 'none';
                swapPercent.style.display = 'inline';
                swapPercent.parentElement.querySelector('.value-unit-compact').style.display = 'inline';
                swapPercent.textContent = percentValue;

                swapDetails.style.display = 'block';
                swapUnavailable.style.display = 'none';

                document.getElementById('swap-used').textContent = data.swap.used_gb;
                document.getElementById('swap-total').textContent = data.swap.total_gb;

                const swapProgress = document.getElementById('swap-progress');
                swapProgress.style.width = percentValue + '%';

                // 根据使用率设置颜色
                swapProgress.className = 'progress-bar';
                if (percentValue >= 80) {
                    swapProgress.classList.add('bg-danger');
                } else if (percentValue >= 60) {
                    swapProgress.classList.add('bg-warning');
                }
            } else {
                // 无虚拟内存
                swapStatus.style.display = 'inline';
                swapPercent.style.display = 'none';
                swapPercent.parentElement.querySelector('.value-unit-compact').style.display = 'none';
                swapStatus.textContent = '未配置';
                swapStatus.className = 'value-text-compact text-muted';
                swapDetails.style.display = 'none';
                swapUnavailable.style.display = 'block';
            }
        }
    }

    // 显示资源获取错误
    function showResourceError() {
        document.getElementById('cpu-percent').textContent = '错误';
        document.getElementById('memory-percent').textContent = '错误';

        const swapStatus = document.getElementById('swap-status');
        const swapPercent = document.getElementById('swap-percent');
        swapStatus.style.display = 'inline';
        swapPercent.style.display = 'none';
        swapPercent.parentElement.querySelector('.value-unit-compact').style.display = 'none';
        swapStatus.textContent = '获取失败';
        swapStatus.className = 'value-text-compact text-danger';
    }

    // 页面卸载时清理定时器
    $(window).on('beforeunload', function() {
        if (resourceUpdateInterval) {
            clearInterval(resourceUpdateInterval);
        }
    });


</script>

<!-- 调度脚本编辑/查看模态框 -->
<div class="modal fade" id="scheduleScriptModal" tabindex="-1" aria-labelledby="scheduleScriptModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="scheduleScriptModalLabel">
                    <i class="bi bi-code-slash"></i>
                    <span id="modal-script-title">调度调试脚本</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-file-earmark-code me-2 text-primary"></i>
                            <span class="fw-semibold">schedule_startup.py</span>
                        </div>
                        <div id="modal-script-controls">
                            <button class="btn btn-outline-primary btn-sm me-2" onclick="loadScheduleScript()">
                                <i class="bi bi-arrow-clockwise"></i>
                                重新加载
                            </button>
                            <button class="btn btn-success btn-sm" onclick="saveScheduleScript()">
                                <i class="bi bi-save"></i>
                                保存
                            </button>
                        </div>
                    </div>

                    <!-- 可编辑的文本区域 -->
                    <textarea id="schedule-script-editor" class="form-control" rows="25"
                              style="font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 14px;"
                              placeholder="正在加载脚本内容..."></textarea>

                    <!-- 只读的代码显示区域 -->
                    <div id="schedule-script-viewer" style="display: none;">
                        <pre class="line-numbers" style="height: 600px; overflow-y: auto; margin: 0; border-radius: 8px;"><code class="language-python" id="script-code-content">正在加载脚本内容...</code></pre>
                    </div>

                    <div class="form-text mt-2">
                        <i class="bi bi-info-circle"></i>
                        <span id="modal-script-help-text">编辑 schedule_startup.py 脚本内容，支持Python语法。保存后可通过调度调试按钮运行。</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle"></i>
                    关闭
                </button>
                <div id="modal-footer-controls">
                    <button class="btn btn-outline-primary me-2" onclick="loadScheduleScript()">
                        <i class="bi bi-arrow-clockwise"></i>
                        重新加载
                    </button>
                    <button class="btn btn-success" onclick="saveScheduleScript()">
                        <i class="bi bi-save"></i>
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 备份查看模态框 -->
<div class="modal fade" id="backupViewModal" tabindex="-1" aria-labelledby="backupViewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="backupViewModalLabel">
                    <i class="bi bi-file-earmark-code"></i>
                    <span id="backup-view-title">备份文件查看</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-clock-history me-2 text-info"></i>
                            <span class="fw-semibold" id="backup-filename">备份文件</span>
                        </div>
                        <div>
                            <button class="btn btn-warning btn-sm me-2" onclick="restoreBackup()">
                                <i class="bi bi-arrow-counterclockwise"></i>
                                恢复此版本
                            </button>
                        </div>
                    </div>

                    <!-- 备份内容显示区域 -->
                    <div id="backup-content-viewer">
                        <pre class="line-numbers" style="height: 600px; overflow-y: auto; margin: 0; border-radius: 8px;"><code class="language-python" id="backup-code-content">正在加载备份内容...</code></pre>
                    </div>

                    <div class="form-text mt-2">
                        <i class="bi bi-info-circle"></i>
                        <span id="backup-info-text">查看历史备份文件内容，可以选择恢复此版本到当前脚本。</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle"></i>
                    关闭
                </button>
                <button class="btn btn-warning" onclick="restoreBackup()">
                    <i class="bi bi-arrow-counterclockwise"></i>
                    恢复此版本
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 备份恢复确认模态框 -->
<div class="modal fade" id="backupRestoreConfirmModal" tabindex="-1" aria-labelledby="backupRestoreConfirmModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="backupRestoreConfirmModalLabel">
                    <i class="bi bi-exclamation-triangle text-warning"></i>
                    确认恢复备份
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>注意：</strong>恢复备份将会覆盖当前的调度脚本文件。
                </div>
                <p>您确定要恢复以下备份文件吗？</p>
                <div class="bg-light p-3 rounded">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-file-earmark-code me-2"></i>
                        <span class="fw-semibold" id="restore-filename">备份文件名</span>
                    </div>
                    <div class="text-muted small mt-1" id="restore-time">创建时间</div>
                </div>
                <p class="mt-3 text-muted small">
                    <i class="bi bi-info-circle"></i>
                    恢复前会自动创建当前版本的备份，您可以随时撤销此操作。
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle"></i>
                    取消
                </button>
                <button type="button" class="btn btn-warning" onclick="confirmRestoreBackup()">
                    <i class="bi bi-arrow-counterclockwise"></i>
                    确认恢复
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}
