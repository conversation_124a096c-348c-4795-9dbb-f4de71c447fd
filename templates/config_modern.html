{% extends "base_modern.html" %}

{% block title %}账户管理 - 量化交易系统{% endblock %}
{% block page_title %}账户管理{% endblock %}

{% block content %}
<!-- 顶部统计卡片 -->
<div class="row g-4 mb-4">
    <div class="col-xl-3 col-md-6">
        <div class="stat-card">
            <div class="stat-icon blue">
                <i class="bi bi-people"></i>
            </div>
            <div class="stat-value">{{ accounts|length if accounts else 0 }}</div>
            <div class="stat-label">总账户数</div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="stat-card">
            <div class="stat-icon green">
                <i class="bi bi-check-circle"></i>
            </div>
            <div class="stat-value" id="active-accounts">0</div>
            <div class="stat-label">活跃账户</div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="stat-card">
            <div class="stat-icon purple">
                <i class="bi bi-gear"></i>
            </div>
            <div class="stat-value">{{ global_config|length if global_config else 0 }}</div>
            <div class="stat-label">全局配置项</div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="stat-card">
            <div class="stat-icon orange">
                <i class="bi bi-shield-check"></i>
            </div>
            <div class="stat-value" id="config-status">正常</div>
            <div class="stat-label">配置状态</div>
        </div>
    </div>
</div>

<!-- 主要内容区域 -->
<div class="row g-4">
    <!-- 全局配置卡片 -->
    <div class="col-lg-6">
        <div class="modern-card">
            <div class="modern-card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-gear"></i>
                    全局配置
                </h5>
                <button class="modern-btn modern-btn-outline btn-sm" onclick="refreshGlobalConfig()">
                    <i class="bi bi-arrow-clockwise"></i>
                    刷新
                </button>
            </div>
            <div class="modern-card-body">
                <form id="global-config-form">
                    <div class="row g-3">
                        {% if global_config %}
                            {% for key, value in global_config.items() %}
                            <div class="col-12">
                                <label for="global_{{ key }}" class="form-label fw-semibold">{{ key }}</label>
                                {% if value is string and value|length > 50 %}
                                    <textarea class="form-control" id="global_{{ key }}" name="{{ key }}" rows="3">{{ value }}</textarea>
                                {% elif value is boolean %}
                                    <select class="form-select" id="global_{{ key }}" name="{{ key }}">
                                        <option value="true" {% if value %}selected{% endif %}>是</option>
                                        <option value="false" {% if not value %}selected{% endif %}>否</option>
                                    </select>
                                {% elif value is number %}
                                    <input type="number" class="form-control" id="global_{{ key }}" name="{{ key }}" value="{{ value }}" step="any">
                                {% else %}
                                    <input type="text" class="form-control" id="global_{{ key }}" name="{{ key }}" value="{{ value }}">
                                {% endif %}
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="col-12 text-center text-muted py-4">
                                <i class="bi bi-gear"></i>
                                <p class="mb-0 mt-2">暂无全局配置</p>
                            </div>
                        {% endif %}
                    </div>
                    
                    {% if global_config %}
                    <div class="mt-4 d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="button" class="modern-btn modern-btn-outline" onclick="resetGlobalConfig()">
                            <i class="bi bi-arrow-counterclockwise"></i>
                            重置
                        </button>
                        <button type="submit" class="modern-btn modern-btn-primary">
                            <i class="bi bi-check-lg"></i>
                            保存配置
                        </button>
                    </div>
                    {% endif %}
                </form>
            </div>
        </div>
    </div>
    
    <!-- 账户管理卡片 -->
    <div class="col-lg-6">
        <div class="modern-card">
            <div class="modern-card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-people"></i>
                    账户管理
                </h5>
                <button class="modern-btn modern-btn-primary btn-sm" onclick="showAddAccountModal()">
                    <i class="bi bi-plus-lg"></i>
                    添加账户
                </button>
            </div>
            <div class="modern-card-body">
                <div id="accounts-list">
                    {% if accounts %}
                        {% for account in accounts %}
                        <div class="account-item border rounded-3 p-3 mb-3" data-account-id="{{ loop.index0 }}">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h6 class="mb-2 text-primary">
                                        <i class="bi bi-person-circle"></i>
                                        账户 #{{ loop.index }}
                                    </h6>
                                    <div class="row g-2 small text-muted">
                                        {% for key, value in account.items() %}
                                        <div class="col-6">
                                            <strong>{{ key }}:</strong> 
                                            {% if key == 'password' or key == 'token' %}
                                                ********
                                            {% else %}
                                                {{ value }}
                                            {% endif %}
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                        <i class="bi bi-three-dots"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="editAccount({{ loop.index0 }})">
                                            <i class="bi bi-pencil"></i> 编辑
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="testAccount({{ loop.index0 }})">
                                            <i class="bi bi-wifi"></i> 测试连接
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#" onclick="deleteAccount({{ loop.index0 }})">
                                            <i class="bi bi-trash"></i> 删除
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="bi bi-people"></i>
                            <p class="mb-0 mt-2">暂无账户配置</p>
                            <button class="modern-btn modern-btn-primary mt-3" onclick="showAddAccountModal()">
                                <i class="bi bi-plus-lg"></i>
                                添加第一个账户
                            </button>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 配置文件管理 -->
<div class="row g-4 mt-2">
    <div class="col-12">
        <div class="modern-card">
            <div class="modern-card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-file-earmark-code"></i>
                    配置文件管理
                </h5>
                <div class="btn-group" role="group">
                    <button class="modern-btn modern-btn-outline btn-sm" onclick="backupConfig()">
                        <i class="bi bi-download"></i>
                        备份配置
                    </button>
                    <button class="modern-btn modern-btn-outline btn-sm" onclick="showImportModal()">
                        <i class="bi bi-upload"></i>
                        导入配置
                    </button>
                </div>
            </div>
            <div class="modern-card-body">
                <div class="row g-3">
                    <div class="col-md-4">
                        <div class="text-center p-3 bg-light rounded-3">
                            <i class="bi bi-file-earmark-code text-primary" style="font-size: 2rem;"></i>
                            <h6 class="mt-2 mb-1">config.py</h6>
                            <small class="text-muted">主配置文件</small>
                            <div class="mt-2">
                                <button class="btn btn-sm btn-outline-primary" onclick="viewConfigFile('config.py')">
                                    <i class="bi bi-eye"></i> 查看
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center p-3 bg-light rounded-3">
                            <i class="bi bi-file-earmark-text text-success" style="font-size: 2rem;"></i>
                            <h6 class="mt-2 mb-1">配置备份</h6>
                            <small class="text-muted">自动备份文件</small>
                            <div class="mt-2">
                                <button class="btn btn-sm btn-outline-success" onclick="viewBackups()">
                                    <i class="bi bi-folder"></i> 查看
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center p-3 bg-light rounded-3">
                            <i class="bi bi-shield-check text-warning" style="font-size: 2rem;"></i>
                            <h6 class="mt-2 mb-1">配置验证</h6>
                            <small class="text-muted">检查配置有效性</small>
                            <div class="mt-2">
                                <button class="btn btn-sm btn-outline-warning" onclick="validateConfig()">
                                    <i class="bi bi-check-circle"></i> 验证
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加账户模态框 -->
<div class="modal fade" id="addAccountModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-person-plus"></i>
                    添加新账户
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="add-account-form">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="account-name" class="form-label">账户名称</label>
                            <input type="text" class="form-control" id="account-name" name="name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="account-type" class="form-label">账户类型</label>
                            <select class="form-select" id="account-type" name="type" required>
                                <option value="">请选择...</option>
                                <option value="demo">模拟账户</option>
                                <option value="live">实盘账户</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="account-server" class="form-label">服务器</label>
                            <input type="text" class="form-control" id="account-server" name="server" required>
                        </div>
                        <div class="col-md-6">
                            <label for="account-login" class="form-label">登录账号</label>
                            <input type="text" class="form-control" id="account-login" name="login" required>
                        </div>
                        <div class="col-md-6">
                            <label for="account-password" class="form-label">密码</label>
                            <input type="password" class="form-control" id="account-password" name="password" required>
                        </div>
                        <div class="col-md-6">
                            <label for="account-currency" class="form-label">基础货币</label>
                            <select class="form-select" id="account-currency" name="currency">
                                <option value="USD">USD</option>
                                <option value="EUR">EUR</option>
                                <option value="GBP">GBP</option>
                                <option value="JPY">JPY</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label for="account-description" class="form-label">描述</label>
                            <textarea class="form-control" id="account-description" name="description" rows="2"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="modern-btn modern-btn-primary" onclick="saveAccount()">
                    <i class="bi bi-check-lg"></i>
                    保存账户
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 页面加载时初始化
    $(document).ready(function() {
        updateAccountStats();
        
        // 全局配置表单提交
        $('#global-config-form').on('submit', function(e) {
            e.preventDefault();
            saveGlobalConfig();
        });
    });
    
    // 更新账户统计
    function updateAccountStats() {
        const totalAccounts = {{ accounts|length if accounts else 0 }};
        const activeAccounts = Math.floor(totalAccounts * 0.8); // 模拟活跃账户数
        document.getElementById('active-accounts').textContent = activeAccounts;
    }
    
    // 保存全局配置
    function saveGlobalConfig() {
        const formData = new FormData(document.getElementById('global-config-form'));
        const config = {};
        
        for (let [key, value] of formData.entries()) {
            // 尝试转换数据类型
            if (value === 'true') {
                config[key] = true;
            } else if (value === 'false') {
                config[key] = false;
            } else if (!isNaN(value) && value !== '') {
                config[key] = parseFloat(value);
            } else {
                config[key] = value;
            }
        }
        
        $.ajax({
            url: '/api/config/global',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(config),
            success: function(response) {
                if (response.success) {
                    showMessage('全局配置保存成功', 'success');
                } else {
                    showMessage('保存失败: ' + response.message, 'danger');
                }
            },
            error: function() {
                showMessage('保存请求失败', 'danger');
            }
        });
    }
    
    // 重置全局配置
    function resetGlobalConfig() {
        if (confirm('确定要重置全局配置吗？这将恢复到默认设置。')) {
            location.reload();
        }
    }
    
    // 刷新全局配置
    function refreshGlobalConfig() {
        location.reload();
    }
    
    // 显示添加账户模态框
    function showAddAccountModal() {
        const modal = new bootstrap.Modal(document.getElementById('addAccountModal'));
        modal.show();
    }
    
    // 保存账户
    function saveAccount() {
        const form = document.getElementById('add-account-form');
        const formData = new FormData(form);
        const account = {};
        
        for (let [key, value] of formData.entries()) {
            account[key] = value;
        }
        
        $.ajax({
            url: '/api/accounts',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(account),
            success: function(response) {
                if (response.success) {
                    showMessage('账户添加成功', 'success');
                    bootstrap.Modal.getInstance(document.getElementById('addAccountModal')).hide();
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showMessage('添加失败: ' + response.message, 'danger');
                }
            },
            error: function() {
                showMessage('添加请求失败', 'danger');
            }
        });
    }
    
    // 编辑账户
    function editAccount(index) {
        showMessage('编辑账户功能开发中...', 'info');
    }
    
    // 测试账户连接
    function testAccount(index) {
        showMessage('正在测试账户连接...', 'info');
        
        $.ajax({
            url: `/api/accounts/${index}/test`,
            method: 'POST',
            success: function(response) {
                if (response.success) {
                    showMessage('账户连接测试成功', 'success');
                } else {
                    showMessage('连接测试失败: ' + response.message, 'danger');
                }
            },
            error: function() {
                showMessage('测试请求失败', 'danger');
            }
        });
    }
    
    // 删除账户
    function deleteAccount(index) {
        if (confirm('确定要删除这个账户吗？此操作不可撤销。')) {
            $.ajax({
                url: `/api/accounts/${index}`,
                method: 'DELETE',
                success: function(response) {
                    if (response.success) {
                        showMessage('账户删除成功', 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showMessage('删除失败: ' + response.message, 'danger');
                    }
                },
                error: function() {
                    showMessage('删除请求失败', 'danger');
                }
            });
        }
    }
    
    // 备份配置
    function backupConfig() {
        $.ajax({
            url: '/api/config/backup',
            method: 'POST',
            success: function(response) {
                if (response.success) {
                    showMessage('配置备份成功', 'success');
                } else {
                    showMessage('备份失败: ' + response.message, 'danger');
                }
            },
            error: function() {
                showMessage('备份请求失败', 'danger');
            }
        });
    }
    
    // 显示导入模态框
    function showImportModal() {
        showMessage('导入配置功能开发中...', 'info');
    }
    
    // 查看配置文件
    function viewConfigFile(filename) {
        showMessage('查看配置文件功能开发中...', 'info');
    }
    
    // 查看备份
    function viewBackups() {
        showMessage('查看备份功能开发中...', 'info');
    }
    
    // 验证配置
    function validateConfig() {
        showMessage('正在验证配置...', 'info');
        
        $.ajax({
            url: '/api/config/validate',
            method: 'POST',
            success: function(response) {
                if (response.success) {
                    showMessage('配置验证通过', 'success');
                    document.getElementById('config-status').textContent = '正常';
                } else {
                    showMessage('配置验证失败: ' + response.message, 'danger');
                    document.getElementById('config-status').textContent = '异常';
                }
            },
            error: function() {
                showMessage('验证请求失败', 'danger');
            }
        });
    }
</script>
{% endblock %}
