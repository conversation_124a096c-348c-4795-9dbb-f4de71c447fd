{% extends "base_modern.html" %}

{% block title %}账户管理 - 量化交易系统{% endblock %}
{% block page_title %}账户管理{% endblock %}

{% block extra_css %}
<!-- Prism.js for syntax highlighting -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" rel="stylesheet">
<style>
    /* 保证金配置显示样式 */
    .margin-config-display {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 12px;
        border-left: 4px solid #007bff;
    }

    .margin-badge {
        display: inline-block;
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        padding: 6px 12px;
        border-radius: 20px;
        margin: 4px;
        font-size: 0.875rem;
        font-weight: 500;
        box-shadow: 0 2px 4px rgba(0,123,255,0.2);
    }

    /* 黑白名单显示样式 */
    .blacklist-display, .whitelist-display {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 12px;
        min-height: 60px;
    }

    .blacklist-display {
        border-left: 4px solid #dc3545;
    }

    .whitelist-display {
        border-left: 4px solid #28a745;
    }

    .blacklist-badge {
        display: inline-block;
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
        padding: 4px 10px;
        border-radius: 15px;
        margin: 2px;
        font-size: 0.8rem;
        font-weight: 500;
        box-shadow: 0 2px 4px rgba(220,53,69,0.2);
    }

    .whitelist-badge {
        display: inline-block;
        background: linear-gradient(135deg, #28a745, #1e7e34);
        color: white;
        padding: 4px 10px;
        border-radius: 15px;
        margin: 2px;
        font-size: 0.8rem;
        font-weight: 500;
        box-shadow: 0 2px 4px rgba(40,167,69,0.2);
    }

    /* 黑白名单输入框样式 */
    .list-pair {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        padding: 8px;
        background: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #e9ecef;
    }

    .list-pair input {
        flex: 1;
        margin-right: 8px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        padding: 6px 10px;
        font-size: 0.9rem;
    }

    .list-pair .btn-remove {
        padding: 4px 8px;
        font-size: 0.8rem;
        line-height: 1;
    }

    /* 账户项样式 */
    .account-item {
        transition: all 0.3s ease;
        border: 1px solid #e5e7eb !important;
    }

    .account-item:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }

    /* 禁用账户样式 */
    .account-item.account-disabled {
        background-color: #f8f9fa;
        opacity: 0.7;
        border-color: #dee2e6 !important;
    }

    .account-item.account-disabled:hover {
        transform: none;
        box-shadow: none;
    }

    .account-item.account-disabled .text-primary {
        color: #6c757d !important;
    }

    .account-item.account-disabled .fw-semibold {
        color: #6c757d !important;
    }

    /* 账户名称编辑样式 */
    .account-item h6[onclick] {
        transition: color 0.2s ease;
    }

    .account-item h6[onclick]:hover {
        color: #0d6efd !important;
    }
</style>
{% endblock %}

{% block content %}
<!-- 顶部统计卡片 -->
<div class="row g-4 mb-4">
    <div class="col-xl-3 col-md-6">
        <div class="stat-card">
            <div class="stat-icon blue">
                <i class="bi bi-people"></i>
            </div>
            <div class="stat-value">{{ accounts|length if accounts else 0 }}</div>
            <div class="stat-label">总账户数</div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="stat-card">
            <div class="stat-icon green">
                <i class="bi bi-check-circle"></i>
            </div>
            <div class="stat-value" id="configured-accounts">0</div>
            <div class="stat-label">配置API账号数</div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="stat-card">
            <div class="stat-icon orange">
                <i class="bi bi-shield-check"></i>
            </div>
            <div class="stat-value" id="active-accounts">0</div>
            <div class="stat-label">活跃账户数</div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="stat-card">
            <div class="stat-icon purple">
                <i class="bi bi-wifi"></i>
            </div>
            <div class="stat-value" id="disabled-accounts">0</div>
            <div class="stat-label">禁用账户数</div>
        </div>
    </div>
</div>

<!-- 账户管理主要内容 -->
<div class="row g-4">
    <div class="col-12">
        <div class="modern-card">
            <div class="modern-card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-people"></i>
                    交易账户列表
                </h5>
                <button class="modern-btn modern-btn-primary" onclick="showAddAccountModal()">
                    <i class="bi bi-plus-lg"></i>
                    添加账户
                </button>
            </div>
            <div class="modern-card-body">
                <div id="accounts-list">
                    {% if accounts %}
                        {% for account in accounts %}
                        <div class="account-item border rounded-3 p-4 mb-3 {% if not account.enabled %}account-disabled{% endif %}" data-account-id="{{ loop.index0 }}" data-account-name="{{ account.name }}" data-enabled="{{ account.enabled|lower }}">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="account-avatar me-3">
                                            {% if account.account_type == '统一账户' %}
                                                <i class="bi bi-layers text-info" style="font-size: 2.5rem;"></i>
                                            {% else %}
                                                <i class="bi bi-wallet text-primary" style="font-size: 2.5rem;"></i>
                                            {% endif %}
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="d-flex align-items-center">
                                                <h6 class="mb-1 text-primary me-2" id="account-name-{{ loop.index0 }}" onclick="editAccountName('{{ account.name }}', {{ loop.index0 }})" style="cursor: pointer;" title="点击编辑账户名称">
                                                    {{ account.name }}
                                                    <i class="bi bi-pencil-square ms-1 text-muted" style="font-size: 0.8rem;"></i>
                                                </h6>
                                                <input type="text" class="form-control form-control-sm me-2" id="account-name-input-{{ loop.index0 }}" value="{{ account.name }}" style="display: none; max-width: 200px;" onkeypress="handleAccountNameKeypress(event, '{{ account.name }}', {{ loop.index0 }})">
                                                <button class="btn btn-sm btn-success me-1" id="save-name-btn-{{ loop.index0 }}" style="display: none;" data-account-index="{{ loop.index0 }}" data-account-name="{{ account.name }}" onclick="handleSaveButtonClick('{{ account.name }}', {{ loop.index0 }})">
                                                    <i class="bi bi-check" style="pointer-events: none;"></i>
                                                </button>
                                                <button class="btn btn-sm btn-secondary" id="cancel-name-btn-{{ loop.index0 }}" style="display: none;" onclick="cancelEditAccountName({{ loop.index0 }})">
                                                    <i class="bi bi-x"></i>
                                                </button>
                                            </div>
                                            <small class="text-muted">
                                                最后修改: {{ account.modified }}
                                            </small>
                                        </div>
                                        <div class="ms-auto d-flex align-items-center">
                                            <!-- 启用/禁用切换按钮 -->
                                            <div class="form-check form-switch me-3">
                                                <input class="form-check-input" type="checkbox" id="account-toggle-{{ loop.index0 }}"
                                                       {% if account.enabled %}checked{% endif %}
                                                       onchange="toggleAccountStatus('{{ account.name }}', {{ loop.index0 }}, this.checked)">
                                                <label class="form-check-label" for="account-toggle-{{ loop.index0 }}">
                                                    <span id="toggle-label-{{ loop.index0 }}">
                                                        {% if account.enabled %}启用{% else %}禁用{% endif %}
                                                    </span>
                                                </label>
                                            </div>

                                            <!-- API配置状态 -->
                                            <div>
                                                {% if account.api_configured %}
                                                    <span class="badge bg-success">API已配置</span>
                                                {% else %}
                                                    <span class="badge bg-warning">API未配置</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row g-3">
                                        <div class="col-md-6 col-lg-3">
                                            <div class="account-detail">
                                                <label class="form-label text-muted small">账户类型</label>
                                                <div class="fw-semibold">{{ account.account_type }}</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-lg-3">
                                            <div class="account-detail">
                                                <label class="form-label text-muted small">策略名称</label>
                                                <div class="fw-semibold" id="strategy-name-{{ loop.index0 }}">{{ account.name }}</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-lg-3">
                                            <div class="account-detail">
                                                <label class="form-label text-muted small">资金平衡方式</label>
                                                <div class="fw-semibold">{{ account.strategy_name }}</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-lg-3">
                                            <div class="account-detail">
                                                <label class="form-label text-muted small">资金平衡周期</label>
                                                <div class="fw-semibold">{{ account.hold_period }}</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-lg-3">
                                            <div class="account-detail">
                                                <label class="form-label text-muted small">策略数量</label>
                                                <div class="fw-semibold">{{ account.strategy_count }}</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-lg-3">
                                            <div class="account-detail">
                                                <label class="form-label text-muted small">K线数量</label>
                                                <div class="fw-semibold" id="kline-num-{{ loop.index0 }}">-</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-lg-3">
                                            <div class="account-detail">
                                                <label class="form-label text-muted small">杠杆倍数</label>
                                                <div class="fw-semibold">{{ account.leverage }}x</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-lg-3">
                                            <div class="account-detail">
                                                <label class="form-label text-muted small">BNB抵扣</label>
                                                <div class="fw-semibold">
                                                    {% if account.bnb_burn_enabled %}
                                                        <span class="text-success">已启用</span>
                                                    {% else %}
                                                        <span class="text-muted">未启用</span>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-lg-3">
                                            <div class="account-detail">
                                                <label class="form-label text-muted small">微信推送</label>
                                                <div class="fw-semibold">
                                                    {% if account.wechat_enabled %}
                                                        <span class="text-success">已启用</span>
                                                    {% else %}
                                                        <span class="text-muted">未启用</span>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-lg-3">
                                            <div class="account-detail">
                                                <label class="form-label text-muted small">配置状态</label>
                                                <div class="fw-semibold">{{ account.status }}</div>
                                            </div>
                                        </div>

                                        <!-- 统一账户保证金配置显示 -->
                                        <div class="col-12" id="margin-display-{{ loop.index0 }}" style="display: none;">
                                            <div class="account-detail mt-3">
                                                <label class="form-label text-muted small">
                                                    <i class="fas fa-coins me-1"></i>统一账户保证金配置
                                                </label>
                                                <div class="margin-config-display" id="margin-config-{{ loop.index0 }}">
                                                    <div class="text-muted small">加载中...</div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 黑白名单配置显示 -->
                                        <div class="col-12">
                                            <div class="account-detail mt-3">
                                                <label class="form-label text-muted small">
                                                    <i class="fas fa-list me-1"></i>交易限制配置
                                                </label>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="blacklist-display" id="blacklist-{{ loop.index0 }}">
                                                            <small class="text-muted">黑名单:</small>
                                                            <div class="text-muted small">加载中...</div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="whitelist-display" id="whitelist-{{ loop.index0 }}">
                                                            <small class="text-muted">白名单:</small>
                                                            <div class="text-muted small">加载中...</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="account-actions">
                                    <div class="dropdown">
                                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                            <i class="bi bi-three-dots"></i>
                                            操作
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#" onclick="editAccount('{{ account.name }}')">
                                                <i class="bi bi-pencil"></i> 编辑配置
                                            </a></li>
                                            <li><a class="dropdown-item" href="#" onclick="copyAccount('{{ account.name }}')">
                                                <i class="bi bi-files"></i> 复制账户
                                            </a></li>
                                            <li><a class="dropdown-item" href="#" onclick="testAccount('{{ account.name }}')">
                                                <i class="bi bi-wifi"></i> 测试API连接
                                            </a></li>
                                            <li><a class="dropdown-item" href="#" onclick="viewAccountConfig('{{ account.name }}')">
                                                <i class="bi bi-file-text"></i> 查看配置文件
                                            </a></li>
                                            <li><a class="dropdown-item" href="#" onclick="downloadAccount('{{ account.name }}')">
                                                <i class="bi bi-download"></i> 下载配置
                                            </a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item text-danger" href="#" onclick="deleteAccount('{{ account.name }}')">
                                                <i class="bi bi-trash"></i> 删除账户
                                            </a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-5">
                            <div class="mb-4">
                                <i class="bi bi-people" style="font-size: 4rem; color: #e5e7eb;"></i>
                            </div>
                            <h5 class="text-muted mb-3">暂无交易账户</h5>
                            <p class="text-muted mb-4">添加您的第一个交易账户开始使用系统</p>
                            <button class="modern-btn modern-btn-primary" onclick="showAddAccountModal()">
                                <i class="bi bi-plus-lg"></i>
                                添加第一个账户
                            </button>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 账户操作工具栏 -->
<div class="row g-4 mt-2">
    <div class="col-12">
        <div class="modern-card">
            <div class="modern-card-header">
                <h5 class="mb-0">
                    <i class="bi bi-tools"></i>
                    账户管理工具
                </h5>
            </div>
            <div class="modern-card-body">
                <div class="row g-3">
                    <div class="col-md-3">
                        <div class="text-center p-3 bg-light rounded-3">
                            <i class="bi bi-download text-primary" style="font-size: 2rem;"></i>
                            <h6 class="mt-2 mb-1">导出账户</h6>
                            <small class="text-muted">备份账户配置</small>
                            <div class="mt-2">
                                <button class="btn btn-sm btn-outline-primary" onclick="downloadAllAccounts()">
                                    <i class="bi bi-download"></i> 批量下载
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3 bg-light rounded-3">
                            <i class="bi bi-upload text-success" style="font-size: 2rem;"></i>
                            <h6 class="mt-2 mb-1">导入账户</h6>
                            <small class="text-muted">批量导入配置</small>
                            <div class="mt-2">
                                <button class="btn btn-sm btn-outline-success" onclick="showImportModal()">
                                    <i class="bi bi-upload"></i> 导入
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3 bg-light rounded-3">
                            <i class="bi bi-arrow-clockwise text-warning" style="font-size: 2rem;"></i>
                            <h6 class="mt-2 mb-1">批量测试</h6>
                            <small class="text-muted">测试所有连接</small>
                            <div class="mt-2">
                                <button class="btn btn-sm btn-outline-warning" onclick="testAllAccounts()">
                                    <i class="bi bi-wifi"></i> 测试
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3 bg-light rounded-3">
                            <i class="bi bi-shield-check text-info" style="font-size: 2rem;"></i>
                            <h6 class="mt-2 mb-1">安全检查</h6>
                            <small class="text-muted">验证账户安全</small>
                            <div class="mt-2">
                                <button class="btn btn-sm btn-outline-info" onclick="securityCheck()">
                                    <i class="bi bi-shield-check"></i> 检查
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加账户模态框 -->
<div class="modal fade" id="addAccountModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-person-plus"></i>
                    添加新账户
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="add-account-form">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="account-name" class="form-label">账户名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="account-name" name="name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="account-type" class="form-label">账户类型 <span class="text-danger">*</span></label>
                            <select class="form-select" id="account-type" name="type" required>
                                <option value="">请选择...</option>
                                <option value="demo">模拟账户</option>
                                <option value="live">实盘账户</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="account-server" class="form-label">服务器地址 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="account-server" name="server" required>
                        </div>
                        <div class="col-md-6">
                            <label for="account-login" class="form-label">登录账号 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="account-login" name="login" required>
                        </div>
                        <div class="col-md-6">
                            <label for="account-password" class="form-label">密码 <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="account-password" name="password" required>
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('account-password')">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="account-currency" class="form-label">基础货币</label>
                            <select class="form-select" id="account-currency" name="currency">
                                <option value="USD">USD - 美元</option>
                                <option value="EUR">EUR - 欧元</option>
                                <option value="GBP">GBP - 英镑</option>
                                <option value="JPY">JPY - 日元</option>
                                <option value="CNY">CNY - 人民币</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label for="account-description" class="form-label">账户描述</label>
                            <textarea class="form-control" id="account-description" name="description" rows="3" placeholder="可选：添加账户相关说明..."></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="modern-btn modern-btn-primary" onclick="saveAccount()">
                    <i class="bi bi-check-lg"></i>
                    保存账户
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑账户模态框 -->
<div class="modal fade" id="editAccountModal" tabindex="-1" aria-labelledby="editAccountModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editAccountModalLabel">编辑账户配置</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editAccountForm">
                    <div class="row">
                        <!-- 基本信息 -->
                        <div class="col-12">
                            <h6 class="text-muted mb-3">基本信息</h6>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit-backtest-name" class="form-label">策略名称</label>
                                <input type="text" class="form-control" id="edit-backtest-name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit-account-type" class="form-label">账户类型</label>
                                <select class="form-select" id="edit-account-type">
                                    <option value="普通账户">普通账户</option>
                                    <option value="统一账户">统一账户</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit-leverage" class="form-label">杠杆倍数</label>
                                <input type="number" class="form-control" id="edit-leverage" min="1" max="125" step="0.01">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit-kline-num" class="form-label">K线数量</label>
                                <input type="number" class="form-control" id="edit-kline-num" min="100" max="10000">
                            </div>
                        </div>

                        <!-- API配置 -->
                        <div class="col-12 mt-3">
                            <h6 class="text-muted mb-3">API配置</h6>
                        </div>
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="edit-api-key" class="form-label">API Key</label>
                                <input type="password" class="form-control" id="edit-api-key" placeholder="输入API密钥">
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="edit-secret" class="form-label">Secret Key</label>
                                <input type="password" class="form-control" id="edit-secret" placeholder="输入Secret密钥">
                            </div>
                        </div>

                        <!-- 策略配置 -->
                        <div class="col-12 mt-3">
                            <h6 class="text-muted mb-3">策略配置</h6>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit-strategy-name" class="form-label">资金平衡方式</label>
                                <select class="form-select" id="edit-strategy-name">
                                    <option value="FixedRatioStrategy">FixedRatioStrategy</option>
                                    <option value="EqualWeightStrategy">EqualWeightStrategy</option>
                                    <option value="CustomStrategy">CustomStrategy</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit-hold-period" class="form-label">资金平衡周期</label>
                                <select class="form-select" id="edit-hold-period">
                                    <option value="1H">1小时</option>
                                    <option value="6H">6小时</option>
                                    <option value="1D">1天</option>
                                    <option value="3D">3天</option>
                                    <option value="7D">7天</option>
                                </select>
                            </div>
                        </div>

                        <!-- 其他配置 -->
                        <div class="col-12 mt-3">
                            <h6 class="text-muted mb-3">其他配置</h6>
                        </div>
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="edit-wechat-url" class="form-label">企业微信Webhook URL</label>
                                <input type="url" class="form-control" id="edit-wechat-url" placeholder="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=...">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit-hour-offset" class="form-label">时间偏移</label>
                                <input type="text" class="form-control" id="edit-hour-offset" placeholder="0m">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit-bnb-value" class="form-label">BNB购买金额</label>
                                <input type="number" class="form-control" id="edit-bnb-value" min="1" max="100">
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="edit-bnb-burn">
                                <label class="form-check-label" for="edit-bnb-burn">
                                    启用BNB抵扣手续费
                                </label>
                            </div>
                        </div>

                        <!-- 统一账户保证金配置 -->
                        <div class="col-12 mt-3" id="margin-config-section" style="display: none;">
                            <h6 class="text-muted mb-3">
                                <i class="fas fa-coins me-2"></i>统一账户保证金配置
                            </h6>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                仅在账户类型为"统一账户"时生效，用于设置各交易对的保证金金额
                            </div>
                            <div id="margin-pairs-container">
                                <!-- 动态生成的保证金配置项 -->
                            </div>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="addMarginPair()">
                                <i class="fas fa-plus me-1"></i>添加交易对
                            </button>
                        </div>

                        <!-- 策略池配置编辑 -->
                        <div class="col-12 mt-3">
                            <h6 class="text-muted mb-3">
                                <i class="fas fa-code me-2"></i>策略池配置
                            </h6>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                请谨慎编辑策略池配置，确保Python语法正确。保存前会进行语法验证。
                            </div>
                            <div class="mb-3">
                                <label for="edit-strategy-pool" class="form-label">Strategy Pool 配置代码</label>
                                <textarea class="form-control font-monospace" id="edit-strategy-pool" rows="15"
                                          placeholder="strategy_pool = [...]" style="font-size: 13px; line-height: 1.4;"></textarea>
                                <div class="form-text">
                                    <i class="fas fa-lightbulb me-1"></i>
                                    支持完整的Python语法，包括注释和多行配置
                                </div>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="validateStrategyPool()">
                                    <i class="fas fa-check-circle me-1"></i>验证语法
                                </button>
                                <div id="syntax-validation-result" class="text-muted small"></div>
                            </div>
                        </div>

                        <!-- 黑白名单配置编辑 -->
                        <div class="col-12 mt-4">
                            <h6 class="text-muted mb-3">
                                <i class="fas fa-list me-2"></i>交易限制配置
                            </h6>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>黑名单:</strong> 永远不会交易的币种，不喜欢的币、异常的币<br>
                                <strong>白名单:</strong> 如果不为空，即只交易这些币，只在这些币当中进行选币（为空则不限制）<br>
                                <small class="text-muted">注意：交易对格式需要包含'-'符号，如 LUNA-USDT</small>
                            </div>

                            <div class="row">
                                <!-- 黑名单配置 -->
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">
                                            <i class="fas fa-ban me-1 text-danger"></i>黑名单 (拉黑名单)
                                        </label>
                                        <div id="blacklist-container">
                                            <!-- 动态生成的黑名单输入框 -->
                                        </div>
                                        <button type="button" class="btn btn-outline-danger btn-sm mt-2" onclick="addBlacklistPair()">
                                            <i class="fas fa-plus me-1"></i>添加黑名单币种
                                        </button>
                                    </div>
                                </div>

                                <!-- 白名单配置 -->
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">
                                            <i class="fas fa-check me-1 text-success"></i>白名单 (选币限制)
                                        </label>
                                        <div id="whitelist-container">
                                            <!-- 动态生成的白名单输入框 -->
                                        </div>
                                        <button type="button" class="btn btn-outline-success btn-sm mt-2" onclick="addWhitelistPair()">
                                            <i class="fas fa-plus me-1"></i>添加白名单币种
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveAccountConfig()">保存配置</button>
            </div>
        </div>
    </div>
</div>

<!-- 复制账户模态框 -->
<div class="modal fade" id="copyAccountModal" tabindex="-1" aria-labelledby="copyAccountModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="copyAccountModalLabel">复制账户配置</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="copyAccountForm">
                    <div class="mb-3">
                        <label for="copy-source-name" class="form-label">源账户名称</label>
                        <input type="text" class="form-control" id="copy-source-name" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="copy-new-name" class="form-label">新账户名称</label>
                        <input type="text" class="form-control" id="copy-new-name" required placeholder="输入新账户名称">
                        <div class="form-text">新账户将复制所有配置，但API密钥将被清空</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="confirmCopyAccount()">确认复制</button>
            </div>
        </div>
    </div>
</div>

<!-- 查看配置文件模态框 -->
<div class="modal fade" id="viewConfigModal" tabindex="-1" aria-labelledby="viewConfigModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewConfigModalLabel">查看配置文件</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <pre><code id="config-file-content" class="language-python"></code></pre>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="downloadCurrentConfig()">下载配置</button>
            </div>
        </div>
    </div>
</div>

<!-- 强制保存确认对话框 -->
<div class="modal fade" id="forceSaveConfirmModal" tabindex="-1" aria-labelledby="forceSaveConfirmModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="forceSaveConfirmModalLabel">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>确认强制保存
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>注意：</strong>当前配置未经过语法校验或校验失败！
                </div>
                <p class="mb-3">检测到以下情况之一：</p>
                <ul class="mb-3">
                    <li>策略池配置内容已修改但未重新验证语法</li>
                    <li>策略池配置语法校验失败</li>
                    <li>策略池配置从未进行过语法校验</li>
                </ul>
                <p class="text-danger mb-3">
                    <i class="fas fa-exclamation-circle me-1"></i>
                    <strong>强制保存可能导致配置错误，影响策略正常运行！</strong>
                </p>
                <p class="mb-0">是否确认要强制保存当前配置？</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>取消
                </button>
                <button type="button" class="btn btn-danger" onclick="confirmForceSave()">
                    <i class="fas fa-exclamation-triangle me-1"></i>确认强制保存
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Google验证提交函数
    function submitGoogleVerification(code) {
        console.log('submitGoogleVerification被调用，验证码:', code);

        // 提交验证码
        const csrfToken = $('meta[name=csrf-token]').attr('content') ||
                         $('input[name=csrf_token]').val();
        console.log('CSRF令牌:', csrfToken);

        const requestData = { google_code: code };
        console.log('请求数据:', requestData);

        $.ajax({
            url: '/auth/api/google-verify',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(requestData),
            beforeSend: function(xhr) {
                console.log('设置请求头');
                // 添加CSRF令牌
                if (csrfToken) {
                    xhr.setRequestHeader("X-CSRFToken", csrfToken);
                    console.log('已添加CSRF令牌到请求头');
                } else {
                    console.warn('未找到CSRF令牌');
                }
            },
            success: function(response) {
                if (response.success) {
                    console.log('Google验证成功，关闭模态框');
                    $('#googleAuthModal').modal('hide');

                    // 验证成功后，重试原始请求或重新加载页面数据
                    if (window.pendingRequest) {
                        console.log('重试原始请求');
                        // 清除待处理请求
                        window.pendingRequest = null;
                    }

                    // 重新加载账户详情
                    setTimeout(() => {
                        loadAccountDetails();
                    }, 500);
                } else {
                    console.error('Google验证失败:', response.message);
                    alert('验证失败: ' + (response.message || '未知错误'));
                }
            },
            error: function(xhr, status, error) {
                console.error('Google验证请求失败:', xhr.status, xhr.responseText);
                let errorMessage = '验证请求失败，请重试';

                if (xhr.status === 401) {
                    errorMessage = '会话已过期，请重新登录';
                    // 可以考虑重定向到登录页面
                    setTimeout(() => {
                        window.location.href = '/auth/login';
                    }, 2000);
                } else if (xhr.status === 403) {
                    errorMessage = 'CSRF验证失败，请刷新页面重试';
                }

                alert(errorMessage);
            }
        });
    }

    // 账户管理页面专用的Google认证处理函数
    function handleGoogleAuthRequired(originalRequest) {
        console.log('账户管理页面检测到Google认证要求，调用处理函数');
        console.log('handleGoogleAuthRequired函数类型:', typeof handleGoogleAuthRequired);

        // 检查是否已经有Google验证模态框在显示
        if ($('#googleAuthModal').length > 0 && $('#googleAuthModal').hasClass('show')) {
            console.log('Google验证模态框已经在显示，忽略重复调用');
            return;
        }

        console.log('调用handleGoogleAuthRequired函数');

        // 保存原始请求信息
        window.pendingRequest = originalRequest;

        // 检查是否有标准的showGoogleAuthModal函数
        if (typeof showGoogleAuthModal === 'function') {
            console.log('使用标准的showGoogleAuthModal函数');
            showGoogleAuthModal();
        } else {
            console.log('showGoogleAuthModal函数不存在，创建备用Google验证模态框');
            // 备用处理：创建简化的Google验证模态框（不跳转登录页面）
            createBackupGoogleAuthModal();
        }
    }

    // 备用Google验证模态框创建函数
    function createBackupGoogleAuthModal() {
        console.log('创建备用Google验证模态框');

        // 再次检查是否已经有模态框存在
        if ($('#googleAuthModal').length > 0) {
            console.log('模态框已存在，直接显示');
            $('#googleAuthModal').modal('show');
            return;
        }

        // 创建简化的Google验证模态框
        const modalHtml = `
            <div class="modal fade" id="googleAuthModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="static">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title">
                                <i class="bi bi-shield-check me-2"></i>
                                Google 二次验证
                            </h5>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>
                                您的Google验证已过期，请重新验证以继续操作
                            </div>
                            <form id="googleVerifyForm">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="modal_google_code"
                                           placeholder="000000" maxlength="6" pattern="[0-9]{6}" required>
                                    <label for="modal_google_code">
                                        <i class="bi bi-key me-2"></i>6位验证码
                                    </label>
                                </div>
                                <button type="submit" class="btn btn-primary w-100">
                                    <span class="spinner-border spinner-border-sm me-2" style="display: none;"></span>
                                    验证
                                </button>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" onclick="cancelGoogleAuth()">
                                <i class="bi bi-x-circle me-1"></i>
                                取消
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的模态框
        $('#googleAuthModal').remove();

        // 添加新模态框
        $('body').append(modalHtml);

        // 初始化模态框
        initializeBackupGoogleAuthModal();

        // 显示模态框
        $('#googleAuthModal').modal('show');
    }

    // 初始化备用Google验证模态框
    function initializeBackupGoogleAuthModal() {
        console.log('初始化备用Google验证模态框');

        // 验证码输入格式化
        $('#modal_google_code').on('input', function() {
            this.value = this.value.replace(/\D/g, '').substring(0, 6);
        });

        // 表单提交处理
        $('#googleVerifyForm').on('submit', function(e) {
            e.preventDefault();
            console.log('备用模态框：Google验证表单提交');

            const code = $('#modal_google_code').val().trim();
            console.log('获取到的验证码:', code);

            if (code.length !== 6) {
                console.log('验证码长度不正确:', code.length);
                alert('请输入6位验证码');
                return;
            }

            // 调用验证函数
            submitBackupGoogleVerification(code);
        });

        // 回车键提交
        $('#modal_google_code').on('keypress', function(e) {
            if (e.which === 13) {
                console.log('回车键提交验证码');
                e.preventDefault();
                $('#googleVerifyForm').trigger('submit');
            }
        });
    }

    // 备用Google验证提交函数
    function submitBackupGoogleVerification(code) {
        console.log('提交备用Google验证，验证码:', code);

        const btn = $('#googleVerifyForm button[type="submit"]');
        const spinner = btn.find('.spinner-border');

        // 显示加载状态
        btn.prop('disabled', true);
        spinner.show();

        // 获取CSRF令牌
        const csrfToken = $('meta[name="csrf-token"]').attr('content');
        console.log('CSRF令牌:', csrfToken);

        const requestData = { google_code: code };
        console.log('请求数据:', requestData);

        $.ajax({
            url: '/auth/verify-google',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(requestData),
            headers: {
                'X-CSRFToken': csrfToken
            },
            success: function(response) {
                console.log('Google验证成功:', response);
                if (response.success) {
                    console.log('Google验证成功，关闭模态框');
                    $('#googleAuthModal').modal('hide');

                    // 验证成功后，重试原始请求或重新加载页面数据
                    if (window.pendingRequest) {
                        console.log('重试原始请求');
                        retryPendingRequest();
                    } else {
                        console.log('重新加载页面数据');
                        location.reload();
                    }
                } else {
                    console.log('验证失败:', response.error);
                    alert(response.error || '验证失败');
                    resetBackupLoadingState();
                    $('#modal_google_code').val('').focus();
                }
            },
            error: function(xhr, status, error) {
                console.error('Google验证请求失败:', xhr, status, error);
                let errorMsg = '验证失败';
                if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMsg = xhr.responseJSON.error;
                } else if (xhr.status === 400) {
                    errorMsg = '验证码错误';
                } else if (xhr.status === 401) {
                    errorMsg = '会话已过期，请重新登录';
                    // 如果是401错误，说明session也过期了，需要跳转登录页面
                    window.location.href = '/auth/login';
                    return;
                }

                alert(errorMsg);
                resetBackupLoadingState();
                $('#modal_google_code').val('').focus();
            }
        });
    }

    // 重置备用模态框加载状态
    function resetBackupLoadingState() {
        const btn = $('#googleVerifyForm button[type="submit"]');
        const spinner = btn.find('.spinner-border');

        btn.prop('disabled', false);
        spinner.hide();
    }

    // 取消Google验证
    function cancelGoogleAuth() {
        console.log('取消Google验证');
        $('#googleAuthModal').modal('hide');
        // 可以选择重定向到主页或其他页面
        // window.location.href = '/';
    }

    // 重试待处理的请求
    function retryPendingRequest() {
        if (window.pendingRequest) {
            console.log('重试待处理的请求:', window.pendingRequest);

            // 重新发送原始请求
            $.ajax(window.pendingRequest).done(function() {
                console.log('重试请求成功');
                // 清除待处理请求
                window.pendingRequest = null;
            }).fail(function(xhr) {
                console.log('重试请求失败:', xhr);
                if (xhr.status === 403) {
                    // 如果还是403，可能需要再次验证
                    console.log('重试后仍需要Google验证');
                    handleGoogleAuthRequired(window.pendingRequest);
                } else {
                    // 其他错误，清除待处理请求
                    window.pendingRequest = null;
                    location.reload();
                }
            });
        } else {
            console.log('没有待处理的请求，重新加载页面');
            location.reload();
        }
    }



    // 统一的AJAX错误处理函数
    function handleAjaxError(xhr, status, error, customHandler) {
        if (xhr.status === 403) {
            try {
                const response = JSON.parse(xhr.responseText);
                if (response.google_auth_required) {
                    console.log('账户管理页面检测到Google认证要求，调用处理函数');
                    console.log('handleGoogleAuthRequired函数类型:', typeof handleGoogleAuthRequired);
                    if (typeof handleGoogleAuthRequired === 'function') {
                        console.log('调用handleGoogleAuthRequired函数');
                        handleGoogleAuthRequired({
                            url: xhr.responseURL || this.url,
                            method: this.type || 'GET',
                            data: this.data
                        });
                        return; // 阻止执行自定义错误处理
                    } else {
                        console.error('handleGoogleAuthRequired函数未定义或不是函数');
                    }
                }
            } catch (e) {
                console.log('解析403响应失败:', e);
            }
        }

        // 执行自定义错误处理
        if (customHandler && typeof customHandler === 'function') {
            customHandler.call(this, xhr, status, error);
        }
    }

    // 主动的Google认证检查函数
    function startGoogleAuthCheck() {
        console.log('启动主动的Google认证检查...');

        // 立即检查一次
        checkGoogleAuthStatus();

        // 每30秒检查一次Google认证状态
        setInterval(checkGoogleAuthStatus, 30000);

        // 页面可见性变化时也检查
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                console.log('页面重新可见，检查Google认证状态');
                checkGoogleAuthStatus();
            }
        });
    }

    // 检查Google认证状态
    function checkGoogleAuthStatus() {
        // 如果已经有Google验证模态框在显示，跳过检查
        if ($('#googleAuthModal').length > 0 && $('#googleAuthModal').hasClass('show')) {
            console.log('Google验证模态框已在显示，跳过状态检查');
            return;
        }

        $.ajax({
            url: '/auth/api/session-status',
            method: 'GET',
            timeout: 5000,
            success: function(response) {
                console.log('会话状态检查结果:', response);
                if (!response.valid) {
                    console.log('会话已过期，跳转登录页面');
                    window.location.href = '/auth/login';
                } else if (response.google_auth_required) {
                    console.log('检测到Google认证要求，主动弹出验证框');
                    handleGoogleAuthRequired({
                        url: window.location.href,
                        method: 'GET',
                        data: null
                    });
                }
            },
            error: function(xhr, status, error) {
                console.log('会话状态检查失败:', status, error);
                // 如果是401错误，说明会话过期
                if (xhr.status === 401) {
                    console.log('会话已过期，跳转登录页面');
                    window.location.href = '/auth/login';
                }
                // 其他错误暂时忽略，避免影响用户体验
            }
        });
    }

    // 页面加载时初始化
    $(document).ready(function() {
        // 检查auth.js是否正确加载
        console.log('检查auth.js加载状态:');
        console.log('- handleGoogleAuthRequired:', typeof handleGoogleAuthRequired);
        console.log('- showGoogleAuthModal:', typeof showGoogleAuthModal);
        console.log('- initializeCSRFProtection:', typeof initializeCSRFProtection);

        // 初始化认证系统（如果存在）
        if (typeof initializeAuth === 'function') {
            console.log('🔧 账户页面手动初始化认证系统...');
            initializeAuth();
            window.authInitialized = true;
            console.log('✅ 账户页面认证系统初始化完成');
        } else {
            console.log('❌ initializeAuth函数不存在，等待auth.js自动初始化');
        }

        // 启动主动的Google认证检查
        startGoogleAuthCheck();

        updateAccountStats();
        loadAccountDetails();
        initializeEventDelegation();
    });

    // 直接的onclick处理函数（备用方案）
    function handleSaveButtonClick(accountName, accountIndex) {
        if (accountName && !isNaN(accountIndex)) {
            saveAccountName(accountName, accountIndex);
        } else {
            showMessage('保存失败：参数无效', 'danger');
        }
    }

    // 初始化事件委托
    function initializeEventDelegation() {
        // 使用事件委托处理保存按钮点击
        $(document).on('click', 'button[id^="save-name-btn-"]', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const buttonId = $(this).attr('id');
            const accountIndex = parseInt(buttonId.replace('save-name-btn-', ''));
            const currentAccountName = $(this).attr('data-account-name');

            if (currentAccountName && !isNaN(accountIndex)) {
                if (typeof saveAccountName === 'function') {
                    saveAccountName(currentAccountName, accountIndex);
                }
            } else {
                showMessage('保存失败：无法获取账户信息', 'danger');
            }
        });

        // 使用事件委托处理取消按钮点击
        $(document).on('click', 'button[id^="cancel-name-btn-"], [id^="cancel-name-btn-"]', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const buttonId = $(this).attr('id');
            const accountIndex = parseInt(buttonId.replace('cancel-name-btn-', ''));
            cancelEditAccountName(accountIndex);
        });

        // 延时初始化鼠标事件处理
        setTimeout(() => {
            const saveButtons = $('[id^="save-name-btn-"]');

            // 三重保护机制：mousedown/mouseup/延时触发
            let currentMouseDownButton = null;
            let mouseDownTime = 0;

            saveButtons.each(function(index, button) {
                $(button).on('mousedown', function(e) {
                    currentMouseDownButton = this;
                    mouseDownTime = Date.now();

                    // 延时触发机制：200ms后如果没有mouseup就强制触发
                    setTimeout(() => {
                        if (currentMouseDownButton === this) {
                            const buttonId = this.id;
                            const accountIndex = parseInt(buttonId.replace('save-name-btn-', ''));
                            const currentAccountName = $(this).attr('data-account-name');

                            if (currentAccountName && !isNaN(accountIndex)) {
                                saveAccountName(currentAccountName, accountIndex);
                                currentMouseDownButton = null;
                            }
                        }
                    }, 200);
                });

                $(button).on('mouseup', function(e) {
                    if (currentMouseDownButton === this) {
                        const buttonId = this.id;
                        const accountIndex = parseInt(buttonId.replace('save-name-btn-', ''));
                        const currentAccountName = $(this).attr('data-account-name');

                        if (currentAccountName && !isNaN(accountIndex)) {
                            saveAccountName(currentAccountName, accountIndex);
                        }

                        currentMouseDownButton = null;
                    }
                });
            });

            // 全局mouseup监听，防止鼠标在按钮外释放
            $(document).on('mouseup', function(e) {
                if (currentMouseDownButton) {
                    const clickDuration = Date.now() - mouseDownTime;

                    if (clickDuration < 1000 && clickDuration > 0) {
                        const buttonId = currentMouseDownButton.id;
                        const accountIndex = parseInt(buttonId.replace('save-name-btn-', ''));
                        const currentAccountName = $(currentMouseDownButton).attr('data-account-name');

                        if (currentAccountName && !isNaN(accountIndex)) {
                            saveAccountName(currentAccountName, accountIndex);
                        }
                    }

                    currentMouseDownButton = null;
                }
            });
        }, 1000);
    }
    
    // 更新账户统计
    function updateAccountStats() {
        const accounts = {{ accounts|tojson if accounts else '[]' }};
        const totalAccounts = accounts.length;
        const configuredAccounts = accounts.filter(acc => acc.api_configured).length;
        const disabledAccounts = accounts.filter(acc => !acc.enabled).length;
        const activeAccounts = totalAccounts - disabledAccounts;

        // 更新各个统计指标
        document.getElementById('configured-accounts').textContent = configuredAccounts;
        document.getElementById('active-accounts').textContent = activeAccounts;
        document.getElementById('disabled-accounts').textContent = disabledAccounts;

        // 更新统计卡片（按新的顺序）
        const statCards = document.querySelectorAll('.stat-value');
        if (statCards.length >= 4) {
            statCards[0].textContent = totalAccounts;        // 总账户数
            statCards[1].textContent = configuredAccounts;   // 配置API账号数
            statCards[2].textContent = activeAccounts;       // 活跃账户数
            statCards[3].textContent = disabledAccounts;     // 禁用账户数
        }
    }

    // 加载账户详细信息
    function loadAccountDetails() {
        const accounts = {{ accounts|tojson if accounts else '[]' }};

        accounts.forEach((account, index) => {
            // 获取账户详细配置信息
            $.ajax({
                url: `/api/accounts/${encodeURIComponent(account.name)}`,
                method: 'GET',
                success: function(response) {
                    // 更新策略名称（backtest_name）
                    const strategyNameElement = document.getElementById(`strategy-name-${index}`);
                    if (strategyNameElement && response.backtest_name) {
                        strategyNameElement.textContent = response.backtest_name;
                    }

                    // 更新K线数量
                    const klineNumElement = document.getElementById(`kline-num-${index}`);
                    if (klineNumElement && response.get_kline_num) {
                        klineNumElement.textContent = response.get_kline_num;
                    }

                    // 显示统一账户保证金配置
                    if (response.account_config?.account_type === '统一账户') {
                        const marginDisplayElement = document.getElementById(`margin-display-${index}`);
                        if (marginDisplayElement) {
                            marginDisplayElement.style.display = 'block';
                            displayMarginConfig(response.account_config.coin_margin, index);
                        }
                    }

                    // 显示黑白名单配置
                    displayBlackWhitelistConfig(response.black_list, response.white_list, index);
                },
                error: function(xhr, status, error) {
                    // 使用统一错误处理，支持Google认证过期处理
                    handleAjaxError.call(this, xhr, status, error, function() {
                        // 静默处理加载失败
                    });
                }
            });
        });
    }
    
    // 显示添加账户模态框
    function showAddAccountModal() {
        const modal = new bootstrap.Modal(document.getElementById('addAccountModal'));
        modal.show();
    }
    
    // 保存账户
    function saveAccount() {
        const form = document.getElementById('add-account-form');
        const formData = new FormData(form);
        const account = {};
        
        for (let [key, value] of formData.entries()) {
            account[key] = value;
        }
        
        // 添加创建时间
        account.created = new Date().toLocaleString('zh-CN');
        
        $.ajax({
            url: '/api/accounts',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(account),
            success: function(response) {
                if (response.success) {
                    showMessage('账户添加成功', 'success');
                    bootstrap.Modal.getInstance(document.getElementById('addAccountModal')).hide();
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showMessage('添加失败: ' + response.message, 'danger');
                }
            },
            error: function() {
                showMessage('添加请求失败', 'danger');
            }
        });
    }
    
    // 编辑账户
    function editAccount(accountName) {
        // 获取账户配置信息
        $.ajax({
            url: `/api/accounts/${encodeURIComponent(accountName)}`,
            method: 'GET',
            success: function(response) {
                if (response.error) {
                    showMessage(`获取账户配置失败: ${response.error}`, 'error');
                    return;
                }

                // 填充表单数据
                $('#edit-backtest-name').val(response.backtest_name || '');
                $('#edit-account-type').val(response.account_config?.account_type || '普通账户');
                $('#edit-leverage').val(response.leverage || 1);
                $('#edit-kline-num').val(response.get_kline_num || 5000);
                $('#edit-api-key').val(response.account_config?.apiKey || '');
                $('#edit-secret').val(response.account_config?.secret || '');
                $('#edit-strategy-name').val(response.strategy_config?.name || 'FixedRatioStrategy');
                $('#edit-hold-period').val(response.strategy_config?.hold_period || '1H');
                $('#edit-wechat-url').val(response.account_config?.wechat_webhook_url || '');
                $('#edit-hour-offset').val(response.account_config?.hour_offset || '0m');
                $('#edit-bnb-value').val(response.account_config?.buy_bnb_value || 11);
                $('#edit-bnb-burn').prop('checked', response.account_config?.if_use_bnb_burn || false);

                // 处理统一账户保证金配置
                const accountType = response.account_config?.account_type || '普通账户';
                toggleMarginConfig(accountType);
                if (accountType === '统一账户' && response.account_config?.coin_margin) {
                    loadMarginConfig(response.account_config.coin_margin);
                }

                // 加载策略池配置
                loadStrategyPoolConfig(accountName);

                // 加载黑白名单配置
                loadBlackWhitelistConfig(response);

                // 存储当前编辑的账户名
                $('#editAccountModal').data('account-name', accountName);

                // 设置策略池配置变化监听器
                setupStrategyPoolChangeListener();

                // 显示模态框
                $('#editAccountModal').modal('show');
            },
            error: function() {
                showMessage('获取账户配置失败', 'error');
            }
        });
    }

    // 保存账户配置
    function saveAccountConfig() {
        const accountName = $('#editAccountModal').data('account-name');

        // 收集表单数据
        const configData = {
            backtest_name: $('#edit-backtest-name').val(),
            account_type: $('#edit-account-type').val(),
            leverage: parseFloat($('#edit-leverage').val()) || 1,
            get_kline_num: parseInt($('#edit-kline-num').val()) || 5000,
            apiKey: $('#edit-api-key').val(),
            secret: $('#edit-secret').val(),
            strategy_name: $('#edit-strategy-name').val(),
            hold_period: $('#edit-hold-period').val(),
            wechat_webhook_url: $('#edit-wechat-url').val(),
            hour_offset: $('#edit-hour-offset').val(),
            buy_bnb_value: parseInt($('#edit-bnb-value').val()) || 11,
            if_use_bnb_burn: $('#edit-bnb-burn').is(':checked')
        };

        // 添加统一账户保证金配置
        if (configData.account_type === '统一账户') {
            configData.coin_margin = collectMarginConfig();
        }

        // 添加策略池配置
        const strategyPoolCode = $('#edit-strategy-pool').val();
        if (strategyPoolCode.trim()) {
            configData.strategy_pool_code = strategyPoolCode;
        }

        // 添加黑白名单配置
        configData.black_list = collectBlacklistConfig();
        configData.white_list = collectWhitelistConfig();

        // 表单验证
        if (!configData.backtest_name) {
            showMessage('请输入策略名称', 'error');
            return;
        }

        // 检查策略池配置的语法校验状态
        if (strategyPoolCode.trim()) {
            const needsConfirmation = checkIfNeedsValidationConfirmation(strategyPoolCode);

            if (needsConfirmation) {
                // 存储配置数据以便确认后使用
                window.pendingConfigData = configData;
                window.pendingAccountName = accountName;

                // 显示强制保存确认对话框
                new bootstrap.Modal(document.getElementById('forceSaveConfirmModal')).show();
                return;
            }
        }

        // 直接保存配置（已通过验证或无需验证）
        performSaveAccountConfig(configData, accountName);
    }

    // 检查是否需要验证确认
    function checkIfNeedsValidationConfirmation(currentCode) {
        // 如果从未验证过
        if (!strategyPoolValidationStatus.validated) {
            return true;
        }

        // 如果验证失败
        if (!strategyPoolValidationStatus.isValid) {
            return true;
        }

        // 如果代码已修改但未重新验证
        if (strategyPoolValidationStatus.lastValidatedCode !== currentCode) {
            return true;
        }

        return false;
    }

    // 确认强制保存
    function confirmForceSave() {
        // 关闭确认对话框
        bootstrap.Modal.getInstance(document.getElementById('forceSaveConfirmModal')).hide();

        // 执行保存
        if (window.pendingConfigData && window.pendingAccountName) {
            performSaveAccountConfig(window.pendingConfigData, window.pendingAccountName);

            // 清理临时数据
            delete window.pendingConfigData;
            delete window.pendingAccountName;
        }
    }

    // 执行实际的保存操作
    function performSaveAccountConfig(configData, accountName) {
        // 保存配置
        $.ajax({
            url: `/api/accounts/${encodeURIComponent(accountName)}`,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(configData),
            success: function(response) {
                if (response.success) {
                    showMessage('账户配置保存成功', 'success');
                    $('#editAccountModal').modal('hide');
                    // 刷新页面
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showMessage(`保存失败: ${response.message}`, 'error');
                }
            },
            error: function() {
                showMessage('保存账户配置失败', 'error');
            }
        });
    }

    // 复制账户
    function copyAccount(accountName) {
        // 设置源账户名称
        $('#copy-source-name').val(accountName);
        $('#copy-new-name').val('');

        // 存储源账户名
        $('#copyAccountModal').data('source-account', accountName);

        // 显示模态框
        $('#copyAccountModal').modal('show');
    }

    // 确认复制账户
    function confirmCopyAccount() {
        const sourceAccount = $('#copyAccountModal').data('source-account');
        const newAccountName = $('#copy-new-name').val().trim();

        if (!newAccountName) {
            showMessage('请输入新账户名称', 'error');
            return;
        }

        // 验证账户名称格式
        if (!/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/.test(newAccountName)) {
            showMessage('账户名称只能包含字母、数字、下划线和中文字符', 'error');
            return;
        }

        // 使用新的复制账户API
        $.ajax({
            url: `/api/accounts/${encodeURIComponent(sourceAccount)}/copy`,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                new_name: newAccountName
            }),
            success: function(response) {
                if (response.success) {
                    showMessage('账户复制成功', 'success');
                    $('#copyAccountModal').modal('hide');
                    // 刷新页面
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showMessage(`复制失败: ${response.message}`, 'error');
                }
            },
            error: function() {
                showMessage('复制账户失败', 'error');
            }
        });
    }

    // 测试API连接
    function testAccount(accountName) {
        showMessage('正在测试API连接...', 'info');

        $.ajax({
            url: `/api/accounts/${accountName}/test`,
            method: 'POST',
            success: function(response) {
                if (response.success) {
                    let message = 'API连接测试成功';
                    if (response.details) {
                        const details = response.details;
                        message += `<br><small>
                            账户类型: ${details.account_type}<br>
                            交易权限: ${details.can_trade ? '✓' : '✗'}<br>
                            提现权限: ${details.can_withdraw ? '✓' : '✗'}<br>
                            充值权限: ${details.can_deposit ? '✓' : '✗'}<br>
                            资产数量: ${details.balance_count}
                        </small>`;
                    }
                    showMessage(message, 'success');
                } else {
                    showMessage(`API连接测试失败: ${response.message}`, 'error');
                }
            },
            error: function(xhr) {
                let errorMsg = 'API连接测试失败';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMsg += `: ${xhr.responseJSON.message}`;
                }
                showMessage(errorMsg, 'error');
            }
        });
    }

    // 查看配置文件
    function viewAccountConfig(accountName) {
        // 获取配置文件内容
        $.ajax({
            url: `/api/accounts/${encodeURIComponent(accountName)}/config-file`,
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    // 设置配置文件内容
                    $('#config-file-content').text(response.content);

                    // 存储当前查看的账户名
                    $('#viewConfigModal').data('account-name', accountName);

                    // 更新模态框标题
                    $('#viewConfigModalLabel').text(`查看配置文件 - ${accountName}`);

                    // 显示模态框
                    $('#viewConfigModal').modal('show');

                    // 应用语法高亮
                    if (typeof Prism !== 'undefined') {
                        Prism.highlightElement(document.getElementById('config-file-content'));
                    }
                } else {
                    showMessage(`获取配置文件失败: ${response.message}`, 'error');
                }
            },
            error: function() {
                showMessage('获取配置文件失败', 'error');
            }
        });
    }

    // 下载当前查看的配置文件
    function downloadCurrentConfig() {
        const accountName = $('#viewConfigModal').data('account-name');
        if (accountName) {
            downloadAccount(accountName);
        }
    }

    // 下载配置
    function downloadAccount(accountName) {
        showMessage('正在准备下载...', 'info');

        // 验证配置文件是否存在
        $.ajax({
            url: `/api/accounts/${encodeURIComponent(accountName)}/config-file`,
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    // 配置文件存在，开始下载
                    showMessage('开始下载配置文件...', 'success');
                    window.location.href = `/api/accounts/${encodeURIComponent(accountName)}/download`;
                } else {
                    showMessage(`下载失败: ${response.message}`, 'error');
                }
            },
            error: function() {
                showMessage('验证配置文件失败', 'error');
            }
        });
    }

    // 批量下载所有账户配置
    function downloadAllAccounts() {
        const accounts = {{ accounts|tojson if accounts else '[]' }};
        if (accounts.length === 0) {
            showMessage('没有可下载的账户配置', 'warning');
            return;
        }

        showMessage(`正在准备下载 ${accounts.length} 个账户配置...`, 'info');

        let downloadCount = 0;
        let failedCount = 0;

        accounts.forEach((account, index) => {
            setTimeout(() => {
                $.ajax({
                    url: `/api/accounts/${encodeURIComponent(account.name)}/download`,
                    method: 'GET',
                    xhrFields: {
                        responseType: 'blob'
                    },
                    success: function(data, status, xhr) {
                        // 创建下载链接
                        const blob = new Blob([data]);
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `${account.name}.py`;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);

                        downloadCount++;
                        updateBatchDownloadProgress(downloadCount, failedCount, accounts.length);
                    },
                    error: function() {
                        failedCount++;
                        updateBatchDownloadProgress(downloadCount, failedCount, accounts.length);
                    }
                });
            }, index * 500); // 每个下载间隔500ms
        });
    }

    // 更新批量下载进度
    function updateBatchDownloadProgress(downloaded, failed, total) {
        const completed = downloaded + failed;
        if (completed === total) {
            if (failed === 0) {
                showMessage(`批量下载完成！成功下载 ${downloaded} 个配置文件`, 'success');
            } else {
                showMessage(`批量下载完成！成功: ${downloaded}, 失败: ${failed}`, 'warning');
            }
        } else {
            showMessage(`下载进度: ${completed}/${total}`, 'info');
        }
    }
    
    // 删除账户
    function deleteAccount(accountName) {
        if (confirm(`确定要删除交易账户 "${accountName}" 吗？此操作将删除配置文件，不可撤销。`)) {
            $.ajax({
                url: `/api/accounts/${accountName}`,
                method: 'DELETE',
                success: function(response) {
                    if (response.success) {
                        showMessage('交易账户删除成功', 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showMessage('删除失败: ' + response.message, 'danger');
                    }
                },
                error: function() {
                    showMessage('删除请求失败', 'danger');
                }
            });
        }
    }
    
    // 导出账户
    function exportAccounts() {
        showMessage('导出账户功能开发中...', 'info');
    }
    
    // 显示导入模态框
    function showImportModal() {
        showMessage('导入账户功能开发中...', 'info');
    }
    
    // 批量测试所有账户
    function testAllAccounts() {
        showMessage('正在批量测试所有账户连接...', 'info');
    }
    
    // 安全检查
    function securityCheck() {
        showMessage('正在进行安全检查...', 'info');
    }
    
    // 切换密码显示
    function togglePassword(inputId) {
        const input = document.getElementById(inputId);
        const icon = input.nextElementSibling.querySelector('i');
        
        if (input.type === 'password') {
            input.type = 'text';
            icon.className = 'bi bi-eye-slash';
        } else {
            input.type = 'password';
            icon.className = 'bi bi-eye';
        }
    }

    // ========== 统一账户保证金管理功能 ==========

    // 切换保证金配置显示
    function toggleMarginConfig(accountType) {
        const marginSection = $('#margin-config-section');
        if (accountType === '统一账户') {
            marginSection.show();
        } else {
            marginSection.hide();
        }
    }

    // 监听账户类型变化
    $('#edit-account-type').on('change', function() {
        toggleMarginConfig($(this).val());
        if ($(this).val() !== '统一账户') {
            $('#margin-pairs-container').empty();
        }
    });

    // 添加保证金交易对
    function addMarginPair(symbol = '', amount = '') {
        const pairId = 'margin-pair-' + Date.now();
        const pairHtml = `
            <div class="row mb-2 margin-pair" id="${pairId}">
                <div class="col-md-5">
                    <input type="text" class="form-control" placeholder="交易对 (如: ETHUSDT)"
                           value="${symbol}" onchange="validateTradingPair(this)">
                </div>
                <div class="col-md-5">
                    <input type="number" class="form-control" placeholder="保证金金额 (USDT)"
                           value="${amount}" min="1" step="0.01">
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-outline-danger btn-sm"
                            onclick="removeMarginPair('${pairId}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
        $('#margin-pairs-container').append(pairHtml);
    }

    // 删除保证金交易对
    function removeMarginPair(pairId) {
        $('#' + pairId).remove();
    }

    // 验证交易对格式
    function validateTradingPair(input) {
        const value = input.value.toUpperCase();
        const pattern = /^[A-Z]+USDT$/;
        if (value && !pattern.test(value)) {
            $(input).addClass('is-invalid');
            showMessage('交易对格式错误，应为类似 ETHUSDT 的格式', 'error');
        } else {
            $(input).removeClass('is-invalid');
            input.value = value;
        }
    }

    // 加载保证金配置
    function loadMarginConfig(coinMargin) {
        $('#margin-pairs-container').empty();
        if (coinMargin && typeof coinMargin === 'object') {
            Object.entries(coinMargin).forEach(([symbol, amount]) => {
                addMarginPair(symbol, amount);
            });
        }
    }

    // 收集保证金配置
    function collectMarginConfig() {
        const marginConfig = {};
        $('.margin-pair').each(function() {
            const symbol = $(this).find('input[type="text"]').val().trim();
            const amount = parseFloat($(this).find('input[type="number"]').val());
            if (symbol && amount > 0) {
                marginConfig[symbol] = amount;
            }
        });
        return marginConfig;
    }

    // ========== 策略池配置编辑功能 ==========

    // 全局变量跟踪语法校验状态
    let strategyPoolValidationStatus = {
        validated: false,
        isValid: false,
        lastValidatedCode: ''
    };

    // 加载策略池配置
    function loadStrategyPoolConfig(accountName) {
        $.ajax({
            url: `/api/accounts/${encodeURIComponent(accountName)}/strategy-pool`,
            method: 'GET',
            success: function(response) {
                if (response.success && response.strategy_pool_code) {
                    $('#edit-strategy-pool').val(response.strategy_pool_code);
                    // 重置验证状态
                    resetValidationStatus();
                }
            },
            error: function() {
                // 使用默认配置
            }
        });
    }

    // 重置验证状态
    function resetValidationStatus() {
        strategyPoolValidationStatus = {
            validated: false,
            isValid: false,
            lastValidatedCode: ''
        };
        $('#syntax-validation-result').html('');
    }

    // 监听策略池配置内容变化
    function setupStrategyPoolChangeListener() {
        // 先移除之前的监听器，避免重复绑定
        $('#edit-strategy-pool').off('input.strategyPoolValidation');

        // 绑定新的监听器
        $('#edit-strategy-pool').on('input.strategyPoolValidation', function() {
            const currentCode = $(this).val();
            // 如果内容发生变化且之前已验证过，则重置验证状态
            if (strategyPoolValidationStatus.validated &&
                strategyPoolValidationStatus.lastValidatedCode !== currentCode) {
                strategyPoolValidationStatus.validated = false;
                strategyPoolValidationStatus.isValid = false;
                $('#syntax-validation-result').html('<span class="text-muted"><i class="fas fa-info-circle me-1"></i>配置已修改，建议重新验证语法</span>');
            }
        });
    }

    // 验证策略池语法
    function validateStrategyPool() {
        const code = $('#edit-strategy-pool').val();
        if (!code.trim()) {
            $('#syntax-validation-result').html('<span class="text-warning">请输入策略池配置代码</span>');
            strategyPoolValidationStatus = {
                validated: true,
                isValid: false,
                lastValidatedCode: ''
            };
            return false;
        }

        $.ajax({
            url: '/api/validate-strategy-pool',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ code: code }),
            success: function(response) {
                if (response.valid) {
                    $('#syntax-validation-result').html('<span class="text-success"><i class="fas fa-check-circle me-1"></i>语法验证通过</span>');
                    strategyPoolValidationStatus = {
                        validated: true,
                        isValid: true,
                        lastValidatedCode: code
                    };
                } else {
                    $('#syntax-validation-result').html(`<span class="text-danger"><i class="fas fa-times-circle me-1"></i>语法错误: ${response.error}</span>`);
                    strategyPoolValidationStatus = {
                        validated: true,
                        isValid: false,
                        lastValidatedCode: code
                    };
                }
            },
            error: function() {
                $('#syntax-validation-result').html('<span class="text-danger">验证失败，请检查网络连接</span>');
                strategyPoolValidationStatus = {
                    validated: true,
                    isValid: false,
                    lastValidatedCode: code
                };
            }
        });
    }

    // 简单的客户端语法验证
    function validateStrategyPoolSyntax(code) {
        try {
            // 基本的语法检查
            if (!code.includes('strategy_pool')) {
                return false;
            }
            // 检查括号匹配
            const openBrackets = (code.match(/\[/g) || []).length;
            const closeBrackets = (code.match(/\]/g) || []).length;
            const openBraces = (code.match(/\{/g) || []).length;
            const closeBraces = (code.match(/\}/g) || []).length;
            const openParens = (code.match(/\(/g) || []).length;
            const closeParens = (code.match(/\)/g) || []).length;

            return openBrackets === closeBrackets &&
                   openBraces === closeBraces &&
                   openParens === closeParens;
        } catch (e) {
            return false;
        }
    }

    // ========== 黑白名单配置管理功能 ==========

    // 加载黑白名单配置
    function loadBlackWhitelistConfig(accountData) {
        // 清空现有配置
        $('#blacklist-container').empty();
        $('#whitelist-container').empty();

        // 加载黑名单
        if (accountData.black_list && Array.isArray(accountData.black_list)) {
            accountData.black_list.forEach(symbol => {
                if (symbol.trim()) {
                    addBlacklistPair(symbol);
                }
            });
        }
        // 如果黑名单为空，添加一个空的输入框
        if ($('#blacklist-container').children().length === 0) {
            addBlacklistPair();
        }

        // 加载白名单
        if (accountData.white_list && Array.isArray(accountData.white_list)) {
            accountData.white_list.forEach(symbol => {
                if (symbol.trim()) {
                    addWhitelistPair(symbol);
                }
            });
        }
        // 如果白名单为空，添加一个空的输入框
        if ($('#whitelist-container').children().length === 0) {
            addWhitelistPair();
        }
    }

    // 添加黑名单交易对
    function addBlacklistPair(symbol = '') {
        const pairId = 'blacklist-' + Date.now() + Math.random().toString(36).substr(2, 9);
        const pairHtml = `
            <div class="list-pair blacklist-pair" id="${pairId}">
                <input type="text" class="form-control" placeholder="如: LUNA-USDT" value="${symbol}"
                       pattern="[A-Z]+-[A-Z]+" title="请输入正确的交易对格式，如 LUNA-USDT">
                <button type="button" class="btn btn-outline-danger btn-sm btn-remove" onclick="removeListPair('${pairId}')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        $('#blacklist-container').append(pairHtml);
    }

    // 添加白名单交易对
    function addWhitelistPair(symbol = '') {
        const pairId = 'whitelist-' + Date.now() + Math.random().toString(36).substr(2, 9);
        const pairHtml = `
            <div class="list-pair whitelist-pair" id="${pairId}">
                <input type="text" class="form-control" placeholder="如: BTC-USDT" value="${symbol}"
                       pattern="[A-Z]+-[A-Z]+" title="请输入正确的交易对格式，如 BTC-USDT">
                <button type="button" class="btn btn-outline-success btn-sm btn-remove" onclick="removeListPair('${pairId}')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        $('#whitelist-container').append(pairHtml);
    }

    // 移除列表项
    function removeListPair(pairId) {
        $(`#${pairId}`).remove();
    }

    // 收集黑名单配置
    function collectBlacklistConfig() {
        const blacklist = [];
        $('.blacklist-pair input').each(function() {
            const symbol = $(this).val().trim();
            if (symbol && validateTradingPairFormat(symbol)) {
                blacklist.push(symbol);
            }
        });
        return blacklist;
    }

    // 收集白名单配置
    function collectWhitelistConfig() {
        const whitelist = [];
        $('.whitelist-pair input').each(function() {
            const symbol = $(this).val().trim();
            if (symbol && validateTradingPairFormat(symbol)) {
                whitelist.push(symbol);
            }
        });
        return whitelist;
    }

    // 验证交易对格式
    function validateTradingPairFormat(symbol) {
        // 检查格式是否为 XXX-XXX 的形式
        const pattern = /^[A-Z]+-[A-Z]+$/;
        return pattern.test(symbol);
    }

    // ========== 账户详情显示功能 ==========

    // 显示保证金配置
    function displayMarginConfig(coinMargin, accountIndex) {
        const marginConfigElement = document.getElementById(`margin-config-${accountIndex}`);
        if (!marginConfigElement) return;

        if (coinMargin && typeof coinMargin === 'object' && Object.keys(coinMargin).length > 0) {
            let marginHtml = '';
            Object.entries(coinMargin).forEach(([symbol, amount]) => {
                marginHtml += `<span class="margin-badge">${symbol}: ${amount} USDT</span>`;
            });
            marginConfigElement.innerHTML = marginHtml;
        } else {
            marginConfigElement.innerHTML = '<div class="text-muted small">未配置保证金</div>';
        }
    }

    // 显示黑白名单配置
    function displayBlackWhitelistConfig(blackList, whiteList, accountIndex) {
        // 显示黑名单
        const blacklistElement = document.getElementById(`blacklist-${accountIndex}`);
        if (blacklistElement) {
            const blacklistContent = blacklistElement.querySelector('div');
            if (blackList && Array.isArray(blackList) && blackList.length > 0) {
                let blacklistHtml = '';
                blackList.forEach(symbol => {
                    if (symbol.trim()) {
                        blacklistHtml += `<span class="blacklist-badge">${symbol}</span>`;
                    }
                });
                blacklistContent.innerHTML = blacklistHtml || '<div class="text-muted small">无黑名单</div>';
            } else {
                blacklistContent.innerHTML = '<div class="text-muted small">无黑名单</div>';
            }
        }

        // 显示白名单
        const whitelistElement = document.getElementById(`whitelist-${accountIndex}`);
        if (whitelistElement) {
            const whitelistContent = whitelistElement.querySelector('div');
            if (whiteList && Array.isArray(whiteList) && whiteList.length > 0) {
                let whitelistHtml = '';
                whiteList.forEach(symbol => {
                    if (symbol.trim()) {
                        whitelistHtml += `<span class="whitelist-badge">${symbol}</span>`;
                    }
                });
                whitelistContent.innerHTML = whitelistHtml || '<div class="text-muted small">无限制（全部币种）</div>';
            } else {
                whitelistContent.innerHTML = '<div class="text-muted small">无限制（全部币种）</div>';
            }
        }
    }

    // ====================================================================================================
    // ** 账户名称编辑功能 **
    // ====================================================================================================

    // 开始编辑账户名称
    function editAccountName(accountName, accountIndex) {
        const nameElement = document.getElementById(`account-name-${accountIndex}`);
        const inputElement = document.getElementById(`account-name-input-${accountIndex}`);
        const saveBtnElement = document.getElementById(`save-name-btn-${accountIndex}`);
        const cancelBtnElement = document.getElementById(`cancel-name-btn-${accountIndex}`);

        if (!saveBtnElement) {
            return;
        }

        // 隐藏名称显示，显示编辑控件
        nameElement.style.display = 'none';
        inputElement.style.display = 'inline-block';
        saveBtnElement.style.display = 'inline-block';
        cancelBtnElement.style.display = 'inline-block';

        // 确保输入框值是最新的
        inputElement.value = accountName;

        // 初始化全局编辑值存储
        currentEditingValue[accountIndex] = accountName;

        // 聚焦到输入框并选中文本
        inputElement.focus();
        inputElement.select();

        // 添加输入事件监听器，实时同步值到全局存储
        inputElement.oninput = function() {
            currentEditingValue[accountIndex] = this.value;
        };

        // 添加键盘事件监听器，确保值同步
        inputElement.onkeyup = function() {
            currentEditingValue[accountIndex] = this.value;
        };

        // 添加粘贴事件监听器
        inputElement.onpaste = function() {
            setTimeout(() => {
                currentEditingValue[accountIndex] = this.value;
            }, 10);
        };
    }

    // 取消编辑账户名称
    function cancelEditAccountName(accountIndex) {
        const nameElement = document.getElementById(`account-name-${accountIndex}`);
        const inputElement = document.getElementById(`account-name-input-${accountIndex}`);
        const saveBtnElement = document.getElementById(`save-name-btn-${accountIndex}`);
        const cancelBtnElement = document.getElementById(`cancel-name-btn-${accountIndex}`);

        // 清理全局编辑值存储
        if (currentEditingValue[accountIndex]) {
            delete currentEditingValue[accountIndex];
        }

        // 从账户项获取当前的账户名称
        const accountItem = document.querySelector(`[data-account-id="${accountIndex}"]`);
        const currentName = accountItem ? accountItem.getAttribute('data-account-name') : '';

        // 恢复当前值
        if (currentName) {
            inputElement.value = currentName;
        } else {
            // 备用方案：从名称元素提取
            const originalName = nameElement.textContent.trim().replace(/\s*📝\s*$/, '').replace(/\s*✏️\s*$/, '');
            inputElement.value = originalName;
        }

        // 显示名称，隐藏编辑控件
        nameElement.style.display = 'inline-block';
        inputElement.style.display = 'none';
        saveBtnElement.style.display = 'none';
        cancelBtnElement.style.display = 'none';
    }

    // 处理保存按钮点击事件
    function handleSaveButtonClick(accountIndex) {
        const accountItem = document.querySelector(`[data-account-id="${accountIndex}"]`);
        const oldAccountName = accountItem ? accountItem.getAttribute('data-account-name') : '';

        if (oldAccountName) {
            saveAccountName(oldAccountName, accountIndex);
        } else {
            showMessage('保存失败：无法获取账户信息', 'danger');
        }
    }

    // 处理账户名称输入框按键事件
    function handleAccountNameKeypress(event, accountName, accountIndex) {
        if (event.key === 'Enter') {
            event.preventDefault();
            saveAccountName(accountName, accountIndex);
        } else if (event.key === 'Escape') {
            event.preventDefault();
            cancelEditAccountName(accountIndex);
        }
    }

    // 全局变量存储当前编辑的值
    let currentEditingValue = {};

    // 保存账户名称
    function saveAccountName(oldName, accountIndex) {
        const inputElement = document.getElementById(`account-name-input-${accountIndex}`);
        let newName = '';

        // 优先使用全局存储的编辑值
        if (currentEditingValue[accountIndex]) {
            newName = currentEditingValue[accountIndex].trim();
        }

        // 如果全局存储没有值，从DOM获取
        if (!newName) {
            inputElement.style.display = 'inline-block';
            inputElement.focus();
            newName = inputElement.value.trim();

            // 使用jQuery作为备用
            const jqueryValue = $(`#account-name-input-${accountIndex}`).val().trim();
            if (jqueryValue && jqueryValue !== newName) {
                newName = jqueryValue;
            }
        }

        if (!newName) {
            showMessage('账户名称不能为空', 'danger');
            return;
        }

        if (newName === oldName) {
            cancelEditAccountName(accountIndex);
            return;
        }

        // 显示加载状态
        const saveBtnElement = document.getElementById(`save-name-btn-${accountIndex}`);
        const originalSaveBtnHtml = saveBtnElement.innerHTML;
        saveBtnElement.innerHTML = '<i class="bi bi-hourglass-split"></i>';
        saveBtnElement.disabled = true;

        $.ajax({
            url: `/api/accounts/${oldName}/rename`,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ new_name: newName }),
            success: function(response) {
                if (response.success) {
                    showMessage(response.message, 'success');
                    updateAccountNameDisplay(accountIndex, newName);
                    cancelEditAccountName(accountIndex);
                } else {
                    showMessage('重命名失败: ' + response.message, 'danger');
                }
            },
            error: function() {
                showMessage('重命名请求失败', 'danger');
            },
            complete: function() {
                saveBtnElement.innerHTML = originalSaveBtnHtml;
                saveBtnElement.disabled = false;
            }
        });
    }

    // 更新账户名称显示并重新绑定事件
    function updateAccountNameDisplay(accountIndex, newName) {
        const nameElement = document.getElementById(`account-name-${accountIndex}`);
        const inputElement = document.getElementById(`account-name-input-${accountIndex}`);
        const saveBtnElement = document.getElementById(`save-name-btn-${accountIndex}`);
        const toggleElement = document.getElementById(`account-toggle-${accountIndex}`);

        // 更新名称显示
        nameElement.innerHTML = `${newName} <i class="bi bi-pencil-square ms-1 text-muted" style="font-size: 0.8rem;"></i>`;

        // 更新输入框的值
        inputElement.value = newName;

        // 重新绑定名称编辑事件
        nameElement.onclick = function() { editAccountName(newName, accountIndex); };

        // 更新保存按钮的data属性（事件委托会自动处理）
        if (saveBtnElement) {
            saveBtnElement.setAttribute('data-account-name', newName);
        }

        // 重新绑定输入框按键事件
        inputElement.onkeypress = function(event) { handleAccountNameKeypress(event, newName, accountIndex); };

        // 重新绑定切换开关事件
        if (toggleElement) {
            toggleElement.onchange = function() { toggleAccountStatus(newName, accountIndex, this.checked); };
        }

        // 更新账户项的data属性
        const accountItem = document.querySelector(`[data-account-id="${accountIndex}"]`);
        if (accountItem) {
            accountItem.setAttribute('data-account-name', newName);
        }
    }

    // ====================================================================================================
    // ** 账户启用/禁用功能 **
    // ====================================================================================================

    // 切换账户启用/禁用状态
    function toggleAccountStatus(accountName, accountIndex, enable) {
        // 获取当前实际的账户名称（可能已经被修改过）
        const accountItem = document.querySelector(`[data-account-id="${accountIndex}"]`);
        const currentAccountName = accountItem ? accountItem.getAttribute('data-account-name') : accountName;

        const action = enable ? '启用' : '禁用';

        if (!confirm(`确定要${action}账户 "${currentAccountName}" 吗？\n\n${enable ? '启用后账户将重新参与交易系统。' : '禁用后账户将不参与交易系统，配置文件将移动到accounts_disabled目录。'}`)) {
            // 用户取消，恢复开关状态
            const toggleElement = document.getElementById(`account-toggle-${accountIndex}`);
            toggleElement.checked = !enable;
            return;
        }

        $.ajax({
            url: `/api/accounts/${currentAccountName}/toggle`,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ enable: enable }),
            success: function(response) {
                if (response.success) {
                    showMessage(response.message, 'success');

                    // 更新界面显示
                    updateAccountStatusDisplay(accountIndex, enable);

                    // 可选：延迟刷新页面以显示最新状态
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showMessage(`${action}失败: ` + response.message, 'danger');
                    // 恢复开关状态
                    const toggleElement = document.getElementById(`account-toggle-${accountIndex}`);
                    toggleElement.checked = !enable;
                }
            },
            error: function() {
                showMessage(`${action}请求失败`, 'danger');
                // 恢复开关状态
                const toggleElement = document.getElementById(`account-toggle-${accountIndex}`);
                toggleElement.checked = !enable;
            }
        });
    }

    // 更新账户状态显示
    function updateAccountStatusDisplay(accountIndex, enabled) {
        const accountItem = document.querySelector(`[data-account-id="${accountIndex}"]`);
        const toggleLabel = document.getElementById(`toggle-label-${accountIndex}`);

        if (accountItem) {
            if (enabled) {
                accountItem.classList.remove('account-disabled');
                accountItem.setAttribute('data-enabled', 'true');
            } else {
                accountItem.classList.add('account-disabled');
                accountItem.setAttribute('data-enabled', 'false');
            }
        }

        if (toggleLabel) {
            toggleLabel.textContent = enabled ? '启用' : '禁用';
        }
    }
</script>

<!-- Prism.js for syntax highlighting -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
{% endblock %}
