<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}量化交易系统管理界面{% endblock %}</title>
    {% if csrf_token %}
    <meta name="csrf-token" content="{{ csrf_token }}">
    {% endif %}
    
    <!-- Bootstrap CSS (移除完整性验证) -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet" crossorigin="anonymous" referrerpolicy="no-referrer">
    <!-- Bootstrap Icons - 使用稳定的CDN源 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.10.0/font/bootstrap-icons.min.css" rel="stylesheet" crossorigin="anonymous" referrerpolicy="no-referrer">
    <!-- Chart.js for data visualization -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <!-- Prism.js for syntax highlighting (移除完整性验证) -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet" crossorigin="anonymous" referrerpolicy="no-referrer">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-python.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/line-numbers/prism-line-numbers.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/line-numbers/prism-line-numbers.min.css" rel="stylesheet" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

    <!-- 自定义CSS -->
    <link href="{{ url_for('static', filename='css/modern-style.css') }}" rel="stylesheet">

    {% block extra_css %}{% endblock %}
</head>
<body class="modern-layout">
    <!-- 侧边栏 -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-brand">
                <i class="bi bi-graph-up-arrow"></i>
                <span class="brand-text">监控系统</span>
            </div>
        </div>
        
        <nav class="sidebar-nav">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="{{ url_for('index') }}" class="nav-link {% if request.endpoint == 'index' %}active{% endif %}">
                        <i class="bi bi-play-circle"></i>
                        <span class="nav-text">程序控制</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{{ url_for('accounts_page') }}" class="nav-link {% if request.endpoint == 'accounts_page' %}active{% endif %}">
                        <i class="bi bi-people"></i>
                        <span class="nav-text">账户管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{{ url_for('fund_curve_page') }}" class="nav-link {% if request.endpoint == 'fund_curve_page' %}active{% endif %}">
                        <i class="bi bi-graph-up-arrow"></i>
                        <span class="nav-text">资金曲线</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{{ url_for('global_config_page') }}" class="nav-link {% if request.endpoint == 'global_config_page' %}active{% endif %}">
                        <i class="bi bi-gear"></i>
                        <span class="nav-text">全局配置</span>
                    </a>
                </li>
            </ul>
        </nav>
        

    </div>

    <!-- 主内容区域 -->
    <div class="main-content" id="main-content">
        <!-- 顶部栏 -->
        <div class="top-bar">
            <div class="top-bar-left">
                <button id="sidebar-toggle" class="sidebar-toggle-btn" onclick="toggleSidebar()" title="折叠/展开侧边栏" aria-label="折叠展开侧边栏">
                    <i class="bi bi-list" id="main-icon"></i>
                    <span class="fallback-icon" id="fallback-icon" style="display: none; font-size: 1.2rem; font-weight: bold; line-height: 1;">☰</span>
                    <span class="text-fallback" id="text-fallback" style="display: none; font-size: 0.8rem; font-weight: bold;">菜单</span>
                </button>

                <h1 class="page-title">{% block page_title %}仪表盘{% endblock %}</h1>
            </div>
            <div class="top-bar-right">
                {% if current_user %}
                <!-- 用户信息区域 -->
                <div class="user-info-section">
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle d-flex align-items-center" type="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-2"></i>
                            <span class="d-none d-md-inline">{{ current_user.user_name }}</span>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><span class="dropdown-item-text">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-person-circle me-2"></i>
                                    <div>
                                        <div class="fw-semibold">{{ current_user.user_name }}</div>
                                        <small class="text-muted">{{ current_user.role }}</small>
                                    </div>
                                </div>
                            </span></li>
                            <li><hr class="dropdown-divider"></li>
                            {% if current_user.is_google_auth == 'Y' %}
                            <li><span class="dropdown-item-text"><i class="bi bi-shield-check text-success me-2"></i>双因素认证已启用</span></li>
                            {% else %}
                            <li><a class="dropdown-item" href="{{ url_for('auth.setup_google') }}"><i class="bi bi-shield-plus me-2"></i>设置双因素认证</a></li>
                            {% endif %}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="#" onclick="confirmLogout(event)"><i class="bi bi-box-arrow-right me-2"></i>退出登录</a></li>
                        </ul>
                    </div>
                </div>
                {% endif %}

                <div class="status-indicator">
                    <span id="connection-status" class="status-badge status-success">
                        <i class="bi bi-wifi"></i>
                        已连接
                    </span>
                </div>
                <div class="current-time" id="current-time"></div>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 消息提示区域 -->
            <div id="message-area">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show flash-message"
                                 role="alert"
                                 data-category="{{ category }}"
                                 data-auto-close="{{ 'true' if category == 'success' else 'false' }}">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
            </div>

            <!-- 页面内容 -->
            {% block content %}{% endblock %}
        </div>
    </div>

    <!-- 模态框区域 -->
    <div class="modal fade" id="logModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-file-text"></i>
                        系统日志
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="log-content" class="log-viewer">
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2">正在加载日志...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-primary" onclick="refreshLogs()">
                        <i class="bi bi-arrow-clockwise"></i>
                        刷新
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS (移除完整性验证) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <!-- jQuery -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.0/jquery.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <!-- Socket.IO -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

    <!-- 备用CDN加载脚本 -->
    <script>
        // 检查关键库是否加载成功，如果失败则尝试备用CDN
        function loadFallbackLibraries() {
            // 检查jQuery
            if (typeof jQuery === 'undefined') {
                console.warn('主CDN jQuery加载失败，尝试备用CDN...');
                const script = document.createElement('script');
                script.src = 'https://ajax.googleapis.com/ajax/libs/jquery/3.7.0/jquery.min.js';
                script.crossOrigin = 'anonymous';
                document.head.appendChild(script);
            }

            // 检查Bootstrap
            if (typeof bootstrap === 'undefined') {
                console.warn('主CDN Bootstrap加载失败，尝试备用CDN...');
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js';
                script.crossOrigin = 'anonymous';
                document.head.appendChild(script);
            }

            // 检查Socket.IO
            if (typeof io === 'undefined') {
                console.warn('主CDN Socket.IO加载失败，尝试备用CDN...');
                const script = document.createElement('script');
                script.src = 'https://cdn.socket.io/4.7.2/socket.io.min.js';
                script.crossOrigin = 'anonymous';
                document.head.appendChild(script);
            }
        }

        // 页面加载完成后检查并加载备用库
        window.addEventListener('load', function() {
            setTimeout(loadFallbackLibraries, 3000);
        });
    </script>

    <!-- 基础JavaScript -->
    <script>
        // 改进的依赖检查
        function checkDependencies() {
            const dependencies = [
                { name: 'jQuery', check: () => typeof jQuery !== 'undefined' },
                { name: 'Bootstrap', check: () => typeof bootstrap !== 'undefined' },
                { name: 'Socket.IO', check: () => typeof io !== 'undefined' }
            ];

            const missing = dependencies.filter(dep => !dep.check());

            if (missing.length > 0) {
                console.warn('以下依赖库未完全加载:', missing.map(dep => dep.name).join(', '));
                // 不阻止页面加载，只显示警告
                const warningDiv = document.createElement('div');
                warningDiv.className = 'alert alert-warning alert-dismissible fade show m-3';
                warningDiv.innerHTML = `
                    <strong>注意:</strong> 部分JavaScript库可能未完全加载 (${missing.map(dep => dep.name).join(', ')})。
                    如果功能异常，请刷新页面或检查网络连接。
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                document.body.insertBefore(warningDiv, document.body.firstChild);

                // 5秒后自动隐藏警告
                setTimeout(() => {
                    if (warningDiv.parentNode) {
                        warningDiv.remove();
                    }
                }, 5000);
            }
        }

        // 页面加载完成后检查依赖
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(checkDependencies, 2000); // 延迟2秒检查，确保所有资源都有时间加载
        });
        
        // WebSocket连接（改进错误处理）
        let socket;

        // 等待Socket.IO库加载完成
        function initializeWebSocket() {
            if (typeof io !== 'undefined') {
                try {
                    socket = io({
                        timeout: 5000,
                        reconnection: true,
                        reconnectionDelay: 1000,
                        reconnectionAttempts: 5
                    });

                    // 连接状态管理
                    socket.on('connect', function() {
                        updateStatusIndicator('success', '已连接');
                    });

                    socket.on('disconnect', function() {
                        updateStatusIndicator('danger', '连接断开');
                    });

                    socket.on('connect_error', function(error) {
                        console.error('WebSocket连接错误:', error);
                        updateStatusIndicator('warning', '连接错误');
                    });

                    socket.on('status_update', function(data) {
                        if (typeof updateSystemStatus === 'function') {
                            updateSystemStatus(data);
                        }
                    });
                } catch (error) {
                    console.error('WebSocket初始化失败:', error);
                    updateStatusIndicator('danger', '连接失败');
                }
            } else {
                console.warn('Socket.IO库未加载，将在3秒后重试...');
                setTimeout(initializeWebSocket, 3000);
            }
        }

        // 页面加载完成后初始化WebSocket
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(initializeWebSocket, 1000); // 延迟1秒确保所有库都已加载
        });
        
        // 更新状态指示器（改进版本）
        function updateStatusIndicator(type, text) {
            try {
                const indicator = document.getElementById('connection-status');
                if (indicator) {
                    indicator.className = `status-badge status-${type}`;
                    const iconMap = {
                        'success': 'wifi',
                        'danger': 'wifi-off',
                        'warning': 'exclamation-triangle',
                        'info': 'info-circle'
                    };
                    const icon = iconMap[type] || 'circle';
                    indicator.innerHTML = `<i class="bi bi-${icon}"></i> ${text}`;
                } else {
                    console.warn('连接状态指示器元素未找到');
                }
            } catch (error) {
                console.error('更新状态指示器失败:', error);
            }
        }
        
        // 侧边栏切换
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('main-content');

            // 检查屏幕尺寸
            if (window.innerWidth <= 768) {
                // 小屏幕：使用 show/hide 切换
                sidebar.classList.toggle('show');
            } else {
                // 大屏幕：使用 collapsed 切换
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('sidebar-collapsed');
            }
        }

        // 窗口大小变化时重置侧边栏状态
        function handleResize() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('main-content');

            if (window.innerWidth <= 768) {
                // 小屏幕：移除大屏幕的类，确保使用移动端样式
                sidebar.classList.remove('collapsed');
                mainContent.classList.remove('sidebar-collapsed');
            } else {
                // 大屏幕：移除移动端的类
                sidebar.classList.remove('show');
            }
        }

        // 监听窗口大小变化
        window.addEventListener('resize', handleResize);

        // 检查Bootstrap Icons是否加载
        function checkBootstrapIcons() {
            const testElement = document.createElement('i');
            testElement.className = 'bi bi-list';
            testElement.style.position = 'absolute';
            testElement.style.left = '-9999px';
            document.body.appendChild(testElement);

            const computedStyle = window.getComputedStyle(testElement);
            const fontFamily = computedStyle.fontFamily;

            document.body.removeChild(testElement);

            return fontFamily.includes('bootstrap-icons');
        }

        // 显示备用图标
        function showFallbackIcon() {
            const toggleBtn = document.getElementById('sidebar-toggle');
            if (toggleBtn) {
                const mainIcon = document.getElementById('main-icon');
                const fallbackIcon = document.getElementById('fallback-icon');

                if (mainIcon) {
                    mainIcon.style.display = 'none';
                }

                if (fallbackIcon) {
                    // 强制设置备用图标样式确保可见
                    fallbackIcon.style.setProperty('display', 'inline-block', 'important');
                    fallbackIcon.style.setProperty('visibility', 'visible', 'important');
                    fallbackIcon.style.setProperty('opacity', '1', 'important');
                    fallbackIcon.style.setProperty('color', '#007bff', 'important');
                    fallbackIcon.style.setProperty('font-size', '1.3rem', 'important');
                    fallbackIcon.style.setProperty('font-weight', '900', 'important');
                    fallbackIcon.style.setProperty('text-align', 'center', 'important');
                    fallbackIcon.style.setProperty('line-height', '1', 'important');
                    fallbackIcon.style.setProperty('z-index', '10', 'important');
                    fallbackIcon.style.setProperty('position', 'relative', 'important');
                }
            }
        }

        // 验证图标显示状态
        function verifyIconDisplay() {
            const toggleBtn = document.getElementById('sidebar-toggle');
            if (!toggleBtn) return;

            const mainIcon = document.getElementById('main-icon');
            const fallbackIcon = document.getElementById('fallback-icon');
        }

        // 初始化Flash消息自动关闭功能
        function initFlashMessages() {
            const flashMessages = document.querySelectorAll('.flash-message');

            flashMessages.forEach(function(message) {
                const autoClose = message.getAttribute('data-auto-close') === 'true';
                const category = message.getAttribute('data-category');

                if (autoClose && category === 'success') {
                    // 为成功消息添加自动关闭功能

                    // 添加倒计时指示器
                    addCountdownIndicator(message);

                    // 5秒后自动关闭
                    setTimeout(function() {
                        if (message.parentNode) {
                            // 使用Bootstrap的fade效果
                            message.classList.remove('show');

                            // 等待动画完成后移除元素
                            setTimeout(function() {
                                if (message.parentNode) {
                                    message.remove();
                                }
                            }, 150); // Bootstrap fade动画时间
                        }
                    }, 5000);
                }
            });
        }

        // 添加倒计时指示器
        function addCountdownIndicator(messageElement) {
            // 创建倒计时指示器
            const countdownDiv = document.createElement('div');
            countdownDiv.className = 'flash-countdown';
            countdownDiv.innerHTML = `
                <small class="text-muted">
                    <i class="bi bi-clock me-1"></i>
                    <span class="countdown-text">5秒后自动关闭</span>
                </small>
            `;

            // 添加样式
            countdownDiv.style.cssText = `
                margin-top: 8px;
                font-size: 0.8rem;
                opacity: 0.8;
            `;

            messageElement.appendChild(countdownDiv);

            // 倒计时更新
            let seconds = 5;
            const countdownText = countdownDiv.querySelector('.countdown-text');

            const countdownInterval = setInterval(function() {
                seconds--;
                if (seconds > 0) {
                    countdownText.textContent = `${seconds}秒后自动关闭`;
                } else {
                    countdownText.textContent = '正在关闭...';
                    clearInterval(countdownInterval);
                }
            }, 1000);

            // 如果用户手动关闭，清除倒计时
            const closeButton = messageElement.querySelector('.btn-close');
            if (closeButton) {
                closeButton.addEventListener('click', function() {
                    clearInterval(countdownInterval);
                });
            }
        }

        // 初始化顶部栏滚动效果
        function initTopBarScrollEffect() {
            const topBar = document.querySelector('.top-bar');
            if (!topBar) return;

            let lastScrollY = window.scrollY;
            let ticking = false;

            function updateTopBar() {
                const scrollY = window.scrollY;

                // 当滚动超过50px时添加scrolled类
                if (scrollY > 50) {
                    topBar.classList.add('scrolled');
                } else {
                    topBar.classList.remove('scrolled');
                }

                lastScrollY = scrollY;
                ticking = false;
            }

            function requestTick() {
                if (!ticking) {
                    requestAnimationFrame(updateTopBar);
                    ticking = true;
                }
            }

            // 监听滚动事件
            window.addEventListener('scroll', requestTick, { passive: true });

            // 初始检查
            updateTopBar();
        }

        // 强制应用顶部栏样式
        function forceApplyTopBarStyles() {
            const topBar = document.querySelector('.top-bar');
            if (topBar) {
                // 强制应用渐变背景
                topBar.style.setProperty('background', 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)', 'important');
                topBar.style.setProperty('backdrop-filter', 'blur(10px)', 'important');
                topBar.style.setProperty('border-bottom', '1px solid rgba(0, 123, 255, 0.1)', 'important');
                topBar.style.setProperty('box-shadow', '0 4px 20px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.1)', 'important');
                topBar.style.setProperty('padding', '1.25rem 2rem', 'important');
            }

            // 强制应用页面标题样式
            const pageTitle = document.querySelector('.page-title');
            if (pageTitle) {
                pageTitle.style.setProperty('background', 'linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #007bff 100%)', 'important');
                pageTitle.style.setProperty('-webkit-background-clip', 'text', 'important');
                pageTitle.style.setProperty('-webkit-text-fill-color', 'transparent', 'important');
                pageTitle.style.setProperty('font-weight', '700', 'important');
            }
        }

        // 验证顶部栏样式是否正确应用
        function verifyTopBarStyles() {
            const topBar = document.querySelector('.top-bar');
            if (!topBar) {
                console.error('❌ 未找到顶部栏元素');
                return;
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            handleResize();

            // 初始化Flash消息自动关闭
            initFlashMessages();

            // 初始化顶部栏滚动效果
            initTopBarScrollEffect();

            // 强制应用顶部栏样式
            forceApplyTopBarStyles();

            // 验证顶部栏样式
            verifyTopBarStyles();

            // 初始化侧边栏折叠按钮
            const toggleBtn = document.getElementById('sidebar-toggle');
            if (toggleBtn) {
                // 确保按钮有正确的CSS类
                toggleBtn.classList.add('sidebar-toggle-btn');

                // 强制应用关键样式确保可见性
                toggleBtn.style.setProperty('background', 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)', 'important');
                toggleBtn.style.setProperty('border', '2px solid rgba(0, 123, 255, 0.6)', 'important');
                toggleBtn.style.setProperty('width', '44px', 'important');
                toggleBtn.style.setProperty('height', '44px', 'important');
                toggleBtn.style.setProperty('border-radius', '12px', 'important');
                toggleBtn.style.setProperty('-webkit-border-radius', '12px', 'important');
                toggleBtn.style.setProperty('-moz-border-radius', '12px', 'important');
                toggleBtn.style.setProperty('-ms-border-radius', '12px', 'important');
                toggleBtn.style.setProperty('-o-border-radius', '12px', 'important');

                // 分别设置四个角的圆角
                toggleBtn.style.setProperty('border-top-left-radius', '12px', 'important');
                toggleBtn.style.setProperty('border-top-right-radius', '12px', 'important');
                toggleBtn.style.setProperty('border-bottom-left-radius', '12px', 'important');
                toggleBtn.style.setProperty('border-bottom-right-radius', '12px', 'important');

                // 禁用Bootstrap变量
                toggleBtn.style.setProperty('--bs-border-radius', '12px', 'important');
                toggleBtn.style.setProperty('display', 'inline-flex', 'important');
                toggleBtn.style.setProperty('opacity', '1', 'important');
                toggleBtn.style.setProperty('visibility', 'visible', 'important');

                // 强制重新计算样式（触发CSS重新应用）
                toggleBtn.offsetHeight; // 触发重排

                // 检查Bootstrap Icons是否加载
                setTimeout(() => {
                    if (!checkBootstrapIcons()) {
                        console.warn('⚠️ Bootstrap Icons未加载，使用备用图标');
                        toggleBtn.classList.add('use-fallback');
                        showFallbackIcon();
                    }

                    // 验证图标显示状态
                    setTimeout(() => {
                        verifyIconDisplay();
                    }, 300);

                    // 延迟重试border-radius设置
                    setTimeout(() => {
                        const finalStyle = window.getComputedStyle(toggleBtn);
                        const finalRadius = finalStyle.borderRadius;

                        // 更宽松的检查条件
                        if (finalRadius.includes('calc') || finalRadius.includes('%') ||
                            (!finalRadius.includes('12px') && !finalRadius.includes('12'))) {

                            // 最终强制设置 - 使用所有可能的属性
                            const radiusProps = [
                                'border-radius',
                                'border-top-left-radius',
                                'border-top-right-radius',
                                'border-bottom-left-radius',
                                'border-bottom-right-radius',
                                'border-start-start-radius',
                                'border-start-end-radius',
                                'border-end-start-radius',
                                'border-end-end-radius',
                                '-webkit-border-radius',
                                '-moz-border-radius',
                                '-ms-border-radius',
                                '-o-border-radius'
                            ];

                            radiusProps.forEach(prop => {
                                toggleBtn.style.setProperty(prop, '12px', 'important');
                            });

                            // 再次验证
                            setTimeout(() => {
                                const retryStyle = window.getComputedStyle(toggleBtn);
                                if (retryStyle.borderRadius.includes('calc') || retryStyle.borderRadius.includes('%')) {
                                    console.warn('⚠️ Bootstrap CSS覆盖过强，border-radius仍为计算值');
                                }
                            }, 200);
                        }
                    }, 800);

                    // 验证现代化样式是否生效
                    const computedStyle = window.getComputedStyle(toggleBtn);
                    const borderRadius = computedStyle.borderRadius;
                    const width = computedStyle.width;

                    // 检查border-radius是否包含12px或接近12px的值
                    const isCorrectRadius = borderRadius === '12px' ||
                                          borderRadius.includes('12px') ||
                                          (parseFloat(borderRadius) >= 11 && parseFloat(borderRadius) <= 13);

                    if (!(isCorrectRadius && width === '44px')) {
                        // 如果border-radius不正确，尝试再次强制设置
                        if (!isCorrectRadius) {

                            // 强制设置所有border-radius相关属性
                            toggleBtn.style.setProperty('border-radius', '12px', 'important');
                            toggleBtn.style.setProperty('-webkit-border-radius', '12px', 'important');
                            toggleBtn.style.setProperty('-moz-border-radius', '12px', 'important');
                            toggleBtn.style.setProperty('-ms-border-radius', '12px', 'important');
                            toggleBtn.style.setProperty('-o-border-radius', '12px', 'important');

                            // 分别设置四个角的圆角
                            toggleBtn.style.setProperty('border-top-left-radius', '12px', 'important');
                            toggleBtn.style.setProperty('border-top-right-radius', '12px', 'important');
                            toggleBtn.style.setProperty('border-bottom-left-radius', '12px', 'important');
                            toggleBtn.style.setProperty('border-bottom-right-radius', '12px', 'important');

                            // 禁用Bootstrap变量
                            toggleBtn.style.setProperty('--bs-border-radius', '12px', 'important');
                        }
                    }
                }, 200);
            } else {
                console.error('❌ 未找到侧边栏折叠按钮');
            }
        });
        
        // 显示消息
        function showMessage(message, type = 'info') {
            const messageArea = document.getElementById('message-area');
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show dynamic-message`;
            alertDiv.setAttribute('data-category', type);
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            messageArea.appendChild(alertDiv);

            // 为成功消息添加倒计时指示器和自动关闭
            if (type === 'success') {
                addCountdownIndicator(alertDiv);

                // 5秒后自动关闭
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.classList.remove('show');
                        setTimeout(() => {
                            if (alertDiv.parentNode) {
                                alertDiv.remove();
                            }
                        }, 150);
                    }
                }, 5000);
            } else {
                // 非成功消息仍然5秒后自动消失（保持原有行为）
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }
        }
        
        // 显示日志
        function showLogs() {
            const modal = new bootstrap.Modal(document.getElementById('logModal'));
            modal.show();
            // 这里可以添加加载日志的逻辑
            setTimeout(() => {
                document.getElementById('log-content').innerHTML = '<pre class="log-text">暂无日志数据</pre>';
            }, 1000);
        }
        
        // 刷新日志
        function refreshLogs() {
            document.getElementById('log-content').innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">刷新中...</span>
                    </div>
                    <p class="mt-2">正在刷新日志...</p>
                </div>
            `;
            // 模拟刷新
            setTimeout(() => {
                document.getElementById('log-content').innerHTML = '<pre class="log-text">日志已刷新</pre>';
            }, 1000);
        }
        
        // 更新当前时间
        function updateCurrentTime() {
            const timeElement = document.getElementById('current-time');
            if (timeElement) {
                const now = new Date();
                timeElement.textContent = now.toLocaleString('zh-CN');
            }
        }

        // 退出登录确认
        function confirmLogout(event) {
            event.preventDefault();

            // 创建确认对话框
            const confirmModal = document.createElement('div');
            confirmModal.className = 'modal fade';
            confirmModal.innerHTML = `
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header border-0">
                            <h5 class="modal-title">
                                <i class="bi bi-question-circle text-warning me-2"></i>
                                确认退出登录
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body text-center py-4">
                            <div class="mb-3">
                                <i class="bi bi-box-arrow-right text-danger" style="font-size: 3rem;"></i>
                            </div>
                            <p class="mb-0">您确定要退出登录吗？</p>
                            <small class="text-muted">退出后需要重新输入用户名密码和Google认证码</small>
                        </div>
                        <div class="modal-footer border-0 justify-content-center">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="bi bi-x-lg me-1"></i>取消
                            </button>
                            <button type="button" class="btn btn-danger" onclick="performLogout()">
                                <i class="bi bi-box-arrow-right me-1"></i>确认退出
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(confirmModal);
            const modal = new bootstrap.Modal(confirmModal);
            modal.show();

            // 模态框关闭后移除DOM元素
            confirmModal.addEventListener('hidden.bs.modal', function() {
                document.body.removeChild(confirmModal);
            });
        }

        // 执行退出登录
        function performLogout() {
            // 显示加载状态
            showMessage('正在退出登录...', 'info');

            // 延迟跳转，让用户看到消息
            setTimeout(() => {
                window.location.href = '{{ url_for("auth.logout") }}';
            }, 500);
        }

        // 页面加载完成后初始化
        $(document).ready(function() {
            updateCurrentTime();
            setInterval(updateCurrentTime, 1000);
        });
    </script>

    <!-- Google认证处理脚本 -->
    <script src="{{ url_for('static', filename='auth/js/auth.js') }}?v={{ range(1000, 9999) | random }}"></script>
    <script>
        // 初始化认证系统
        $(document).ready(function() {
            if (typeof initializeAuth === 'function') {
                initializeAuth();
            }
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
