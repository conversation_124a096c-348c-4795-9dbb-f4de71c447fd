<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>双因素认证验证 - 量化交易系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet" crossorigin="anonymous" referrerpolicy="no-referrer">
    <!-- Bootstrap Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.10.0/font/bootstrap-icons.min.css" rel="stylesheet" crossorigin="anonymous" referrerpolicy="no-referrer">
    
    <!-- 自定义CSS -->
    <link href="{{ url_for('static', filename='auth/css/auth.css') }}" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
        }
        
        .verify-container {
            max-width: 400px;
            width: 100%;
            padding: 0 20px;
        }
        
        .verify-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
        }
        
        .verify-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .verify-header h2 {
            margin: 0;
            font-weight: 600;
            font-size: 1.5rem;
        }
        
        .verify-header .subtitle {
            margin-top: 8px;
            opacity: 0.9;
            font-size: 0.9rem;
        }
        
        .verify-body {
            padding: 30px;
        }
        
        .auth-method-tabs {
            display: flex;
            margin-bottom: 20px;
            border-radius: 10px;
            background: #f8f9fa;
            padding: 4px;
        }
        
        .auth-method-tab {
            flex: 1;
            padding: 10px;
            text-align: center;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .auth-method-tab.active {
            background: #667eea;
            color: white;
        }
        
        .auth-method-tab:hover:not(.active) {
            background: #e9ecef;
        }
        
        .auth-method-content {
            display: none;
        }
        
        .auth-method-content.active {
            display: block;
        }
        
        .form-floating > .form-control {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 12px 16px;
            height: auto;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-floating > .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .verification-code-input {
            text-align: center;
            font-size: 1.2rem;
            letter-spacing: 0.5rem;
            font-weight: 600;
        }
        
        .btn-verify {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 12px;
            font-size: 1rem;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-verify:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .btn-verify:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: none;
        }
        
        .alert {
            border-radius: 12px;
            border: none;
            margin-bottom: 20px;
        }
        
        .help-text {
            font-size: 0.9rem;
            color: #6c757d;
            text-align: center;
            margin-top: 15px;
        }
        
        .help-text i {
            color: #667eea;
        }
        
        .countdown-timer {
            font-size: 0.8rem;
            color: #6c757d;
            text-align: center;
            margin-top: 10px;
        }
        
        .skip-link {
            text-align: center;
            margin-top: 20px;
        }
        
        .skip-link a {
            color: #6c757d;
            text-decoration: none;
            font-size: 0.9rem;
        }
        
        .skip-link a:hover {
            color: #667eea;
            text-decoration: underline;
        }
        
        @media (max-width: 576px) {
            .verify-container {
                padding: 0 15px;
            }
            
            .verify-header, .verify-body {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="verify-container">
        <div class="verify-card">
            <!-- 验证头部 -->
            <div class="verify-header">
                <i class="bi bi-shield-check fs-1 mb-3"></i>
                <h2>双因素认证</h2>
                <div class="subtitle">请完成安全验证以继续</div>
            </div>
            
            <!-- 验证内容 -->
            <div class="verify-body">
                <!-- 错误提示 -->
                {% if error_message %}
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    {{ error_message }}
                </div>
                {% endif %}
                
                <!-- 认证方式选择 -->
                <div class="auth-method-tabs">
                    <div class="auth-method-tab active" onclick="switchAuthMethod('google')">
                        <i class="bi bi-key me-1"></i>
                        验证码
                    </div>
                    <div class="auth-method-tab" onclick="switchAuthMethod('backup')">
                        <i class="bi bi-shield-exclamation me-1"></i>
                        备用码
                    </div>
                </div>
                
                <!-- Google验证码方式 -->
                <div class="auth-method-content active" id="googleMethod">
                    <form id="googleVerifyForm" method="POST" action="{{ url_for('auth.google_verify_old') }}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">

                        <div class="form-floating mb-3">
                            <input type="text" class="form-control verification-code-input"
                                   id="google_code" name="google_code"
                                   placeholder="000000" maxlength="6" pattern="[0-9]{6}" required
                                   autocomplete="one-time-code">
                            <label for="google_code">
                                <i class="bi bi-key me-2"></i>6位验证码
                            </label>
                        </div>

                        <button type="submit" class="btn btn-verify" id="googleVerifyBtn">
                            <span class="spinner-border spinner-border-sm me-2" role="status" style="display: none;"></span>
                            <i class="bi bi-check-lg me-2"></i>
                            验证
                        </button>

                        <div class="help-text">
                            <i class="bi bi-info-circle me-1"></i>
                            请打开Google Authenticator应用获取验证码
                        </div>
                    </form>
                </div>

                <!-- 备用码方式 -->
                <div class="auth-method-content" id="backupMethod">
                    <form id="backupVerifyForm" method="POST" action="{{ url_for('auth.google_verify_old') }}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                        
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control verification-code-input" 
                                   id="backup_code" name="backup_code" 
                                   placeholder="12345678" maxlength="8" pattern="[0-9]{8}">
                            <label for="backup_code">
                                <i class="bi bi-shield-exclamation me-2"></i>8位备用码
                            </label>
                        </div>
                        
                        <button type="submit" class="btn btn-verify" id="backupVerifyBtn">
                            <span class="spinner-border spinner-border-sm me-2" role="status" style="display: none;"></span>
                            <i class="bi bi-check-lg me-2"></i>
                            验证
                        </button>
                        
                        <div class="help-text">
                            <i class="bi bi-exclamation-triangle me-1"></i>
                            备用码使用后将失效，请妥善保管剩余备用码
                        </div>
                    </form>
                </div>
                
                <!-- 倒计时提示 -->
                <div class="countdown-timer" id="countdownTimer" style="display: none;">
                    <i class="bi bi-clock me-1"></i>
                    验证将在 <span id="countdown">600</span> 秒后过期
                </div>
                
                <!-- 跳过链接（仅在特定情况下显示） -->
                <div class="skip-link" style="display: none;">
                    <a href="{{ url_for('auth.logout') }}">
                        <i class="bi bi-box-arrow-right me-1"></i>
                        退出登录
                    </a>
                </div>
            </div>
        </div>
        
        <!-- 版权信息 -->
        <div class="text-center mt-4">
            <small class="text-white-50">
                © 2025 量化交易系统 | 安全可靠的交易管理平台
            </small>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <!-- jQuery -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.0/jquery.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    
    <!-- 自定义JavaScript -->
    <script src="{{ url_for('static', filename='auth/js/auth.js') }}?v={{ range(1000, 9999) | random }}"></script>
    
    <script>
        let countdownInterval;
        
        $(document).ready(function() {
            // 初始化验证码输入框
            initializeCodeInputs();
            
            // 初始化表单提交
            initializeFormSubmit();
            
            // 启动倒计时
            startCountdown(600); // 10分钟
            
            // 自动聚焦到验证码输入框
            $('#google_code').focus();
        });
        
        function switchAuthMethod(method) {
            // 更新选项卡状态
            $('.auth-method-tab').removeClass('active');
            $('.auth-method-content').removeClass('active');
            
            if (method === 'google') {
                $('.auth-method-tab:first').addClass('active');
                $('#googleMethod').addClass('active');
                $('#google_code').focus();
            } else {
                $('.auth-method-tab:last').addClass('active');
                $('#backupMethod').addClass('active');
                $('#backup_code').focus();
            }
        }
        
        function initializeCodeInputs() {
            // Google验证码输入框
            $('#google_code').on('input', function() {
                let value = this.value.replace(/\D/g, '');
                if (value.length > 6) {
                    value = value.substring(0, 6);
                }
                this.value = value;
            });
            
            // 备用码输入框
            $('#backup_code').on('input', function() {
                let value = this.value.replace(/\D/g, '');
                if (value.length > 8) {
                    value = value.substring(0, 8);
                }
                this.value = value;
            });
        }
        
        function initializeFormSubmit() {
            // Google验证码表单
            $('#googleVerifyForm').on('submit', function(e) {
                const code = $('#google_code').val().trim();
                if (code.length !== 6) {
                    e.preventDefault();
                    showError('请输入6位验证码');
                    return;
                }
                
                showLoadingState('#googleVerifyBtn', '验证中...');
            });
            
            // 备用码表单
            $('#backupVerifyForm').on('submit', function(e) {
                const code = $('#backup_code').val().trim();
                if (code.length !== 8) {
                    e.preventDefault();
                    showError('请输入8位备用码');
                    return;
                }
                
                showLoadingState('#backupVerifyBtn', '验证中...');
            });
        }
        
        function startCountdown(seconds) {
            let remaining = seconds;
            const countdownElement = $('#countdown');
            const timerElement = $('#countdownTimer');
            
            timerElement.show();
            
            countdownInterval = setInterval(function() {
                remaining--;
                countdownElement.text(remaining);
                
                if (remaining <= 0) {
                    clearInterval(countdownInterval);
                    handleTimeout();
                } else if (remaining <= 60) {
                    // 最后一分钟显示红色警告
                    timerElement.addClass('text-danger');
                }
            }, 1000);
        }
        
        function handleTimeout() {
            showError('验证超时，请重新登录', 'warning');
            setTimeout(function() {
                window.location.href = '{{ url_for("auth.logout") }}';
            }, 2000);
        }
        
        function showError(message, type = 'danger') {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            // 移除现有警告
            $('.alert').remove();
            
            // 添加新警告
            $('.verify-body').prepend(alertHtml);
        }
        
        function showLoadingState(buttonSelector, text) {
            const btn = $(buttonSelector);
            const spinner = btn.find('.spinner-border');
            const textElement = btn.find('span:not(.spinner-border)').last();
            
            btn.prop('disabled', true);
            spinner.show();
            if (textElement.length > 0) {
                textElement.text(text);
            }
        }
        
        // 页面卸载时清理倒计时
        $(window).on('beforeunload', function() {
            if (countdownInterval) {
                clearInterval(countdownInterval);
            }
        });
    </script>
</body>
</html>
