<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google认证设置成功 - 量化交易系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet" crossorigin="anonymous" referrerpolicy="no-referrer">
    <!-- Bootstrap Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.10.0/font/bootstrap-icons.min.css" rel="stylesheet" crossorigin="anonymous" referrerpolicy="no-referrer">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', '<PERSON>Fang SC', 'Hiragino Sans GB', sans-serif;
            padding: 20px 0;
        }
        
        .success-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .success-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
        }
        
        .success-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }
        
        .success-header .success-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            animation: successPulse 2s ease-in-out infinite;
        }
        
        @keyframes successPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        .success-header h2 {
            margin: 0;
            font-weight: 600;
            font-size: 1.8rem;
        }
        
        .success-header .subtitle {
            margin-top: 10px;
            opacity: 0.9;
            font-size: 1rem;
        }
        
        .success-body {
            padding: 40px 30px;
        }
        
        .backup-codes-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border: 2px dashed #dee2e6;
        }
        
        .backup-codes-title {
            color: #495057;
            font-weight: 600;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .backup-codes-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        
        .backup-code {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 12px 8px;
            text-align: center;
            font-family: 'Courier New', monospace;
            font-weight: bold;
            font-size: 0.9rem;
            color: #495057;
            transition: all 0.3s ease;
        }
        
        .backup-code:hover {
            background: #e9ecef;
            border-color: #adb5bd;
            transform: translateY(-2px);
        }
        
        .warning-box {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .warning-box .warning-title {
            color: #856404;
            font-weight: 600;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        
        .warning-box ul {
            color: #856404;
            margin: 0;
            padding-left: 20px;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin: 30px 0 20px 0;
        }
        
        .btn-custom {
            border-radius: 25px;
            padding: 12px 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            min-width: 140px;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .btn-primary-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-success-custom {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        
        .btn-warning-custom {
            background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
            color: white;
        }
        
        .countdown-info {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 1px solid #bbdefb;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
        }
        
        .countdown-info .countdown-text {
            color: #1976d2;
            font-weight: 500;
            margin: 0;
        }
        
        .countdown-number {
            font-size: 1.2rem;
            font-weight: bold;
            color: #1565c0;
        }
        
        @media (max-width: 576px) {
            .success-container {
                padding: 0 15px;
            }
            
            .success-header {
                padding: 30px 20px;
            }
            
            .success-body {
                padding: 30px 20px;
            }
            
            .backup-codes-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .btn-custom {
                width: 100%;
                max-width: 250px;
            }
        }
    </style>
</head>
<body>
    <div class="success-container">
        <div class="success-card">
            <!-- 成功头部 -->
            <div class="success-header">
                <div class="success-icon">
                    <i class="bi bi-check-circle-fill"></i>
                </div>
                <h2>Google认证设置成功！</h2>
                <div class="subtitle">您的账户安全性已得到显著增强</div>
            </div>
            
            <!-- 成功内容 -->
            <div class="success-body">
                <!-- Flash消息显示 -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'success' if category == 'success' else 'danger' if category == 'error' else 'warning' if category == 'warning' else 'info' }} alert-dismissible fade show" role="alert">
                                <i class="bi bi-{{ 'check-circle-fill' if category == 'success' else 'exclamation-triangle-fill' if category in ['error', 'warning'] else 'info-circle-fill' }} me-2"></i>
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                <!-- 备用码显示 -->
                {% if backup_codes %}
                <div class="backup-codes-section">
                    <div class="backup-codes-title">
                        <i class="bi bi-shield-exclamation text-warning me-2"></i>
                        重要：请保存您的备用码
                    </div>
                    <p class="text-muted mb-3">
                        以下是您的Google认证备用码。当您无法使用Google Authenticator应用时，可以使用这些备用码登录系统。
                    </p>
                    <div class="backup-codes-grid">
                        {% for code in backup_codes %}
                        <div class="backup-code" onclick="selectCode(this)">{{ code }}</div>
                        {% endfor %}
                    </div>
                    <div class="text-center">
                        <button type="button" class="btn btn-sm btn-outline-primary me-2" onclick="downloadBackupCodes()">
                            <i class="bi bi-download me-1"></i>下载备用码
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="printBackupCodes()">
                            <i class="bi bi-printer me-1"></i>打印备用码
                        </button>
                    </div>
                </div>
                {% endif %}
                
                <!-- 重要提示 -->
                <div class="warning-box">
                    <div class="warning-title">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        重要安全提示
                    </div>
                    <ul>
                        <li>请妥善保存上述备用码，建议打印或下载保存</li>
                        <li>每个备用码只能使用一次，使用后将自动失效</li>
                        <li>请勿将备用码分享给他人或存储在不安全的位置</li>
                        <li>从现在开始，登录时需要提供Google Authenticator验证码</li>
                    </ul>
                </div>
                
                <!-- 自动跳转提示 -->
                <div class="countdown-info">
                    <p class="countdown-text">
                        <i class="bi bi-clock me-2"></i>
                        系统将在 <span class="countdown-number" id="countdown">10</span> 秒后自动跳转到主页
                    </p>
                </div>
                
                <!-- 操作按钮 -->
                <div class="action-buttons">
                    <button type="button" class="btn btn-primary-custom btn-custom" onclick="goToHome()">
                        <i class="bi bi-house-fill me-2"></i>
                        立即进入系统
                    </button>
                    <button type="button" class="btn btn-success-custom btn-custom" onclick="testGoogleAuth()">
                        <i class="bi bi-shield-check me-2"></i>
                        测试Google认证
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 版权信息 -->
        <div class="text-center mt-4">
            <small class="text-white-50">
                © 2025 量化交易系统 | 安全可靠的交易管理平台
            </small>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <!-- jQuery -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.0/jquery.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    
    <script>
        let countdownInterval;
        
        $(document).ready(function() {
            // 启动倒计时
            startCountdown(10);
            
            // 自动关闭Flash消息
            setTimeout(function() {
                $('.alert').fadeOut();
            }, 5000);
        });
        
        function startCountdown(seconds) {
            let remaining = seconds;
            const countdownElement = $('#countdown');
            
            countdownInterval = setInterval(function() {
                remaining--;
                countdownElement.text(remaining);
                
                if (remaining <= 0) {
                    clearInterval(countdownInterval);
                    goToHome();
                }
            }, 1000);
        }
        
        function goToHome() {
            if (countdownInterval) {
                clearInterval(countdownInterval);
            }
            window.location.href = '{{ url_for("index") }}';
        }
        
        function testGoogleAuth() {
            if (countdownInterval) {
                clearInterval(countdownInterval);
            }
            window.location.href = '{{ url_for("auth.logout") }}';
        }
        
        function selectCode(element) {
            // 选择备用码文本
            const range = document.createRange();
            range.selectNodeContents(element);
            const selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(range);
            
            // 复制到剪贴板
            try {
                document.execCommand('copy');
                $(element).addClass('bg-success text-white');
                setTimeout(function() {
                    $(element).removeClass('bg-success text-white');
                }, 1000);
            } catch (err) {
                // 复制失败，忽略
            }
        }
        
        function downloadBackupCodes() {
            const codes = [];
            $('.backup-code').each(function() {
                codes.push($(this).text());
            });
            
            const content = `量化交易系统 - Google Authenticator 备用码
生成时间: ${new Date().toLocaleString()}
用户: {{ username }}

备用码列表:
${codes.map((code, index) => `${index + 1}. ${code}`).join('\n')}

重要提示:
- 请妥善保存这些备用码
- 每个备用码只能使用一次
- 当您无法使用Google Authenticator时可以使用备用码登录
- 请勿将备用码分享给他人`;
            
            const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `Google认证备用码_${new Date().toISOString().slice(0, 10)}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }
        
        function printBackupCodes() {
            const codes = [];
            $('.backup-code').each(function() {
                codes.push($(this).text());
            });
            
            const printContent = `
                <html>
                <head>
                    <title>Google Authenticator 备用码</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        h1 { color: #333; }
                        .codes { display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px; margin: 20px 0; }
                        .code { border: 1px solid #ccc; padding: 10px; text-align: center; font-family: monospace; font-weight: bold; }
                        .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; }
                    </style>
                </head>
                <body>
                    <h1>量化交易系统 - Google Authenticator 备用码</h1>
                    <p><strong>生成时间:</strong> ${new Date().toLocaleString()}</p>
                    <p><strong>用户:</strong> {{ username }}</p>
                    <div class="codes">
                        ${codes.map(code => `<div class="code">${code}</div>`).join('')}
                    </div>
                    <div class="warning">
                        <h3>重要提示:</h3>
                        <ul>
                            <li>请妥善保存这些备用码</li>
                            <li>每个备用码只能使用一次</li>
                            <li>当您无法使用Google Authenticator时可以使用备用码登录</li>
                            <li>请勿将备用码分享给他人</li>
                        </ul>
                    </div>
                </body>
                </html>
            `;
            
            const printWindow = window.open('', '_blank');
            printWindow.document.write(printContent);
            printWindow.document.close();
            printWindow.print();
        }
    </script>
</body>
</html>
