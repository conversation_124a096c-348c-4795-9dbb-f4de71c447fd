<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置双因素认证 - 量化交易系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet" crossorigin="anonymous" referrerpolicy="no-referrer">
    <!-- Bootstrap Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.10.0/font/bootstrap-icons.min.css" rel="stylesheet" crossorigin="anonymous" referrerpolicy="no-referrer">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', '<PERSON>Fang SC', 'Hiragino Sans GB', sans-serif;
            padding: 20px 0;
        }
        
        .setup-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .setup-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
        }
        
        .setup-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .setup-header h2 {
            margin: 0;
            font-weight: 600;
            font-size: 1.5rem;
        }
        
        .setup-header .subtitle {
            margin-top: 8px;
            opacity: 0.9;
            font-size: 0.9rem;
        }
        
        .setup-body {
            padding: 30px;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        
        .step {
            display: flex;
            align-items: center;
            margin: 0 10px;
        }
        
        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .step.active .step-number {
            background: #667eea;
            color: white;
        }
        
        .step.completed .step-number {
            background: #28a745;
            color: white;
        }
        
        .step-line {
            width: 50px;
            height: 2px;
            background: #e9ecef;
            margin: 0 10px;
        }
        
        .step.completed + .step .step-line {
            background: #28a745;
        }
        
        .qr-code-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
            border: 2px dashed #dee2e6;
        }
        
        .qr-code-image {
            max-width: 200px;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .secret-key-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }
        
        .secret-key {
            font-family: 'Courier New', monospace;
            font-size: 1.1rem;
            font-weight: 600;
            color: #495057;
            word-break: break-all;
            background: white;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        
        .backup-codes-container {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .backup-codes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        
        .backup-code {
            background: white;
            padding: 8px 12px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-weight: 600;
            text-align: center;
            border: 1px solid #ffeaa7;
        }
        
        .form-floating > .form-control {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 12px 16px;
            height: auto;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-floating > .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .verification-code-input {
            text-align: center;
            font-size: 1.2rem;
            letter-spacing: 0.5rem;
            font-weight: 600;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
        }
        
        .alert {
            border-radius: 12px;
            border: none;
            margin-bottom: 20px;
        }
        
        .instruction-list {
            padding-left: 0;
            list-style: none;
        }
        
        .instruction-list li {
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
            position: relative;
            padding-left: 30px;
        }
        
        .instruction-list li:before {
            content: counter(step-counter);
            counter-increment: step-counter;
            position: absolute;
            left: 0;
            top: 10px;
            background: #667eea;
            color: white;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .instruction-list {
            counter-reset: step-counter;
        }
        
        @media (max-width: 576px) {
            .setup-container {
                padding: 0 15px;
            }
            
            .setup-header, .setup-body {
                padding: 20px;
            }
            
            .step-indicator {
                flex-direction: column;
                align-items: center;
            }
            
            .step {
                margin: 5px 0;
            }
            
            .step-line {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-card">
            <!-- 设置头部 -->
            <div class="setup-header">
                <i class="bi bi-shield-check fs-1 mb-3"></i>
                <h2>设置双因素认证</h2>
                <div class="subtitle">增强账户安全性</div>
            </div>
            
            <!-- 设置内容 -->
            <div class="setup-body">
                <!-- 步骤指示器 -->
                <div class="step-indicator">
                    <div class="step active" id="step1">
                        <div class="step-number">1</div>
                    </div>
                    <div class="step-line"></div>
                    <div class="step" id="step2">
                        <div class="step-number">2</div>
                    </div>
                    <div class="step-line"></div>
                    <div class="step" id="step3">
                        <div class="step-number">3</div>
                    </div>
                </div>
                
                <!-- 错误提示 -->
                {% if error_message %}
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    {{ error_message }}
                </div>
                {% endif %}
                
                <!-- 步骤1: 下载应用 -->
                <div class="setup-step" id="setupStep1">
                    <h5 class="mb-3">
                        <i class="bi bi-download text-primary me-2"></i>
                        步骤1: 下载认证应用
                    </h5>
                    
                    <p class="text-muted mb-3">
                        请在您的手机上下载并安装Google Authenticator应用：
                    </p>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="bi bi-apple fs-1 text-dark mb-2"></i>
                                    <h6>iOS设备</h6>
                                    <small class="text-muted">App Store搜索<br>"Google Authenticator"</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="bi bi-google-play fs-1 text-success mb-2"></i>
                                    <h6>Android设备</h6>
                                    <small class="text-muted">Google Play搜索<br>"Google Authenticator"</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center mt-3">
                        <button type="button" class="btn btn-primary" onclick="nextStep(2)">
                            下一步：扫描二维码
                            <i class="bi bi-arrow-right ms-2"></i>
                        </button>
                    </div>
                </div>
                
                <!-- 步骤2: 扫描二维码 -->
                <div class="setup-step" id="setupStep2" style="display: none;">
                    <h5 class="mb-3">
                        <i class="bi bi-qr-code text-primary me-2"></i>
                        步骤2: 扫描二维码
                    </h5>
                    
                    <p class="text-muted mb-3">
                        使用Google Authenticator应用扫描下方二维码：
                    </p>
                    
                    <!-- 二维码显示 -->
                    <div class="qr-code-container">
                        {% if qr_code_url %}
                        <img src="{{ qr_code_url }}" alt="Google Authenticator二维码" class="qr-code-image">
                        {% else %}
                        <div class="text-muted">
                            <i class="bi bi-exclamation-circle fs-1 mb-2"></i>
                            <p>二维码生成失败，请使用手动输入方式</p>
                        </div>
                        {% endif %}
                    </div>
                    
                    <!-- 手动输入密钥 -->
                    <div class="secret-key-container">
                        <h6 class="mb-2">
                            <i class="bi bi-key me-2"></i>
                            手动输入密钥
                        </h6>
                        <p class="text-muted small mb-2">
                            如果无法扫描二维码，请在Google Authenticator中手动输入以下密钥：
                        </p>
                        <div class="secret-key" id="secretKey">{{ secret_key }}</div>
                        <div class="text-center mt-2">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="copySecretKey()">
                                <i class="bi bi-clipboard me-1"></i>复制密钥
                            </button>
                        </div>
                    </div>
                    
                    <div class="text-center mt-3">
                        <button type="button" class="btn btn-secondary me-2" onclick="prevStep(1)">
                            <i class="bi bi-arrow-left me-2"></i>上一步
                        </button>
                        <button type="button" class="btn btn-primary" onclick="nextStep(3)">
                            下一步：验证设置
                            <i class="bi bi-arrow-right ms-2"></i>
                        </button>
                    </div>
                </div>
                
                <!-- 步骤3: 验证设置 -->
                <div class="setup-step" id="setupStep3" style="display: none;">
                    <h5 class="mb-3">
                        <i class="bi bi-check-circle text-primary me-2"></i>
                        步骤3: 验证设置
                    </h5>
                    
                    <p class="text-muted mb-3">
                        请输入Google Authenticator应用中显示的6位验证码以完成设置：
                    </p>
                    
                    <form id="verifyForm" method="POST" action="{{ url_for('auth.setup_google_verify') }}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                        <input type="hidden" name="secret_key" value="{{ secret_key }}">
                        
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control verification-code-input" 
                                   id="verification_code" name="verification_code" 
                                   placeholder="000000" maxlength="6" pattern="[0-9]{6}" required>
                            <label for="verification_code">
                                <i class="bi bi-key me-2"></i>6位验证码
                            </label>
                        </div>
                        
                        <div class="text-center">
                            <button type="button" class="btn btn-secondary me-2" onclick="prevStep(2)">
                                <i class="bi bi-arrow-left me-2"></i>上一步
                            </button>
                            <button type="submit" class="btn btn-primary" id="verifyBtn">
                                <span class="spinner-border spinner-border-sm me-2" role="status" style="display: none;"></span>
                                <i class="bi bi-check-lg me-2"></i>
                                完成设置
                            </button>
                        </div>
                    </form>
                </div>
                
                <!-- 备用码显示 -->
                {% if backup_codes %}
                <div class="backup-codes-container" id="backupCodesSection" style="display: none;">
                    <h6 class="mb-2">
                        <i class="bi bi-shield-exclamation text-warning me-2"></i>
                        备用码
                    </h6>
                    <p class="text-muted small mb-2">
                        请妥善保存以下备用码，当您无法使用Google Authenticator时可以使用这些备用码登录：
                    </p>
                    <div class="backup-codes">
                        {% for code in backup_codes %}
                        <div class="backup-code">{{ code }}</div>
                        {% endfor %}
                    </div>
                    <div class="text-center mt-3">
                        <button type="button" class="btn btn-sm btn-outline-warning" onclick="downloadBackupCodes()">
                            <i class="bi bi-download me-1"></i>下载备用码
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-primary ms-2" onclick="printBackupCodes()">
                            <i class="bi bi-printer me-1"></i>打印备用码
                        </button>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <!-- jQuery -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.0/jquery.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    
    <script>
        $(document).ready(function() {
            // 验证码输入格式化
            $('#verification_code').on('input', function() {
                this.value = this.value.replace(/\D/g, '').substring(0, 6);
            });
            
            // 表单提交处理
            $('#verifyForm').on('submit', function(e) {
                const btn = $('#verifyBtn');
                const spinner = btn.find('.spinner-border');
                
                btn.prop('disabled', true);
                spinner.show();
                
                const code = $('#verification_code').val().trim();
                if (code.length !== 6) {
                    e.preventDefault();
                    alert('请输入6位验证码');
                    btn.prop('disabled', false);
                    spinner.hide();
                }
            });
        });
        
        function nextStep(step) {
            // 隐藏当前步骤
            $('.setup-step').hide();
            
            // 显示目标步骤
            $(`#setupStep${step}`).show();
            
            // 更新步骤指示器
            $('.step').removeClass('active completed');
            for (let i = 1; i < step; i++) {
                $(`#step${i}`).addClass('completed');
            }
            $(`#step${step}`).addClass('active');
            
            // 特殊处理
            if (step === 3) {
                $('#verification_code').focus();
            }
        }
        
        function prevStep(step) {
            nextStep(step);
        }
        
        function copySecretKey() {
            const secretKey = document.getElementById('secretKey').textContent;
            navigator.clipboard.writeText(secretKey).then(function() {
                alert('密钥已复制到剪贴板');
            }).catch(function() {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = secretKey;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('密钥已复制到剪贴板');
            });
        }
        
        function downloadBackupCodes() {
            const codes = [];
            $('.backup-code').each(function() {
                codes.push($(this).text());
            });
            
            const content = `量化交易系统 - Google Authenticator 备用码\n生成时间: ${new Date().toLocaleString()}\n\n${codes.join('\n')}\n\n请妥善保存这些备用码，每个备用码只能使用一次。`;
            
            const blob = new Blob([content], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `backup_codes_${new Date().getTime()}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }
        
        function printBackupCodes() {
            const codes = [];
            $('.backup-code').each(function() {
                codes.push($(this).text());
            });
            
            const printContent = `
                <h2>量化交易系统 - Google Authenticator 备用码</h2>
                <p>生成时间: ${new Date().toLocaleString()}</p>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px; margin: 20px 0;">
                    ${codes.map(code => `<div style="border: 1px solid #ccc; padding: 10px; text-align: center; font-family: monospace; font-weight: bold;">${code}</div>`).join('')}
                </div>
                <p><strong>重要提示：</strong></p>
                <ul>
                    <li>请妥善保存这些备用码</li>
                    <li>每个备用码只能使用一次</li>
                    <li>当您无法使用Google Authenticator时可以使用备用码登录</li>
                </ul>
            `;
            
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                    <head>
                        <title>Google Authenticator 备用码</title>
                        <style>
                            body { font-family: Arial, sans-serif; margin: 20px; }
                            h2 { color: #333; }
                        </style>
                    </head>
                    <body>${printContent}</body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }
    </script>
</body>
</html>
