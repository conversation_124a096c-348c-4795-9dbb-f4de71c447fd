<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - 量化交易系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet" crossorigin="anonymous" referrerpolicy="no-referrer">
    <!-- Bootstrap Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.10.0/font/bootstrap-icons.min.css" rel="stylesheet" crossorigin="anonymous" referrerpolicy="no-referrer">
    
    <!-- 自定义CSS -->
    <link href="{{ url_for('static', filename='auth/css/auth.css') }}" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
        }
        
        .login-container {
            max-width: 400px;
            width: 100%;
            padding: 0 20px;
        }
        
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
        }
        
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .login-header h2 {
            margin: 0;
            font-weight: 600;
            font-size: 1.5rem;
        }
        
        .login-header .subtitle {
            margin-top: 8px;
            opacity: 0.9;
            font-size: 0.9rem;
        }
        
        .login-body {
            padding: 30px;
        }
        
        .form-floating {
            margin-bottom: 20px;
        }
        
        .form-floating > .form-control {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 12px 16px;
            height: auto;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-floating > .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .form-floating > label {
            padding: 12px 16px;
            color: #6c757d;
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 12px;
            font-size: 1rem;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .btn-login:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: none;
        }
        
        .alert {
            border-radius: 12px;
            border: none;
            margin-bottom: 20px;
        }
        
        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .alert-success {
            background-color: #d1edff;
            color: #0c5460;
        }
        
        .form-check {
            margin: 20px 0;
        }
        
        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }
        
        .loading-spinner {
            display: none;
            margin-right: 8px;
        }
        
        .google-auth-section {
            display: none;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }
        
        .google-auth-section.show {
            display: block;
        }
        
        .verification-code-input {
            text-align: center;
            font-size: 1.2rem;
            letter-spacing: 0.5rem;
            font-weight: 600;
        }
        
        .backup-code-link {
            color: #667eea;
            text-decoration: none;
            font-size: 0.9rem;
        }
        
        .backup-code-link:hover {
            color: #764ba2;
            text-decoration: underline;
        }
        
        .system-status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 10px 15px;
            font-size: 0.8rem;
            color: #6c757d;
            backdrop-filter: blur(10px);
        }

        .login-progress {
            display: none;
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 12px;
            border-left: 4px solid #667eea;
        }

        .login-progress.show {
            display: block;
        }

        .progress-step {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .progress-step:last-child {
            margin-bottom: 0;
        }

        .progress-step .step-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-size: 0.7rem;
        }

        .progress-step.completed .step-icon {
            background: #28a745;
            color: white;
        }

        .progress-step.current .step-icon {
            background: #667eea;
            color: white;
        }

        .progress-step.pending .step-icon {
            background: #e9ecef;
            color: #6c757d;
        }

        .progress-step.completed .step-text {
            color: #28a745;
            font-weight: 500;
        }

        .progress-step.current .step-text {
            color: #667eea;
            font-weight: 600;
        }

        .progress-step.pending .step-text {
            color: #6c757d;
        }
        
        @media (max-width: 576px) {
            .login-container {
                padding: 0 15px;
            }
            
            .login-header, .login-body {
                padding: 20px;
            }
            
            .system-status {
                position: static;
                margin: 20px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <!-- 系统状态指示器 -->
    <div class="system-status">
        <i class="bi bi-shield-check text-success"></i>
        安全登录
    </div>

    <div class="login-container">
        <div class="login-card">
            <!-- 登录头部 -->
            <div class="login-header">
                <i class="bi bi-graph-up-arrow fs-1 mb-3"></i>
                <h2>量化交易系统</h2>
                <div class="subtitle">安全登录验证</div>
            </div>
            
            <!-- 登录表单 -->
            <div class="login-body">
                <!-- Flash消息显示 -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            {% if category == 'error' %}
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% elif category == 'success' %}
                                <div class="alert alert-success alert-dismissible fade show" role="alert">
                                    <i class="bi bi-check-circle-fill me-2"></i>
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% elif category == 'warning' %}
                                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                                    <i class="bi bi-exclamation-triangle me-2"></i>
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% elif category == 'info' %}
                                <div class="alert alert-info alert-dismissible fade show" role="alert">
                                    <i class="bi bi-info-circle-fill me-2"></i>
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endif %}
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <!-- 错误提示 -->
                {% if error_message %}
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    {{ error_message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                {% endif %}

                <!-- 成功提示 -->
                {% if success_message %}
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle-fill me-2"></i>
                    {{ success_message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                {% endif %}

                <!-- 登录进度指示器 -->
                <div class="login-progress" id="loginProgress">
                    <div class="progress-step completed" id="step1">
                        <div class="step-icon">
                            <i class="bi bi-check"></i>
                        </div>
                        <div class="step-text">用户名和密码验证</div>
                    </div>
                    <div class="progress-step current" id="step2">
                        <div class="step-icon">
                            <i class="bi bi-shield-check"></i>
                        </div>
                        <div class="step-text">双因素认证验证</div>
                    </div>
                    <div class="progress-step pending" id="step3">
                        <div class="step-icon">
                            <i class="bi bi-box-arrow-in-right"></i>
                        </div>
                        <div class="step-text">登录完成</div>
                    </div>
                </div>

                <!-- 基础登录表单 -->
                <form id="loginForm" method="POST" action="{{ url_for('auth.login') }}">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                    
                    <!-- 用户名输入 -->
                    <div class="form-floating">
                        <input type="text" class="form-control" id="username" name="username"
                               placeholder="请输入用户名" required autocomplete="username"
                               value="{{ username or request.form.username or '' }}">
                        <label for="username">
                            <i class="bi bi-person me-2"></i>用户名
                        </label>
                    </div>
                    
                    <!-- 密码输入 -->
                    <div class="form-floating">
                        <input type="password" class="form-control" id="password" name="password"
                               placeholder="请输入密码" required autocomplete="current-password"
                               value="{{ password or '' }}">
                        <label for="password">
                            <i class="bi bi-lock me-2"></i>密码
                        </label>
                    </div>
                    
                    <!-- 记住我选项 -->
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="remember_me" name="remember_me"
                               {% if remember_me %}checked{% endif %}>
                        <label class="form-check-label" for="remember_me">
                            记住登录状态 (7天)
                        </label>
                    </div>
                    
                    <!-- Google验证码部分 -->
                    <div class="google-auth-section" id="googleAuthSection">
                        <div class="text-center mb-3">
                            <i class="bi bi-shield-check text-primary fs-4"></i>
                            <h6 class="mt-2 mb-0">双因素认证</h6>
                            <small class="text-muted">
                                用户名和密码验证成功！<br>
                                请输入Google Authenticator验证码完成登录
                            </small>
                        </div>
                        
                        <div class="form-floating">
                            <input type="text" class="form-control verification-code-input" 
                                   id="google_code" name="google_code" 
                                   placeholder="000000" maxlength="6" pattern="[0-9]{6}"
                                   autocomplete="one-time-code">
                            <label for="google_code">
                                <i class="bi bi-key me-2"></i>6位验证码
                            </label>
                        </div>
                        
                        <div class="text-center mt-3">
                            <a href="#" class="backup-code-link" onclick="showBackupCodeInput()">
                                使用备用码登录
                            </a>
                        </div>
                    </div>
                    
                    <!-- 备用码输入部分 -->
                    <div class="google-auth-section" id="backupCodeSection">
                        <div class="text-center mb-3">
                            <i class="bi bi-key text-warning fs-4"></i>
                            <h6 class="mt-2 mb-0">备用码验证</h6>
                            <small class="text-muted">请输入8位备用码</small>
                        </div>
                        
                        <div class="form-floating">
                            <input type="text" class="form-control verification-code-input" 
                                   id="backup_code" name="backup_code" 
                                   placeholder="12345678" maxlength="8" pattern="[0-9]{8}">
                            <label for="backup_code">
                                <i class="bi bi-shield-exclamation me-2"></i>8位备用码
                            </label>
                        </div>
                        
                        <div class="text-center mt-3">
                            <a href="#" class="backup-code-link" onclick="showGoogleCodeInput()">
                                返回验证码登录
                            </a>
                        </div>
                    </div>
                    
                    <!-- 登录按钮 -->
                    <button type="submit" class="btn btn-login" id="loginBtn">
                        <span class="loading-spinner spinner-border spinner-border-sm" role="status"></span>
                        <i class="bi bi-box-arrow-in-right me-2"></i>
                        <span id="loginBtnText">登录</span>
                    </button>
                </form>
            </div>
        </div>
        
        <!-- 版权信息 -->
        <div class="text-center mt-4">
            <small class="text-white-50">
                © 2025 量化交易系统 | 安全可靠的交易管理平台
            </small>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <!-- jQuery -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.0/jquery.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    
    <!-- 自定义JavaScript -->
    <script src="{{ url_for('static', filename='auth/js/auth.js') }}?v={{ range(1000, 9999) | random }}"></script>
    
    <script>
        // 登录表单处理
        $(document).ready(function() {
            // 自动关闭成功消息
            setTimeout(function() {
                $('.alert-success').fadeOut();
            }, 3000);

            $('#loginForm').on('submit', function(e) {
                const btn = $('#loginBtn');
                const btnText = $('#loginBtnText');
                const spinner = $('.loading-spinner');

                // 清除之前的错误提示
                $('.alert-danger').not('[data-permanent]').remove();

                // 显示加载状态
                btn.prop('disabled', true);
                spinner.show();
                btnText.text('登录中...');

                // 表单验证
                const username = $('#username').val().trim();
                const password = $('#password').val();

                if (!username || !password) {
                    e.preventDefault();
                    showError('请输入用户名和密码');
                    resetLoginButton();
                    return;
                }

                // 如果显示了Google认证部分，验证验证码
                if ($('#googleAuthSection').hasClass('show')) {
                    const googleCode = $('#google_code').val().trim();
                    const backupCode = $('#backup_code').val().trim();

                    if (!googleCode && !backupCode) {
                        e.preventDefault();
                        showError('请输入Google验证码或备用码');
                        resetLoginButton();
                        return;
                    }

                    // 验证码格式检查
                    if (googleCode && (googleCode.length !== 6 || !/^\d{6}$/.test(googleCode))) {
                        e.preventDefault();
                        showError('Google验证码必须是6位数字');
                        resetLoginButton();
                        return;
                    }

                    if (backupCode && (backupCode.length !== 8 || !/^\d{8}$/.test(backupCode))) {
                        e.preventDefault();
                        showError('备用码必须是8位数字');
                        resetLoginButton();
                        return;
                    }

                    // 更新进度到最后一步
                    updateProgressStep(2, 'completed');
                    updateProgressStep(3, 'current');
                    btnText.text('完成登录...');
                }

                // 设置超时处理
                setTimeout(function() {
                    if (btn.prop('disabled')) {
                        showError('登录请求超时，请检查网络连接后重试');
                        resetLoginButton();
                    }
                }, 30000); // 30秒超时
            });
            
            // 验证码输入框自动格式化
            $('#google_code').on('input', function() {
                this.value = this.value.replace(/\D/g, '').substring(0, 6);
            });
            
            $('#backup_code').on('input', function() {
                this.value = this.value.replace(/\D/g, '').substring(0, 8);
            });
            
            // 检查是否需要显示Google认证
            {% if show_google_auth %}
            showGoogleAuthStep();
            {% endif %}
        });
        
        function resetLoginButton() {
            const btn = $('#loginBtn');
            const btnText = $('#loginBtnText');
            const spinner = $('.loading-spinner');
            
            btn.prop('disabled', false);
            spinner.hide();
            btnText.text('登录');
        }
        
        function showError(message) {
            // 移除之前的动态错误提示
            $('.alert-danger').not('[data-permanent]').remove();

            const alertHtml = `
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('.login-body form').prepend(alertHtml);

            // 滚动到错误提示
            $('.alert-danger').get(0).scrollIntoView({ behavior: 'smooth', block: 'center' });
        }

        function showSuccess(message) {
            // 移除之前的成功提示
            $('.alert-success').remove();

            const alertHtml = `
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle-fill me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('.login-body form').prepend(alertHtml);

            // 3秒后自动关闭
            setTimeout(function() {
                $('.alert-success').fadeOut();
            }, 3000);
        }
        
        function showBackupCodeInput() {
            $('#googleAuthSection').removeClass('show');
            $('#backupCodeSection').addClass('show');
            $('#backup_code').focus();
        }
        
        function showGoogleCodeInput() {
            $('#backupCodeSection').removeClass('show');
            $('#googleAuthSection').addClass('show');
            $('#google_code').focus();
        }

        // 显示Google认证步骤
        function showGoogleAuthStep() {
            // 显示进度指示器
            $('#loginProgress').addClass('show');

            // 显示Google认证部分
            $('#googleAuthSection').addClass('show');
            $('#google_code').focus();

            // 更新页面标题提示
            $('.login-header .subtitle').text('请完成双因素认证');

            // 滚动到Google认证部分
            $('#googleAuthSection')[0].scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
        }

        // 更新进度步骤
        function updateProgressStep(stepNumber, status) {
            const step = $(`#step${stepNumber}`);
            step.removeClass('completed current pending').addClass(status);

            if (status === 'completed') {
                step.find('.step-icon i').removeClass().addClass('bi bi-check');
            } else if (status === 'current') {
                step.find('.step-icon i').removeClass().addClass('bi bi-arrow-right');
            }
        }

        // 登录成功后的处理
        function handleLoginSuccess() {
            // 更新所有步骤为完成状态
            updateProgressStep(1, 'completed');
            updateProgressStep(2, 'completed');
            updateProgressStep(3, 'completed');

            // 显示成功消息
            showSuccess('登录成功！正在跳转...');

            // 延迟跳转以显示成功状态
            setTimeout(function() {
                window.location.reload();
            }, 1500);
        }
    </script>
</body>
</html>
