{% extends "base_modern.html" %}

{% block title %}资金曲线 - 量化交易系统管理界面{% endblock %}

{% block page_title %}资金曲线{% endblock %}

{% block extra_css %}
<!-- Chart.js Annotation Plugin -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/chartjs-plugin-annotation/2.2.1/chartjs-plugin-annotation.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<!-- Chart.js Time Adapter -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/chartjs-adapter-moment/1.0.1/chartjs-adapter-moment.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<style>
    .strategy-card {
        transition: all 0.3s ease;
        border: 1px solid #e0e0e0;
        border-radius: 12px;
        background: #fff;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .strategy-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0,0,0,0.15);
    }
    
    .strategy-card.selected {
        border-color: #007bff;
        background: #f8f9ff;
    }
    
    .chart-container {
        position: relative;
        background: #fff;
        border-radius: 8px;
        padding: 15px;
    }

    .card {
        border: 1px solid #e0e0e0;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        margin-bottom: 0;
    }

    .card:hover {
        box-shadow: 0 4px 16px rgba(0,0,0,0.15);
    }

    .card-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 1px solid #dee2e6;
        border-radius: 12px 12px 0 0 !important;
        padding: 12px 20px;
    }

    .card-header h6 {
        color: #495057;
        font-weight: 600;
        margin: 0;
    }

    .card-body {
        padding: 20px;
    }
    
    .statistics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 24px;
        margin-top: 24px;
    }

    .stat-card {
        background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
        border-radius: 16px;
        padding: 28px 24px;
        text-align: center;
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.08),
            0 2px 8px rgba(0, 0, 0, 0.04),
            inset 0 1px 0 rgba(255, 255, 255, 0.9);
        border: 1px solid rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        backdrop-filter: blur(10px);
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-4px) scale(1.02);
        box-shadow:
            0 16px 48px rgba(0, 0, 0, 0.12),
            0 8px 16px rgba(0, 0, 0, 0.08),
            inset 0 1px 0 rgba(255, 255, 255, 0.9);
    }

    .stat-card:hover::before {
        opacity: 1;
    }

    .stat-card h6 {
        font-size: 1.1rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 20px;
        letter-spacing: -0.02em;
        position: relative;
    }

    .stat-card h6::after {
        content: '';
        position: absolute;
        bottom: -8px;
        left: 50%;
        transform: translateX(-50%);
        width: 30px;
        height: 2px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        border-radius: 1px;
    }

    .stat-value {
        font-size: 2.2rem;
        font-weight: 700;
        margin-bottom: 8px;
        line-height: 1.2;
        letter-spacing: -0.03em;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        transition: all 0.2s ease;
        font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .stat-label {
        color: #64748b;
        font-size: 0.85rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        margin-top: 4px;
        opacity: 0.8;
        transition: color 0.2s ease;
    }

    .stat-card:hover .stat-label {
        color: #475569;
        opacity: 1;
    }

    /* 优化的颜色方案 */
    .positive {
        color: #059669;
        background: linear-gradient(135deg, #059669 0%, #10b981 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .negative {
        color: #dc2626;
        background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .neutral {
        color: #475569;
        background: linear-gradient(135deg, #475569 0%, #64748b 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    /* 特殊指标样式 */
    .stat-value.highlight {
        position: relative;
    }

    .stat-value.highlight::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: calc(100% + 16px);
        height: calc(100% + 8px);
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        border-radius: 12px;
        z-index: -1;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .stat-card:hover .stat-value.highlight::before {
        opacity: 1;
    }

    /* 数值分组样式 */
    .stat-group {
        position: relative;
        margin-bottom: 16px;
    }

    .stat-group::after {
        content: '';
        position: absolute;
        bottom: -8px;
        left: 20%;
        right: 20%;
        height: 1px;
        background: linear-gradient(90deg, transparent 0%, rgba(102, 126, 234, 0.2) 50%, transparent 100%);
    }

    .stat-group:last-child::after {
        display: none;
    }

    /* 数值动画效果 */
    .stat-value {
        animation: fadeInUp 0.6s ease-out;
        animation-fill-mode: both;
    }

    .stat-card:nth-child(1) .stat-value { animation-delay: 0.1s; }
    .stat-card:nth-child(2) .stat-value { animation-delay: 0.2s; }
    .stat-card:nth-child(3) .stat-value { animation-delay: 0.3s; }
    .stat-card:nth-child(4) .stat-value { animation-delay: 0.4s; }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* 特殊数值样式 */
    .stat-value.large {
        font-size: 2.8rem;
        font-weight: 800;
    }

    .stat-value.medium {
        font-size: 2.0rem;
        font-weight: 700;
    }

    .stat-value.small {
        font-size: 1.4rem;
        font-weight: 600;
    }

    /* 图标装饰 */
    .stat-label::before {
        content: '';
        display: inline-block;
        width: 4px;
        height: 4px;
        background: currentColor;
        border-radius: 50%;
        margin-right: 6px;
        opacity: 0.6;
    }

    /* 数值单位样式 */
    .stat-unit {
        font-size: 0.7em;
        opacity: 0.8;
        margin-left: 2px;
    }

    /* 响应式优化 */
    @media (max-width: 768px) {
        .statistics-grid {
            grid-template-columns: 1fr;
            gap: 16px;
        }

        .stat-card {
            padding: 20px 16px;
        }

        .stat-value {
            font-size: 1.8rem;
        }

        .stat-value.large {
            font-size: 2.2rem;
        }

        .stat-value.medium {
            font-size: 1.6rem;
        }

        .stat-value.small {
            font-size: 1.2rem;
        }
    }

    @media (max-width: 480px) {
        .stat-value {
            font-size: 1.6rem;
        }

        .stat-value.large {
            font-size: 1.9rem;
        }

        .stat-value.medium {
            font-size: 1.4rem;
        }

        .stat-value.small {
            font-size: 1.1rem;
        }

        .stat-label {
            font-size: 0.8rem;
        }

        .stat-card h6 {
            font-size: 1rem;
        }
    }
    
    .loading-spinner {
        display: none;
        text-align: center;
        padding: 40px;
    }
    
    .time-range-controls {
        background: #fff;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .btn-group-custom {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }
    
    .btn-custom {
        padding: 8px 16px;
        border: 1px solid #ddd;
        background: #fff;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .btn-custom:hover {
        background: #f8f9fa;
        border-color: #007bff;
    }
    
    .btn-custom.active {
        background: #007bff;
        color: #fff;
        border-color: #007bff;
    }

    /* 最大回撤悬停提示框样式 - 增强版 */
    .drawdown-tooltip {
        position: absolute !important;
        background: rgba(0, 0, 0, 0.95) !important;
        color: white !important;
        padding: 12px 16px !important;
        border-radius: 8px !important;
        font-size: 13px !important;
        line-height: 1.4 !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5) !important;
        z-index: 9999 !important;
        pointer-events: none !important;
        opacity: 0 !important;
        transition: opacity 0.2s ease-in-out !important;
        max-width: 280px !important;
        border: 2px solid #ff6384 !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    }

    .drawdown-tooltip.show {
        opacity: 1 !important;
        display: block !important;
    }

    .drawdown-tooltip .tooltip-title {
        font-weight: bold;
        color: #ff6384;
        margin-bottom: 8px;
        font-size: 14px;
    }

    .drawdown-tooltip .tooltip-section {
        margin-bottom: 6px;
    }

    .drawdown-tooltip .tooltip-label {
        color: #ccc;
        font-size: 12px;
    }

    .drawdown-tooltip .tooltip-value {
        color: white;
        font-weight: 500;
    }

    .drawdown-tooltip .recovery-status {
        margin-top: 8px;
        padding-top: 8px;
        border-top: 1px solid rgba(255, 255, 255, 0.2);
    }

    .drawdown-tooltip .recovery-status.recovered {
        color: #4caf50;
    }

    .drawdown-tooltip .recovery-status.not-recovered {
        color: #ff9800;
    }

    /* 回撤事件分析样式 */
    .filter-group {
        margin-bottom: 1rem;
    }

    .filter-group .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
    }

    .filter-group .form-control-sm {
        border-radius: 6px;
        border: 1px solid #ced4da;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .filter-group .form-control-sm:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    #drawdownEventsTable {
        font-size: 0.9rem;
    }

    #drawdownEventsTable th {
        background: linear-gradient(135deg, #343a40 0%, #495057 100%);
        color: white;
        font-weight: 600;
        text-align: center;
        vertical-align: middle;
        border: none;
        padding: 12px 8px;
        font-size: 0.85rem;
    }

    #drawdownEventsTable td {
        text-align: center;
        vertical-align: middle;
        padding: 10px 8px;
        border-bottom: 1px solid #dee2e6;
    }

    #drawdownEventsTable tbody tr:hover {
        background-color: #f8f9fa;
        transition: background-color 0.15s ease;
    }

    .drawdown-value {
        font-weight: 600;
    }

    .drawdown-value.high {
        color: #dc3545;
    }

    .drawdown-value.medium {
        color: #fd7e14;
    }

    .drawdown-value.low {
        color: #28a745;
    }

    .status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .status-badge.completed {
        background-color: #d4edda;
        color: #155724;
    }

    .status-badge.ongoing {
        background-color: #fff3cd;
        color: #856404;
    }

    .table-responsive {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* 分页样式 */
    .pagination {
        margin-bottom: 0;
    }

    .pagination .page-link {
        color: #007bff;
        border: 1px solid #dee2e6;
        padding: 0.5rem 0.75rem;
        margin: 0 2px;
        border-radius: 6px;
        transition: all 0.15s ease-in-out;
    }

    .pagination .page-link:hover {
        color: #0056b3;
        background-color: #e9ecef;
        border-color: #adb5bd;
    }

    .pagination .page-item.active .page-link {
        background-color: #007bff;
        border-color: #007bff;
        color: white;
    }

    .pagination .page-item.disabled .page-link {
        color: #6c757d;
        background-color: #fff;
        border-color: #dee2e6;
        cursor: not-allowed;
    }

    .pagination .page-item.disabled .page-link:hover {
        color: #6c757d;
        background-color: #fff;
        border-color: #dee2e6;
    }

    #pageInfo {
        font-size: 0.9rem;
        color: #6c757d;
        white-space: nowrap;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 策略选择区域 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-graph-up-arrow me-2"></i>
                        策略选择
                    </h5>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary btn-sm" onclick="refreshStrategies()">
                            <i class="bi bi-arrow-clockwise"></i>
                            刷新
                        </button>
                        <button class="btn btn-primary btn-sm" onclick="compareStrategies()" id="compareBtn" disabled>
                            <i class="bi bi-bar-chart"></i>
                            对比策略
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="strategiesContainer" class="row">
                        <div class="col-12 text-center">
                            <div class="loading-spinner">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="mt-2">正在加载策略列表...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 时间范围控制 -->
    <div class="time-range-controls" id="timeRangeControls" style="display: none;">
        <h6 class="mb-3">
            <i class="bi bi-calendar-range me-2"></i>
            时间范围设置
        </h6>
        <div class="row">
            <div class="col-md-6">
                <label class="form-label">开始时间</label>
                <input type="datetime-local" class="form-control" id="startTime">
            </div>
            <div class="col-md-6">
                <label class="form-label">结束时间</label>
                <input type="datetime-local" class="form-control" id="endTime">
            </div>
        </div>
        <div class="mt-3">
            <label class="form-label">快速选择</label>
            <div class="btn-group-custom">
                <button class="btn-custom" onclick="setTimeRange('7D')">近7天</button>
                <button class="btn-custom" onclick="setTimeRange('1M')">近1个月</button>
                <button class="btn-custom" onclick="setTimeRange('3M')">近3个月</button>
                <button class="btn-custom" onclick="setTimeRange('6M')">近6个月</button>
                <button class="btn-custom" onclick="setTimeRange('1Y')">近1年</button>
                <button class="btn-custom active" onclick="setTimeRange('ALL')">全部</button>
            </div>
        </div>
        <div class="mt-3" id="yearSelector" style="display: none;">
            <label class="form-label">年份选择</label>
            <div class="btn-group-custom" id="yearButtons">
                <!-- 年份按钮将通过JavaScript动态生成 -->
            </div>
        </div>
        <div class="mt-3">
            <button class="btn btn-primary" onclick="updateChart()">
                <i class="bi bi-graph-up"></i>
                更新图表
            </button>
        </div>
    </div>

    <!-- 统计指标区域 -->
    <div id="statisticsSection" style="display: none;">
        <h5 class="mb-3">
            <i class="bi bi-bar-chart-line me-2"></i>
            关键指标
        </h5>
        <div class="statistics-grid" id="statisticsGrid">
            <!-- 统计指标将通过JavaScript动态生成 -->
        </div>
    </div>

    <!-- 资金曲线图区域 -->
    <div class="row mb-4" id="fundCurveSection" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-bar-chart-line me-2"></i>
                        资金曲线图
                    </h6>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height: 450px;">
                        <canvas id="fundCurveChart"></canvas>
                        <!-- 最大回撤悬停提示框 -->
                        <div id="drawdownTooltip" class="drawdown-tooltip"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 回撤曲线图区域 -->
    <div class="row mb-4" id="drawdownChartSection" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-graph-down me-2"></i>
                        回撤曲线图
                    </h6>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height: 350px;">
                        <canvas id="drawdownChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 回撤事件分析区域 -->
    <div class="row mb-4" id="drawdownEventsSection" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-table me-2"></i>
                        回撤事件分析
                    </h6>
                </div>
                <div class="card-body">
                    <!-- 筛选控件 -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="filter-group">
                                <label class="form-label">回撤幅度范围 (%)</label>
                                <div class="row">
                                    <div class="col-6">
                                        <input type="number" class="form-control form-control-sm"
                                               id="minDrawdown" value="0" step="0.1" min="0" max="100"
                                               placeholder="最小回撤">
                                    </div>
                                    <div class="col-6">
                                        <input type="number" class="form-control form-control-sm"
                                               id="maxDrawdown" value="100" step="0.1" min="0" max="100"
                                               placeholder="最大回撤">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="filter-group">
                                <label class="form-label">回撤持续天数</label>
                                <div class="row">
                                    <div class="col-6">
                                        <input type="number" class="form-control form-control-sm"
                                               id="minDrawdownDays" value="0" step="1" min="0"
                                               placeholder="最小天数">
                                    </div>
                                    <div class="col-6">
                                        <input type="number" class="form-control form-control-sm"
                                               id="maxDrawdownDays" value="100" step="1" min="0"
                                               placeholder="最大天数">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="filter-group">
                                <label class="form-label">新高间隔天数</label>
                                <div class="row">
                                    <div class="col-6">
                                        <input type="number" class="form-control form-control-sm"
                                               id="minHighInterval" value="0" step="1" min="0"
                                               placeholder="最小天数">
                                    </div>
                                    <div class="col-6">
                                        <input type="number" class="form-control form-control-sm"
                                               id="maxHighInterval" value="100" step="1" min="0"
                                               placeholder="最大天数">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮和排序 -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <button class="btn btn-primary btn-sm me-2" onclick="updateDrawdownEvents()">
                                <i class="bi bi-funnel"></i>
                                应用筛选
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="resetDrawdownFilters()">
                                <i class="bi bi-arrow-clockwise"></i>
                                重置
                            </button>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center justify-content-end">
                                <label class="form-label me-2 mb-0">排序方式：</label>
                                <select class="form-select form-select-sm" id="sortDrawdownEvents" style="width: auto;" onchange="sortDrawdownEventsTable()">
                                    <option value="default">默认排序</option>
                                    <option value="drawdown_asc">按最大回撤升序</option>
                                    <option value="drawdown_desc">按最大回撤降序</option>
                                    <option value="duration_asc">按回撤持续天数升序</option>
                                    <option value="duration_desc">按回撤持续天数降序</option>
                                    <option value="recovery_asc">按恢复天数升序</option>
                                    <option value="recovery_desc">按恢复天数降序</option>
                                    <option value="interval_asc">按新高间隔升序</option>
                                    <option value="interval_desc">按新高间隔降序</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- 统计信息和分页控件 -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="alert alert-info mb-0" id="drawdownEventsInfo" style="display: none;">
                                <i class="bi bi-info-circle me-2"></i>
                                <span id="drawdownEventsCount">找到 0 个符合条件的回撤事件</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center justify-content-end">
                                <label class="form-label me-2 mb-0">每页显示：</label>
                                <select class="form-select form-select-sm me-3" id="pageSize" style="width: auto;" onchange="changePageSize()">
                                    <option value="10" selected>10条</option>
                                    <option value="20">20条</option>
                                    <option value="50">50条</option>
                                    <option value="100">100条</option>
                                </select>
                                <span class="text-muted" id="pageInfo">第1页，共1页</span>
                            </div>
                        </div>
                    </div>

                    <!-- 数据表格 -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="drawdownEventsTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>序号</th>
                                    <th>回撤开始时间</th>
                                    <th>回撤最低点时间</th>
                                    <th>下一个新高时间</th>
                                    <th>最大回撤 (%)</th>
                                    <th>回撤持续天数</th>
                                    <th>恢复天数</th>
                                    <th>新高间隔天数</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody id="drawdownEventsTableBody">
                                <tr>
                                    <td colspan="9" class="text-center text-muted">
                                        <i class="bi bi-info-circle me-2"></i>
                                        请先选择策略并加载数据
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页导航 -->
                    <nav aria-label="回撤事件分页" id="paginationNav" style="display: none;">
                        <ul class="pagination justify-content-center" id="pagination">
                            <!-- 分页按钮将通过JavaScript动态生成 -->
                        </ul>
                    </nav>

                    <!-- 说明信息 -->
                    <div class="mt-3">
                        <div class="alert alert-light">
                            <h6><i class="bi bi-question-circle me-2"></i>指标说明：</h6>
                            <ul class="mb-0">
                                <li><strong>回撤开始时间</strong>：达到新高后开始回撤的时间点</li>
                                <li><strong>回撤最低点时间</strong>：回撤期间净值达到最低点的时间</li>
                                <li><strong>下一个新高时间</strong>：回撤后达到下一个新高的时间点</li>
                                <li><strong>回撤持续天数</strong>：从回撤开始到最低点的天数</li>
                                <li><strong>恢复天数</strong>：从回撤最低点到下一个新高的天数</li>
                                <li><strong>新高间隔天数</strong>：两个新高之间的间隔天数</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let selectedStrategies = [];
let currentChart = null;
let currentMaxDrawdownAnnotation = null; // 存储当前的回撤标注数据
let tooltipVisible = false; // 跟踪提示框显示状态
let allStrategies = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadStrategies();

    // 为时间输入框添加事件监听器
    const startTimeInput = document.getElementById('startTime');
    const endTimeInput = document.getElementById('endTime');

    if (startTimeInput && endTimeInput) {
        // 添加change事件监听器，当用户修改时间后自动更新图表
        startTimeInput.addEventListener('change', function() {
            if (selectedStrategies.length > 0) {
                setTimeout(() => updateChart(), 100); // 延迟一点确保值已更新
            }
        });

        endTimeInput.addEventListener('change', function() {
            if (selectedStrategies.length > 0) {
                setTimeout(() => updateChart(), 100); // 延迟一点确保值已更新
            }
        });
    }

    // 添加全局鼠标移动监听器来检测鼠标离开图表区域
    document.addEventListener('mousemove', function(event) {
        if (tooltipVisible && currentChart) {
            const canvas = document.getElementById('fundCurveChart');
            if (canvas) {
                const rect = canvas.getBoundingClientRect();
                const mouseX = event.clientX;
                const mouseY = event.clientY;

                // 检查鼠标是否在canvas区域外
                const isOutsideCanvas = mouseX < rect.left || mouseX > rect.right ||
                                       mouseY < rect.top || mouseY > rect.bottom;

                if (isOutsideCanvas) {
                    hideDrawdownTooltip();
                }
            }
        }
    });
});

// 加载策略列表
async function loadStrategies() {
    try {
        const response = await fetch('/api/fund-curve/strategies');
        const data = await response.json();
        
        if (data.success) {
            allStrategies = data.strategies;
            displayStrategies(data.strategies);
        } else {
            showError('加载策略失败: ' + data.message);
        }
    } catch (error) {
        showError('加载策略失败: ' + error.message);
    }
}

// 显示策略列表
function displayStrategies(strategies) {
    const container = document.getElementById('strategiesContainer');
    
    if (strategies.length === 0) {
        container.innerHTML = `
            <div class="col-12 text-center">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    未找到任何策略文件，请确保data/子策略回测结果目录下存在资金曲线.csv文件
                </div>
            </div>
        `;
        return;
    }
    
    let html = '';
    strategies.forEach((strategy, index) => {
        if (strategy.error) {
            html += `
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="strategy-card p-3 border-danger">
                        <h6 class="text-danger">${strategy.name}</h6>
                        <p class="text-danger small mb-0">${strategy.error}</p>
                    </div>
                </div>
            `;
        } else {
            const returnClass = strategy.total_return > 0 ? 'positive' : strategy.total_return < 0 ? 'negative' : 'neutral';
            html += `
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="strategy-card p-3" onclick="toggleStrategy(${index}, event)">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="mb-0">${strategy.name}</h6>
                            <div class="d-flex align-items-center">
                                <button type="button" class="btn btn-outline-danger btn-sm me-2"
                                        onclick="deleteStrategy(${index}, event)"
                                        title="删除资金曲线">
                                    <i class="bi bi-trash"></i>
                                </button>
                                <input type="checkbox" class="form-check-input" id="strategy_${index}">
                            </div>
                        </div>
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="stat-value ${returnClass}">${strategy.total_return}%</div>
                                <div class="stat-label">总收益</div>
                            </div>
                            <div class="col-6">
                                <div class="stat-value">${strategy.data_points}</div>
                                <div class="stat-label">数据点</div>
                            </div>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="bi bi-calendar me-1"></i>
                                ${strategy.start_time} ~ ${strategy.end_time}
                            </small>
                        </div>
                    </div>
                </div>
            `;
        }
    });
    
    container.innerHTML = html;

    // 为所有复选框添加事件监听器
    strategies.forEach((strategy, index) => {
        if (!strategy.error) {
            const checkbox = document.getElementById(`strategy_${index}`);
            if (checkbox) {
                checkbox.addEventListener('change', function(event) {
                    event.stopPropagation();
                    updateStrategySelection(index, this.checked);
                });
            }
        }
    });
}

// 切换策略选择状态
function toggleStrategy(index, event) {
    // 如果点击的是复选框本身，不需要处理（复选框会自动切换状态）
    if (event && event.target.type === 'checkbox') {
        return;
    }

    const checkbox = document.getElementById(`strategy_${index}`);
    const card = checkbox.closest('.strategy-card');

    // 切换复选框状态
    checkbox.checked = !checkbox.checked;

    // 更新策略选择状态
    updateStrategySelection(index, checkbox.checked);
}

// 更新策略选择状态的通用函数
function updateStrategySelection(index, isSelected) {
    const checkbox = document.getElementById(`strategy_${index}`);
    const card = checkbox.closest('.strategy-card');

    if (isSelected) {
        card.classList.add('selected');
        // 检查是否已经在选中列表中
        if (!selectedStrategies.find(s => s.name === allStrategies[index].name)) {
            selectedStrategies.push(allStrategies[index]);
        }
    } else {
        card.classList.remove('selected');
        selectedStrategies = selectedStrategies.filter(s => s.name !== allStrategies[index].name);
    }

    updateCompareButton();

    // 如果只选择了一个策略，自动显示其图表
    if (selectedStrategies.length === 1) {
        showTimeRangeControls();
        // 使用setTimeout确保时间范围控制设置完成后再加载图表
        setTimeout(() => {
            loadSingleStrategyChart(selectedStrategies[0]);
        }, 100);
    } else if (selectedStrategies.length === 0) {
        hideChartSection();
    }
}

// 更新对比按钮状态
function updateCompareButton() {
    const compareBtn = document.getElementById('compareBtn');
    compareBtn.disabled = selectedStrategies.length < 2;
    
    if (selectedStrategies.length >= 2) {
        compareBtn.innerHTML = `<i class="bi bi-bar-chart"></i> 对比策略 (${selectedStrategies.length})`;
    } else {
        compareBtn.innerHTML = `<i class="bi bi-bar-chart"></i> 对比策略`;
    }
}

// 显示时间范围控制
function showTimeRangeControls() {
    document.getElementById('timeRangeControls').style.display = 'block';
    
    // 设置默认时间范围
    if (selectedStrategies.length > 0) {
        const strategy = selectedStrategies[0];
        document.getElementById('startTime').value = formatDateTimeLocal(strategy.start_time);
        document.getElementById('endTime').value = formatDateTimeLocal(strategy.end_time);
        
        // 生成年份选择器
        generateYearSelector(strategy);
    }
}

// 生成年份选择器
function generateYearSelector(strategy) {
    const startYear = new Date(strategy.start_time).getFullYear();
    const endYear = new Date(strategy.end_time).getFullYear();
    
    // 如果开始年份和结束年份相同，不显示年份选择器
    if (startYear === endYear) {
        document.getElementById('yearSelector').style.display = 'none';
        return;
    }
    
    const yearButtons = document.getElementById('yearButtons');
    let html = '';
    
    // 生成年份按钮
    for (let year = startYear; year <= endYear; year++) {
        html += `<button class="btn-custom" onclick="setYearRange(${year})">${year}年</button>`;
    }
    
    yearButtons.innerHTML = html;
    document.getElementById('yearSelector').style.display = 'block';
}

// 设置年份范围
function setYearRange(year) {
    // 移除所有年份按钮的active类
    document.querySelectorAll('#yearButtons .btn-custom').forEach(btn => btn.classList.remove('active'));
    // 添加active类到当前按钮
    event.target.classList.add('active');
    
    // 同时移除快速选择按钮的active类
    document.querySelectorAll('.btn-group-custom .btn-custom').forEach(btn => btn.classList.remove('active'));
    
    if (selectedStrategies.length === 0) return;
    
    const strategy = selectedStrategies[0];
    
    // 设置该年份的开始和结束时间
    const startDate = new Date(year, 0, 1); // 年初
    const endDate = new Date(year, 11, 31, 23, 59, 59); // 年末
    
    // 确保不超出策略的实际时间范围
    const strategyStart = new Date(strategy.start_time);
    const strategyEnd = new Date(strategy.end_time);
    
    const finalStartDate = startDate < strategyStart ? strategyStart : startDate;
    const finalEndDate = endDate > strategyEnd ? strategyEnd : endDate;
    
    document.getElementById('startTime').value = formatDateTimeLocal(finalStartDate.toISOString());
    document.getElementById('endTime').value = formatDateTimeLocal(finalEndDate.toISOString());
    
    // 自动更新图表
    updateChart();
}

// 隐藏图表区域
function hideChartSection() {
    document.getElementById('timeRangeControls').style.display = 'none';
    document.getElementById('statisticsSection').style.display = 'none';
    document.getElementById('fundCurveSection').style.display = 'none';
    document.getElementById('drawdownChartSection').style.display = 'none';
    document.getElementById('drawdownEventsSection').style.display = 'none';
    document.getElementById('yearSelector').style.display = 'none';
}

// 格式化日期时间为本地格式
function formatDateTimeLocal(dateTimeStr) {
    const date = new Date(dateTimeStr);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day}T${hours}:${minutes}`;
}

// 设置时间范围
function setTimeRange(range) {
    // 移除所有快速选择按钮的active类
    document.querySelectorAll('.btn-group-custom .btn-custom').forEach(btn => btn.classList.remove('active'));
    // 移除所有年份按钮的active类
    document.querySelectorAll('#yearButtons .btn-custom').forEach(btn => btn.classList.remove('active'));
    // 添加active类到当前按钮
    event.target.classList.add('active');
    
    if (selectedStrategies.length === 0) return;
    
    const strategy = selectedStrategies[0];
    const endDate = new Date(strategy.end_time);
    let startDate = new Date(strategy.start_time);
    
    switch(range) {
        case '7D':
            startDate = new Date(endDate);
            startDate.setDate(startDate.getDate() - 7);
            break;
        case '1M':
            startDate = new Date(endDate);
            startDate.setMonth(startDate.getMonth() - 1);
            break;
        case '3M':
            startDate = new Date(endDate);
            startDate.setMonth(startDate.getMonth() - 3);
            break;
        case '6M':
            startDate = new Date(endDate);
            startDate.setMonth(startDate.getMonth() - 6);
            break;
        case '1Y':
            startDate = new Date(endDate);
            startDate.setFullYear(startDate.getFullYear() - 1);
            break;
        case 'ALL':
        default:
            startDate = new Date(strategy.start_time);
            break;
    }
    
    document.getElementById('startTime').value = formatDateTimeLocal(startDate.toISOString());
    document.getElementById('endTime').value = formatDateTimeLocal(endDate.toISOString());

    // 自动更新图表
    updateChart();
}

// 显示错误信息
function showError(message) {
    const container = document.getElementById('strategiesContainer');
    container.innerHTML = `
        <div class="col-12">
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle me-2"></i>
                ${message}
            </div>
        </div>
    `;
}

// 刷新策略列表
function refreshStrategies() {
    selectedStrategies = [];
    hideChartSection();
    document.getElementById('strategiesContainer').innerHTML = `
        <div class="col-12 text-center">
            <div class="loading-spinner">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2">正在刷新策略列表...</p>
            </div>
        </div>
    `;
    loadStrategies();
}

// 删除策略
async function deleteStrategy(index, event) {
    // 阻止事件冒泡，避免触发卡片点击事件
    event.stopPropagation();

    const strategy = allStrategies[index];
    if (!strategy) {
        alert('策略不存在');
        return;
    }

    // 确认删除
    const confirmMessage = `确定要删除策略 "${strategy.name}" 及其整个目录吗？\n\n此操作将删除该策略的所有文件，不可撤销！`;
    if (!confirm(confirmMessage)) {
        return;
    }

    try {
        const response = await fetch('/api/fund-curve/delete-strategy', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                file_path: strategy.file_path
            })
        });

        const data = await response.json();

        if (data.success) {
            // 显示成功消息
            showSuccessMessage(data.message || '策略目录删除成功');

            // 如果删除的策略正在被选中，清除选择状态
            selectedStrategies = selectedStrategies.filter(s => s.file_path !== strategy.file_path);

            // 如果没有选中的策略了，隐藏图表
            if (selectedStrategies.length === 0) {
                hideChartSection();
            }

            // 刷新策略列表
            refreshStrategies();
        } else {
            showError('删除失败: ' + data.message);
        }
    } catch (error) {
        showError('删除失败: ' + error.message);
    }
}

// 显示成功消息
function showSuccessMessage(message) {
    const container = document.getElementById('strategiesContainer');
    const successAlert = document.createElement('div');
    successAlert.className = 'alert alert-success alert-dismissible fade show';
    successAlert.innerHTML = `
        <i class="bi bi-check-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // 在容器顶部插入成功消息
    container.parentNode.insertBefore(successAlert, container);

    // 3秒后自动消失
    setTimeout(() => {
        if (successAlert.parentNode) {
            successAlert.remove();
        }
    }, 3000);
}

// 加载单个策略图表
async function loadSingleStrategyChart(strategy) {
    try {
        const startTime = document.getElementById('startTime').value;
        const endTime = document.getElementById('endTime').value;

        const params = new URLSearchParams({
            file_path: strategy.file_path
        });

        if (startTime) params.append('start_time', startTime);
        if (endTime) params.append('end_time', endTime);

        const response = await fetch(`/api/fund-curve/strategy-data?${params}`);
        const data = await response.json();

        if (data.success) {
            // 存储当前策略数据用于回撤事件分析
            currentStrategyData = strategy;

            displayChart(data.chart_data, data.max_drawdown_annotation);
            displayStatistics([{name: strategy.name, statistics: data.statistics}]);

            // 自动加载回撤事件分析
            setTimeout(() => {
                updateDrawdownEvents();
            }, 500);
        } else {
            showError('加载策略数据失败: ' + data.message);
        }
    } catch (error) {
        showError('加载策略数据失败: ' + error.message);
    }
}

// 对比多个策略
async function compareStrategies() {
    if (selectedStrategies.length < 2) {
        alert('请至少选择2个策略进行对比');
        return;
    }

    try {
        const startTime = document.getElementById('startTime').value;
        const endTime = document.getElementById('endTime').value;

        const requestData = {
            file_paths: selectedStrategies.map(s => s.file_path)
        };

        if (startTime) requestData.start_time = startTime;
        if (endTime) requestData.end_time = endTime;

        const response = await fetch('/api/fund-curve/compare', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });

        const data = await response.json();

        if (data.success) {
            // 清除当前策略数据，多策略对比不支持回撤事件分析
            currentStrategyData = null;

            displayChart(data.chart_data, null); // 多策略对比不显示回撤标注
            displayStatistics(data.strategies);

            // 隐藏回撤事件分析区域
            document.getElementById('drawdownEventsSection').style.display = 'none';
        } else {
            showError('策略对比失败: ' + data.message);
        }
    } catch (error) {
        showError('策略对比失败: ' + error.message);
    }
}

// 更新图表
function updateChart() {
    if (selectedStrategies.length === 1) {
        loadSingleStrategyChart(selectedStrategies[0]);
    } else if (selectedStrategies.length > 1) {
        compareStrategies();
    }
}

// 显示图表
function displayChart(chartData, maxDrawdownAnnotation = null) {
    document.getElementById('fundCurveSection').style.display = 'block';
    document.getElementById('drawdownChartSection').style.display = 'block';

    const ctx = document.getElementById('fundCurveChart').getContext('2d');

    // 销毁现有图表
    if (currentChart) {
        currentChart.destroy();
    }

    // 存储回撤标注数据到全局变量
    currentMaxDrawdownAnnotation = maxDrawdownAnnotation;

    // 构建插件配置 - 参考专业金融图表样式
    const plugins = {
        title: {
            display: false  // 使用卡片标题，不需要图表内标题
        },
        legend: {
            display: true,
            position: 'top',
            align: 'start',
            labels: {
                usePointStyle: true,
                pointStyle: 'line',
                font: {
                    size: 12,
                    weight: '500'
                },
                color: '#495057',
                padding: 20
            }
        },
        tooltip: {
            mode: 'index',
            intersect: false,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            titleColor: '#fff',
            bodyColor: '#fff',
            borderColor: '#007bff',
            borderWidth: 1,
            cornerRadius: 8,
            displayColors: true,
            callbacks: {
                title: function(context) {
                    // 直接使用原始的x值，它应该是时间字符串
                    const timeStr = context[0].label || context[0].parsed.x;
                    if (typeof timeStr === 'string') {
                        return timeStr;
                    } else {
                        // 如果是时间戳，转换为日期
                        const date = new Date(timeStr);
                        return date.toLocaleString('zh-CN', {
                            year: 'numeric',
                            month: '2-digit',
                            day: '2-digit',
                            hour: '2-digit',
                            minute: '2-digit'
                        });
                    }
                },
                label: function(context) {
                    const value = context.parsed.y.toFixed(4);
                    const change = context.datasetIndex === 0 ?
                        ((context.parsed.y - 1) * 100).toFixed(2) + '%' :
                        context.parsed.y.toFixed(4);
                    return `${context.dataset.label}: ${value} (${change})`;
                }
            }
        }
    };

    // 如果有最大回撤标注数据，添加annotation配置 - 参考专业金融图表样式
    if (maxDrawdownAnnotation && maxDrawdownAnnotation.start_time && maxDrawdownAnnotation.end_time) {
        plugins.annotation = {
            annotations: {
                maxDrawdownBox: {
                    type: 'box',
                    xMin: maxDrawdownAnnotation.start_time,
                    xMax: maxDrawdownAnnotation.end_time,
                    backgroundColor: 'rgba(220, 53, 69, 0.08)',  // 更淡的红色背景
                    borderColor: 'rgba(220, 53, 69, 0.6)',       // 专业的红色边框
                    borderWidth: 1.5,
                    borderDash: [5, 5],  // 虚线边框
                    label: {
                        display: false
                    }
                }
            }
        };
    }

    // 优化图表数据的颜色配置 - 参考专业金融软件配色
    optimizeChartColors(chartData);

    // 创建新图表 - 参考专业金融图表配置
    currentChart = new Chart(ctx, {
        type: 'line',
        data: chartData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: plugins,
            scales: {
                x: {
                    type: 'time',
                    time: {
                        parser: 'YYYY-MM-DD HH:mm:ss',
                        tooltipFormat: 'YYYY-MM-DD HH:mm:ss',
                        displayFormats: {
                            hour: 'MM-DD HH:mm',
                            day: 'MM-DD',
                            week: 'MM-DD',
                            month: 'YYYY-MM'
                        }
                    },
                    display: true,
                    title: {
                        display: true,
                        text: '日期',
                        font: {
                            size: 12,
                            weight: '500'
                        },
                        color: '#6c757d'
                    },
                    grid: {
                        display: true,
                        color: 'rgba(0, 0, 0, 0.05)',
                        lineWidth: 1
                    },
                    ticks: {
                        maxTicksLimit: 8,
                        color: '#6c757d',
                        font: {
                            size: 11
                        }
                    }
                },
                y: {
                    display: true,
                    title: {
                        display: true,
                        text: '净值',
                        font: {
                            size: 12,
                            weight: '500'
                        },
                        color: '#6c757d'
                    },
                    grid: {
                        display: true,
                        color: 'rgba(0, 0, 0, 0.05)',
                        lineWidth: 1
                    },
                    ticks: {
                        color: '#6c757d',
                        font: {
                            size: 11
                        },
                        callback: function(value) {
                            return value.toFixed(3);
                        }
                    },
                    beginAtZero: false
                }
            },
            elements: {
                line: {
                    tension: 0.1,  // 轻微的曲线平滑
                    borderWidth: 2.5
                },
                point: {
                    radius: 0,  // 默认不显示点
                    hoverRadius: 4,  // 悬停时显示点
                    hitRadius: 8
                }
            },
            interaction: {
                mode: 'index',
                intersect: false
            },
            // 添加鼠标事件处理
            onHover: function(event, activeElements, chart) {
                if (currentMaxDrawdownAnnotation) {
                    handleChartHover(event, chart, currentMaxDrawdownAnnotation);
                }
            },
            // 添加鼠标离开事件处理
            onLeave: function(event, chart) {
                hideDrawdownTooltip();
            }
        }
    });

    // 添加原生DOM事件监听器作为备用方案
    const canvas = document.getElementById('fundCurveChart');
    if (canvas) {
        // 鼠标离开canvas时隐藏提示框
        canvas.addEventListener('mouseleave', function() {
            hideDrawdownTooltip();
        });

        // 鼠标进入canvas时重置状态
        canvas.addEventListener('mouseenter', function() {
            // 不需要特殊处理，onHover会处理
        });
    }

    // 创建回撤图
    createDrawdownChart(chartData, maxDrawdownAnnotation);
}

// 创建回撤图 - 参考参考代码的回撤图实现
function createDrawdownChart(chartData, maxDrawdownAnnotation = null) {
    const ctx = document.getElementById('drawdownChart').getContext('2d');

    // 销毁现有回撤图表
    if (window.currentDrawdownChart) {
        window.currentDrawdownChart.destroy();
    }

    // 计算回撤数据
    const drawdownData = calculateDrawdownData(chartData);

    // 构建回撤图表数据 - 使用时间轴格式
    const drawdownChartData = {
        labels: chartData.labels,
        datasets: [{
            label: '回撤百分比',
            data: chartData.labels.map((label, index) => ({
                x: label,
                y: drawdownData[index]
            })),
            borderColor: '#dc3545',
            backgroundColor: 'rgba(220, 53, 69, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.1,
            pointRadius: 0,
            pointHoverRadius: 4
        }]
    };

    // 回撤图插件配置
    const drawdownPlugins = {
        title: {
            display: false
        },
        legend: {
            display: true,
            position: 'top',
            align: 'start',
            labels: {
                usePointStyle: true,
                pointStyle: 'line',
                font: {
                    size: 12,
                    weight: '500'
                },
                color: '#495057',
                padding: 20
            }
        },
        tooltip: {
            mode: 'index',
            intersect: false,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            titleColor: '#fff',
            bodyColor: '#fff',
            borderColor: '#dc3545',
            borderWidth: 1,
            cornerRadius: 8,
            callbacks: {
                title: function(context) {
                    // 直接使用原始的x值，它应该是时间字符串
                    const timeStr = context[0].label || context[0].parsed.x;
                    if (typeof timeStr === 'string') {
                        return timeStr;
                    } else {
                        // 如果是时间戳，转换为日期
                        const date = new Date(timeStr);
                        return date.toLocaleString('zh-CN', {
                            year: 'numeric',
                            month: '2-digit',
                            day: '2-digit',
                            hour: '2-digit',
                            minute: '2-digit'
                        });
                    }
                },
                label: function(context) {
                    return `回撤: ${context.parsed.y.toFixed(2)}%`;
                }
            }
        }
    };

    // 创建回撤图表
    window.currentDrawdownChart = new Chart(ctx, {
        type: 'line',
        data: drawdownChartData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: drawdownPlugins,
            scales: {
                x: {
                    type: 'time',
                    time: {
                        parser: 'YYYY-MM-DD HH:mm:ss',
                        tooltipFormat: 'YYYY-MM-DD HH:mm:ss',
                        displayFormats: {
                            hour: 'MM-DD HH:mm',
                            day: 'MM-DD',
                            week: 'MM-DD',
                            month: 'YYYY-MM'
                        }
                    },
                    display: true,
                    title: {
                        display: true,
                        text: '日期',
                        font: {
                            size: 12,
                            weight: '500'
                        },
                        color: '#6c757d'
                    },
                    grid: {
                        display: true,
                        color: 'rgba(0, 0, 0, 0.05)',
                        lineWidth: 1
                    },
                    ticks: {
                        maxTicksLimit: 8,
                        color: '#6c757d',
                        font: {
                            size: 11
                        }
                    }
                },
                y: {
                    display: true,
                    title: {
                        display: true,
                        text: '回撤百分比 (%)',
                        font: {
                            size: 12,
                            weight: '500'
                        },
                        color: '#6c757d'
                    },
                    grid: {
                        display: true,
                        color: 'rgba(0, 0, 0, 0.05)',
                        lineWidth: 1
                    },
                    ticks: {
                        color: '#6c757d',
                        font: {
                            size: 11
                        },
                        callback: function(value) {
                            return value.toFixed(1) + '%';
                        }
                    },
                    max: 0,  // 回撤图的最大值为0
                    reverse: false  // 不反转Y轴，让负值在下方
                }
            },
            elements: {
                line: {
                    tension: 0.1,
                    borderWidth: 2
                },
                point: {
                    radius: 0,
                    hoverRadius: 4,
                    hitRadius: 8
                }
            },
            interaction: {
                mode: 'index',
                intersect: false
            }
        }
    });
}

// 计算回撤数据
function calculateDrawdownData(chartData) {
    const drawdownData = [];

    chartData.datasets.forEach(dataset => {
        if (dataset.label.includes('净值') || dataset.label.includes('资金曲线')) {
            const values = dataset.data;
            const drawdowns = [];

            // 处理新的数据格式 {x: time, y: value}
            const numericValues = values.map(item => {
                if (typeof item === 'object' && item.y !== undefined) {
                    return item.y;
                } else {
                    return item;
                }
            });

            let peak = numericValues[0];

            for (let i = 0; i < numericValues.length; i++) {
                // 更新峰值
                if (numericValues[i] > peak) {
                    peak = numericValues[i];
                }

                // 计算回撤百分比
                const drawdown = ((numericValues[i] - peak) / peak) * 100;
                drawdowns.push(drawdown);
            }

            // 只取第一个数据集的回撤数据
            if (drawdownData.length === 0) {
                drawdownData.push(...drawdowns);
            }
        }
    });

    return drawdownData;
}

// 全局变量存储当前的回撤事件数据
let currentDrawdownEvents = [];
let currentStrategyData = null;

// 分页相关变量
let currentPage = 1;
let pageSize = 10;
let totalPages = 1;

// 更新回撤事件分析
async function updateDrawdownEvents() {
    if (!currentStrategyData) {
        showError('请先选择策略并加载数据');
        return;
    }

    // 获取筛选条件
    const minDrawdown = parseFloat(document.getElementById('minDrawdown').value) || 0;
    const maxDrawdown = parseFloat(document.getElementById('maxDrawdown').value) || 100;
    const minDrawdownDays = parseFloat(document.getElementById('minDrawdownDays').value) || 0;
    const maxDrawdownDays = parseFloat(document.getElementById('maxDrawdownDays').value) || 1000;
    const minHighInterval = parseFloat(document.getElementById('minHighInterval').value) || 0;
    const maxHighInterval = parseFloat(document.getElementById('maxHighInterval').value) || 1000;

    try {
        const response = await fetch('/api/fund-curve/drawdown-events', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                file_path: currentStrategyData.file_path,
                start_time: document.getElementById('startTime').value,
                end_time: document.getElementById('endTime').value,
                min_drawdown: minDrawdown,
                max_drawdown: maxDrawdown,
                min_drawdown_days: minDrawdownDays,
                max_drawdown_days: maxDrawdownDays,
                min_high_interval: minHighInterval,
                max_high_interval: maxHighInterval
            })
        });

        const data = await response.json();

        if (data.success) {
            currentDrawdownEvents = data.events;
            displayDrawdownEvents(currentDrawdownEvents);
            document.getElementById('drawdownEventsSection').style.display = 'block';
        } else {
            showError('获取回撤事件失败: ' + data.message);
        }
    } catch (error) {
        showError('获取回撤事件失败: ' + error.message);
    }
}

// 显示回撤事件表格（支持分页）
function displayDrawdownEvents(events) {
    const tbody = document.getElementById('drawdownEventsTableBody');
    const infoDiv = document.getElementById('drawdownEventsInfo');
    const countSpan = document.getElementById('drawdownEventsCount');
    const pageInfo = document.getElementById('pageInfo');
    const paginationNav = document.getElementById('paginationNav');

    // 计算分页信息
    pageSize = parseInt(document.getElementById('pageSize').value);
    totalPages = Math.ceil(events.length / pageSize);

    // 确保当前页在有效范围内
    if (currentPage > totalPages) {
        currentPage = Math.max(1, totalPages);
    }

    // 更新统计信息
    countSpan.textContent = `找到 ${events.length} 个符合条件的回撤事件`;
    infoDiv.style.display = 'block';

    // 更新页面信息
    if (events.length > 0) {
        pageInfo.textContent = `第${currentPage}页，共${totalPages}页，总计${events.length}条记录`;
    } else {
        pageInfo.textContent = '第1页，共1页';
    }

    if (events.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="9" class="text-center text-muted">
                    <i class="bi bi-info-circle me-2"></i>
                    没有找到符合条件的回撤事件
                </td>
            </tr>
        `;
        paginationNav.style.display = 'none';
        return;
    }

    // 计算当前页的数据范围
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, events.length);
    const pageEvents = events.slice(startIndex, endIndex);

    let html = '';
    pageEvents.forEach((event, index) => {
        // 格式化时间
        const startTime = formatDateTime(event.dd_start_time);
        const lowestTime = formatDateTime(event.dd_lowest_time);
        const endTime = event.dd_end_time ? formatDateTime(event.dd_end_time) : 'N/A';

        // 格式化数值
        const drawdownPct = Math.abs(event.max_drawdown).toFixed(2);
        const drawdownDays = event.drawdown_duration.toFixed(1);
        const recoveryDays = event.recovery_duration ? event.recovery_duration.toFixed(1) : 'N/A';
        const intervalDays = event.high_interval ? event.high_interval.toFixed(1) : 'N/A';

        // 确定回撤程度的样式类
        const drawdownClass = getDrawdownClass(Math.abs(event.max_drawdown));

        // 确定状态
        const status = event.is_last_point ?
            '<span class="status-badge ongoing">进行中</span>' :
            '<span class="status-badge completed">已完成</span>';

        // 全局序号（不是页内序号）
        const globalIndex = startIndex + index + 1;

        html += `
            <tr>
                <td>${globalIndex}</td>
                <td>${startTime}</td>
                <td>${lowestTime}</td>
                <td>${endTime}${event.is_last_point ? ' <small class="text-muted">(当前)</small>' : ''}</td>
                <td><span class="drawdown-value ${drawdownClass}">${drawdownPct}%</span></td>
                <td>${drawdownDays}天</td>
                <td>${recoveryDays}${recoveryDays !== 'N/A' ? '天' : ''}</td>
                <td>${intervalDays}${intervalDays !== 'N/A' ? '天' : ''}</td>
                <td>${status}</td>
            </tr>
        `;
    });

    tbody.innerHTML = html;

    // 显示分页导航
    if (totalPages > 1) {
        renderPagination();
        paginationNav.style.display = 'block';
    } else {
        paginationNav.style.display = 'none';
    }
}

// 获取回撤程度的样式类
function getDrawdownClass(drawdownPct) {
    if (drawdownPct >= 10) return 'high';
    if (drawdownPct >= 5) return 'medium';
    return 'low';
}

// 格式化日期时间
function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return 'N/A';
    const date = new Date(dateTimeStr);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 排序回撤事件表格
function sortDrawdownEventsTable() {
    const sortValue = document.getElementById('sortDrawdownEvents').value;

    // 排序时重置到第一页
    currentPage = 1;

    if (sortValue === 'default' || currentDrawdownEvents.length === 0) {
        displayDrawdownEvents(currentDrawdownEvents);
        return;
    }

    let sortedEvents = [...currentDrawdownEvents];

    switch (sortValue) {
        case 'drawdown_asc':
            sortedEvents.sort((a, b) => Math.abs(a.max_drawdown) - Math.abs(b.max_drawdown));
            break;
        case 'drawdown_desc':
            sortedEvents.sort((a, b) => Math.abs(b.max_drawdown) - Math.abs(a.max_drawdown));
            break;
        case 'duration_asc':
            sortedEvents.sort((a, b) => a.drawdown_duration - b.drawdown_duration);
            break;
        case 'duration_desc':
            sortedEvents.sort((a, b) => b.drawdown_duration - a.drawdown_duration);
            break;
        case 'recovery_asc':
            sortedEvents.sort((a, b) => (a.recovery_duration || 0) - (b.recovery_duration || 0));
            break;
        case 'recovery_desc':
            sortedEvents.sort((a, b) => (b.recovery_duration || 0) - (a.recovery_duration || 0));
            break;
        case 'interval_asc':
            sortedEvents.sort((a, b) => (a.high_interval || 0) - (b.high_interval || 0));
            break;
        case 'interval_desc':
            sortedEvents.sort((a, b) => (b.high_interval || 0) - (a.high_interval || 0));
            break;
    }

    displayDrawdownEvents(sortedEvents);
}

// 重置筛选条件
function resetDrawdownFilters() {
    document.getElementById('minDrawdown').value = '0';
    document.getElementById('maxDrawdown').value = '100';
    document.getElementById('minDrawdownDays').value = '0';
    document.getElementById('maxDrawdownDays').value = '100';
    document.getElementById('minHighInterval').value = '0';
    document.getElementById('maxHighInterval').value = '100';
    document.getElementById('sortDrawdownEvents').value = 'default';

    // 重置分页
    currentPage = 1;
    document.getElementById('pageSize').value = '10';

    // 如果有当前策略数据，重新加载
    if (currentStrategyData) {
        updateDrawdownEvents();
    }
}

// 渲染分页导航
function renderPagination() {
    const pagination = document.getElementById('pagination');
    let html = '';

    // 上一页按钮
    const prevDisabled = currentPage === 1 ? 'disabled' : '';
    html += `
        <li class="page-item ${prevDisabled}">
            <a class="page-link" href="#" onclick="changePage(${currentPage - 1}); return false;" aria-label="上一页">
                <span aria-hidden="true">&laquo;</span>
            </a>
        </li>
    `;

    // 页码按钮
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    // 调整起始页，确保显示足够的页码
    if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    // 如果起始页大于1，显示第一页和省略号
    if (startPage > 1) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(1); return false;">1</a></li>`;
        if (startPage > 2) {
            html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
    }

    // 显示页码
    for (let i = startPage; i <= endPage; i++) {
        const active = i === currentPage ? 'active' : '';
        html += `<li class="page-item ${active}"><a class="page-link" href="#" onclick="changePage(${i}); return false;">${i}</a></li>`;
    }

    // 如果结束页小于总页数，显示省略号和最后一页
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
        html += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${totalPages}); return false;">${totalPages}</a></li>`;
    }

    // 下一页按钮
    const nextDisabled = currentPage === totalPages ? 'disabled' : '';
    html += `
        <li class="page-item ${nextDisabled}">
            <a class="page-link" href="#" onclick="changePage(${currentPage + 1}); return false;" aria-label="下一页">
                <span aria-hidden="true">&raquo;</span>
            </a>
        </li>
    `;

    pagination.innerHTML = html;
}

// 切换页面
function changePage(page) {
    if (page < 1 || page > totalPages || page === currentPage) {
        return;
    }

    currentPage = page;

    // 重新显示当前排序后的数据
    const sortValue = document.getElementById('sortDrawdownEvents').value;
    if (sortValue === 'default') {
        displayDrawdownEvents(currentDrawdownEvents);
    } else {
        sortDrawdownEventsTable();
    }
}

// 改变每页显示条数
function changePageSize() {
    currentPage = 1; // 重置到第一页

    // 重新显示当前排序后的数据
    const sortValue = document.getElementById('sortDrawdownEvents').value;
    if (sortValue === 'default') {
        displayDrawdownEvents(currentDrawdownEvents);
    } else {
        sortDrawdownEventsTable();
    }
}

// 优化图表颜色配置 - 参考专业金融软件配色方案
function optimizeChartColors(chartData) {
    // 专业金融图表配色方案 - 参考Bloomberg、TradingView等专业平台
    const professionalColors = [
        {
            border: '#4285F4',      // Google Blue - 主要策略
            background: 'rgba(66, 133, 244, 0.08)',
            gradient: 'linear-gradient(135deg, #4285F4 0%, #1976D2 100%)'
        },
        {
            border: '#34A853',      // Google Green - 盈利策略
            background: 'rgba(52, 168, 83, 0.08)',
            gradient: 'linear-gradient(135deg, #34A853 0%, #2E7D32 100%)'
        },
        {
            border: '#EA4335',      // Google Red - 风险策略
            background: 'rgba(234, 67, 53, 0.08)',
            gradient: 'linear-gradient(135deg, #EA4335 0%, #C62828 100%)'
        },
        {
            border: '#FBBC04',      // Google Yellow - 中性策略
            background: 'rgba(251, 188, 4, 0.08)',
            gradient: 'linear-gradient(135deg, #FBBC04 0%, #F57C00 100%)'
        },
        {
            border: '#9C27B0',      // Purple - 创新策略
            background: 'rgba(156, 39, 176, 0.08)',
            gradient: 'linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%)'
        },
        {
            border: '#FF9800',      // Orange - 成长策略
            background: 'rgba(255, 152, 0, 0.08)',
            gradient: 'linear-gradient(135deg, #FF9800 0%, #F57C00 100%)'
        },
        {
            border: '#607D8B',      // Blue Grey - 稳健策略
            background: 'rgba(96, 125, 139, 0.08)',
            gradient: 'linear-gradient(135deg, #607D8B 0%, #455A64 100%)'
        },
        {
            border: '#795548',      // Brown - 价值策略
            background: 'rgba(121, 85, 72, 0.08)',
            gradient: 'linear-gradient(135deg, #795548 0%, #5D4037 100%)'
        }
    ];

    // 为每个数据集应用专业配色
    chartData.datasets.forEach((dataset, index) => {
        const colorScheme = professionalColors[index % professionalColors.length];

        // 应用颜色配置
        dataset.borderColor = colorScheme.border;
        dataset.backgroundColor = colorScheme.background;

        // 优化线条样式 - 参考专业图表
        dataset.borderWidth = 2.5;
        dataset.pointRadius = 0;
        dataset.pointHoverRadius = 5;
        dataset.pointHoverBorderWidth = 2;
        dataset.pointHoverBackgroundColor = '#fff';
        dataset.pointHoverBorderColor = colorScheme.border;
        dataset.tension = 0.1;
        dataset.fill = false;

        // 为单策略添加填充效果
        if (chartData.datasets.length === 1) {
            dataset.fill = 'origin';
            dataset.backgroundColor = colorScheme.background;
        }
    });
}

// 显示统计指标
function displayStatistics(strategies) {
    document.getElementById('statisticsSection').style.display = 'block';

    const grid = document.getElementById('statisticsGrid');
    let html = '';

    strategies.forEach(strategy => {
        const stats = strategy.statistics;

        // 格式化数值显示
        const formatValue = (value, type = 'number', fieldName = '') => {
            if (value === null || value === undefined || value === '') return '--';

            switch (type) {
                case 'percentage':
                    // 对"距离最大回撤空间"指标显示绝对值
                    if (fieldName === '距离最大回撤空间') {
                        return `${Math.abs(parseFloat(value)).toFixed(2)}%`;
                    }
                    return `${parseFloat(value).toFixed(2)}%`;
                case 'ratio':
                    return parseFloat(value).toFixed(3);
                case 'days':
                    return `${parseFloat(value).toFixed(1)}天`;
                case 'date':
                    return value.toString().length > 10 ? value.substring(0, 19).replace('T', ' ') : value;
                default:
                    return value;
            }
        };

        // 获取数值的颜色类
        const getValueClass = (value, type = 'neutral') => {
            if (value === null || value === undefined || value === '') return 'neutral';

            const numValue = parseFloat(value);
            switch (type) {
                case 'return':
                    return numValue > 0 ? 'positive' : numValue < 0 ? 'negative' : 'neutral';
                case 'drawdown':
                    return 'negative';
                case 'ratio':
                    return numValue > 1 ? 'positive' : numValue > 0.5 ? 'neutral' : 'negative';
                default:
                    return 'neutral';
            }
        };

        html += `
            <div class="stat-card">
                <h6>${strategy.name}</h6>

                <!-- 核心收益指标 -->
                <div class="stat-group">
                    <div class="row">
                        <div class="col-6 mb-3">
                            <div class="stat-value large highlight ${getValueClass(stats['总收益'], 'return')}">
                                ${formatValue(stats['总收益'], 'percentage')}
                            </div>
                            <div class="stat-label">总收益率</div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="stat-value large highlight ${getValueClass(stats['年化收益'], 'return')}">
                                ${formatValue(stats['年化收益'], 'percentage')}
                            </div>
                            <div class="stat-label">年化收益率</div>
                        </div>
                    </div>
                </div>

                <!-- 风险指标 -->
                <div class="stat-group">
                    <div class="row">
                        <div class="col-6 mb-3">
                            <div class="stat-value medium ${getValueClass(stats['最大回撤'], 'drawdown')}">
                                ${formatValue(stats['最大回撤'], 'percentage')}
                            </div>
                            <div class="stat-label">最大回撤</div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="stat-value medium highlight ${getValueClass(stats['夏普比率'], 'ratio')}">
                                ${formatValue(stats['夏普比率'], 'ratio')}
                            </div>
                            <div class="stat-label">夏普比率</div>
                        </div>
                    </div>
                </div>

                <!-- 当前状态 -->
                <div class="stat-group">
                    <div class="row">
                        <div class="col-6 mb-3">
                            <div class="stat-value medium ${getValueClass(stats['当前回撤'], 'drawdown')}">
                                ${formatValue(stats['当前回撤'], 'percentage')}
                            </div>
                            <div class="stat-label">当前回撤</div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="stat-value medium ${getValueClass(stats['距离最大回撤空间'], 'return')}">
                                ${formatValue(stats['距离最大回撤空间'], 'percentage', '距离最大回撤空间')}
                            </div>
                            <div class="stat-label">距离最大回撤空间</div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-6 mb-3">
                            <div class="stat-value medium neutral">
                                ${formatValue(stats['收益率标准差'], 'ratio')}
                            </div>
                            <div class="stat-label">收益率标准差</div>
                        </div>
                        <div class="col-6 mb-3">
                            <!-- 预留位置 -->
                        </div>
                    </div>
                </div>

                <!-- 时间指标 -->
                <div class="stat-group">
                    <div class="row">
                        <div class="col-6 mb-3">
                            <div class="stat-value small neutral">
                                ${formatValue(stats['最大回撤持续天数'], 'days')}
                            </div>
                            <div class="stat-label">最大回撤持续天数</div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="stat-value small neutral">
                                ${formatValue(stats['距离前高天数'], 'days')}
                            </div>
                            <div class="stat-label">距离前高天数</div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-6 mb-3">
                            <div class="stat-value small neutral">
                                ${formatValue(stats['最长不创新高天数'], 'days')}
                            </div>
                            <div class="stat-label">最长不创新高天数</div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="stat-value small neutral">
                                ${formatValue(stats['交易天数'], 'days')}
                            </div>
                            <div class="stat-label">交易天数</div>
                        </div>
                    </div>
                </div>

                <!-- 关键时间点 -->
                <div class="stat-group">
                    <div class="row">
                        <div class="col-12 mb-3">
                            <div class="stat-value small neutral highlight">
                                ${formatValue(stats['最新一次新高时间'], 'date')}
                            </div>
                            <div class="stat-label">最新一次新高时间</div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-6 mb-2">
                            <div class="stat-value small neutral">
                                ${formatValue(stats['最大回撤开始时间'], 'date')}
                            </div>
                            <div class="stat-label">最大回撤开始时间</div>
                        </div>
                        <div class="col-6 mb-2">
                            <div class="stat-value small neutral">
                                ${formatValue(stats['最大回撤结束时间'], 'date')}
                            </div>
                            <div class="stat-label">最大回撤结束时间</div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-6 mb-2">
                            <div class="stat-value small neutral">
                                ${formatValue(stats['最长不创新高开始时间'], 'date')}
                            </div>
                            <div class="stat-label">最长不创新高开始时间</div>
                        </div>
                        <div class="col-6 mb-2">
                            <div class="stat-value small neutral">
                                ${formatValue(stats['最长不创新高结束时间'], 'date')}
                            </div>
                            <div class="stat-label">最长不创新高结束时间</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    grid.innerHTML = html;
}

// 最大回撤悬停提示框功能
function showDrawdownTooltip(event, drawdownData) {
    const tooltip = document.getElementById('drawdownTooltip');

    if (!tooltip || !drawdownData) {
        return;
    }

    // 构建提示框内容
    const recoveryStatus = drawdownData.is_recovered ?
        '<div class="recovery-status recovered">✓ 已恢复到回撤前水平</div>' :
        '<div class="recovery-status not-recovered">⚠ 尚未完全恢复</div>';

    const tooltipContent = `
        <div class="tooltip-title">最大回撤详情</div>

        <div class="tooltip-section">
            <span class="tooltip-label">回撤幅度：</span>
            <span class="tooltip-value">${drawdownData.drawdown_value}%</span>
        </div>

        <div class="tooltip-section">
            <span class="tooltip-label">持续天数：</span>
            <span class="tooltip-value">${drawdownData.duration_days}天</span>
        </div>

        <div class="tooltip-section">
            <span class="tooltip-label">开始时间：</span>
            <span class="tooltip-value">${drawdownData.start_time}</span>
        </div>

        <div class="tooltip-section">
            <span class="tooltip-label">结束时间：</span>
            <span class="tooltip-value">${drawdownData.end_time}</span>
        </div>

        ${drawdownData.start_equity ? `
        <div class="tooltip-section">
            <span class="tooltip-label">回撤前净值：</span>
            <span class="tooltip-value">${drawdownData.start_equity}</span>
        </div>
        ` : ''}

        ${drawdownData.lowest_equity ? `
        <div class="tooltip-section">
            <span class="tooltip-label">最低净值：</span>
            <span class="tooltip-value">${drawdownData.lowest_equity}</span>
        </div>
        ` : ''}

        <div class="tooltip-section">
            <span class="tooltip-label">当前净值：</span>
            <span class="tooltip-value">${drawdownData.current_equity}</span>
        </div>

        ${recoveryStatus}
    `;

    tooltip.innerHTML = tooltipContent;

    // 获取鼠标位置和图表容器位置
    const chartContainer = document.querySelector('.chart-container');
    const containerRect = chartContainer.getBoundingClientRect();

    // 计算提示框位置（处理不同的事件对象结构）
    const clientX = event.native ? event.native.clientX : event.clientX;
    const clientY = event.native ? event.native.clientY : event.clientY;

    let x = clientX - containerRect.left + 15;
    let y = clientY - containerRect.top - 10;

    // 确保提示框不超出容器边界
    const tooltipRect = tooltip.getBoundingClientRect();
    if (x + 280 > containerRect.width) {
        x = clientX - containerRect.left - 295; // 显示在鼠标左侧
    }
    if (y < 0) {
        y = 10;
    }
    if (y + tooltipRect.height > containerRect.height) {
        y = containerRect.height - tooltipRect.height - 10;
    }

    // 设置位置并显示
    tooltip.style.left = x + 'px';
    tooltip.style.top = y + 'px';
    tooltip.style.display = 'block';
    tooltip.style.opacity = ''; // 重置opacity，让CSS控制
    tooltip.classList.add('show');
    tooltipVisible = true;
}

function hideDrawdownTooltip() {
    const tooltip = document.getElementById('drawdownTooltip');
    if (tooltip && tooltipVisible) {
        tooltip.classList.remove('show');
        tooltipVisible = false;
        // 确保提示框完全隐藏
        setTimeout(() => {
            if (!tooltipVisible) {
                tooltip.style.opacity = '0';
                tooltip.style.display = 'none';
            }
        }, 200); // 等待CSS transition完成
    }
}

// 处理图表悬停事件
function handleChartHover(event, chart, drawdownData) {
    try {
        // 检查必要数据
        if (!drawdownData || drawdownData.start_index === null || drawdownData.end_index === null) {
            hideDrawdownTooltip();
            return;
        }

        const canvasPosition = Chart.helpers.getRelativePosition(event, chart);
        const totalDataPoints = chart.data.labels.length;
        const plotAreaLeft = chart.chartArea.left;
        const plotAreaWidth = chart.chartArea.width;

        // 计算回撤区域的像素范围
        const startPixel = plotAreaLeft + (drawdownData.start_index / totalDataPoints) * plotAreaWidth;
        const endPixel = plotAreaLeft + (drawdownData.end_index / totalDataPoints) * plotAreaWidth;
        const mouseX = canvasPosition.x;

        // 添加容错范围
        const tolerance = 10; // 10像素容错
        const inDrawdownArea = mouseX >= (startPixel - tolerance) && mouseX <= (endPixel + tolerance);

        if (inDrawdownArea) {
            showDrawdownTooltip(event, drawdownData);
        } else {
            hideDrawdownTooltip();
        }
    } catch (error) {
        console.error('Error in handleChartHover:', error);
    }
}

// 鼠标移动时更新提示框位置
function updateDrawdownTooltipPosition(event) {
    const tooltip = document.getElementById('drawdownTooltip');
    if (!tooltip || !tooltip.classList.contains('show')) return;

    const chartContainer = document.querySelector('.chart-container');
    const containerRect = chartContainer.getBoundingClientRect();

    let x = event.clientX - containerRect.left + 15;
    let y = event.clientY - containerRect.top - 10;

    // 边界检查
    if (x + 280 > containerRect.width) {
        x = event.clientX - containerRect.left - 295;
    }
    if (y < 0) {
        y = 10;
    }

    tooltip.style.left = x + 'px';
    tooltip.style.top = y + 'px';
}
</script>
{% endblock %}
