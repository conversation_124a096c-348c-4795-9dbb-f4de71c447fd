{% extends "base_modern.html" %}

{% block title %}全局配置 - 量化交易系统{% endblock %}
{% block page_title %}全局配置{% endblock %}

{% block extra_css %}
<style>
    /* 配置管理工具样式 */
    .tool-card {
        transition: all 0.3s ease;
        background: #fff;
        cursor: pointer;
    }

    .tool-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        border-color: #007bff !important;
    }

    .tool-icon {
        transition: transform 0.3s ease;
    }

    .tool-card:hover .tool-icon {
        transform: scale(1.1);
    }

    .config-status-card {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border: 1px solid #dee2e6;
    }

    .status-item .form-label {
        font-size: 0.75rem;
        margin-bottom: 0.25rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .backup-item {
        transition: all 0.3s ease;
        background: #fff;
    }

    .backup-item:hover {
        transform: translateX(5px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        border-color: #007bff !important;
    }

    /* 备份操作按钮样式优化 */
    .backup-item .btn-group .btn {
        transition: all 0.2s ease;
        font-size: 0.8rem;
        padding: 0.375rem 0.75rem;
        border-width: 1px;
    }

    .backup-item .btn-group .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .backup-item .btn-outline-primary:hover {
        background-color: #007bff;
        border-color: #007bff;
        color: white;
    }

    .backup-item .btn-outline-info:hover {
        background-color: #17a2b8;
        border-color: #17a2b8;
        color: white;
    }

    /* 响应式按钮文本 */
    @media (max-width: 768px) {
        .backup-item .btn-group .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }

        .backup-item .btn-group .btn i {
            font-size: 0.9rem;
        }
    }

    .backup-list {
        max-height: 400px;
        overflow-y: auto;
    }

    .validation-result .alert {
        margin-bottom: 1rem;
    }

    .validation-result h6 {
        color: #495057;
        font-weight: 600;
        margin-bottom: 0.75rem;
    }

    #config-editor {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        line-height: 1.5;
        tab-size: 4;
    }

    #config-editor:focus {
        background: #fff;
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .language-python {
        background: #f8f9fa !important;
        border: 1px solid #dee2e6;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 0.875rem;
        line-height: 1.5;
    }

    /* 增强Python语法高亮样式 */
    .language-python .token.keyword {
        color: #0066cc !important;
        font-weight: bold;
    }

    .language-python .token.string {
        color: #008000 !important;
    }

    .language-python .token.comment {
        color: #6a737d !important;
        font-style: italic;
    }

    .language-python .token.number {
        color: #d73a49 !important;
    }

    .language-python .token.function {
        color: #6f42c1 !important;
    }

    .language-python .token.operator {
        color: #d73a49 !important;
    }

    .language-python .token.punctuation {
        color: #24292e !important;
    }

    /* 代码查看器容器样式 */
    .code-viewer-container {
        max-height: 500px;
        overflow-y: auto;
        background: #f8f9fa !important;
        border: 1px solid #dee2e6 !important;
        border-radius: 0.375rem !important;
        padding: 1rem !important;
        margin: 0 !important;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Courier New', monospace !important;
        font-size: 0.875rem !important;
        line-height: 1.6 !important;
        white-space: pre !important;
        word-wrap: normal !important;
        overflow-wrap: normal !important;
        tab-size: 4 !important;
    }

    .code-viewer-container code {
        background: transparent !important;
        border: none !important;
        padding: 0 !important;
        margin: 0 !important;
        font-family: inherit !important;
        font-size: inherit !important;
        line-height: inherit !important;
        white-space: inherit !important;
        word-wrap: inherit !important;
        overflow-wrap: inherit !important;
        display: block !important;
    }

    @media (max-width: 768px) {
        .tool-card {
            margin-bottom: 1rem;
        }

        .config-status-card .row {
            row-gap: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- 顶部统计卡片 -->
<div class="row g-4 mb-4">
    <div class="col-xl-3 col-md-6">
        <div class="stat-card">
            <div class="stat-icon purple">
                <i class="bi bi-gear"></i>
            </div>
            <div class="stat-value">{{ global_config|length if global_config else 0 }}</div>
            <div class="stat-label">配置项数量</div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="stat-card">
            <div class="stat-icon green">
                <i class="bi bi-check-circle"></i>
            </div>
            <div class="stat-value" id="config-status">正常</div>
            <div class="stat-label">配置状态</div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="stat-card">
            <div class="stat-icon blue">
                <i class="bi bi-clock-history"></i>
            </div>
            <div class="stat-value" id="last-modified">未知</div>
            <div class="stat-label">最后修改</div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="stat-card">
            <div class="stat-icon orange">
                <i class="bi bi-shield-check"></i>
            </div>
            <div class="stat-value" id="backup-count">0</div>
            <div class="stat-label">备份数量</div>
        </div>
    </div>
</div>

<!-- 全局配置主要内容 -->
<div class="row g-4">
    <div class="col-lg-8">
        <div class="modern-card">
            <div class="modern-card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-gear"></i>
                    系统配置参数
                </h5>
                <div class="btn-group" role="group">
                    <button class="modern-btn modern-btn-outline btn-sm" onclick="refreshConfig()">
                        <i class="bi bi-arrow-clockwise"></i>
                        刷新
                    </button>
                    <button class="modern-btn modern-btn-outline btn-sm" onclick="resetToDefault()">
                        <i class="bi bi-arrow-counterclockwise"></i>
                        重置
                    </button>
                </div>
            </div>
            <div class="modern-card-body">
                <form id="global-config-form">
                    <div class="row g-4">
                        <!-- 实时数据路径 -->
                        <div class="col-12">
                            <div class="config-item p-3 border rounded-3">
                                <label for="realtime_data_path" class="form-label fw-semibold">
                                    <i class="bi bi-folder text-primary"></i>
                                    实时数据路径
                                </label>
                                <input type="text" class="form-control" id="realtime_data_path" name="realtime_data_path"
                                       value="{{ global_config.realtime_data_path or '/opt/coin-realtime-data_v1.1.0/data' }}"
                                       placeholder="/opt/coin-realtime-data_v1.1.0/data">
                                <div class="form-text">数据中心的数据存储路径，用于获取实时行情数据</div>
                            </div>
                        </div>

                        <!-- 错误通知webhook地址 -->
                        <div class="col-12">
                            <div class="config-item p-3 border rounded-3">
                                <label for="error_webhook_url" class="form-label fw-semibold">
                                    <i class="bi bi-bell text-warning"></i>
                                    错误通知webhook地址
                                </label>
                                <input type="url" class="form-control" id="error_webhook_url" name="error_webhook_url"
                                       value="{{ global_config.error_webhook_url or '' }}"
                                       placeholder="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=...">
                                <div class="form-text">企业微信机器人webhook地址，用于接收系统错误通知</div>
                            </div>
                        </div>

                        <!-- 调试模式开关 -->
                        <div class="col-md-6">
                            <div class="config-item p-3 border rounded-3">
                                <label for="is_debug" class="form-label fw-semibold">
                                    <i class="bi bi-bug text-info"></i>
                                    调试模式
                                </label>
                                <select class="form-select" id="is_debug" name="is_debug">
                                    <option value="false" {% if not global_config.is_debug %}selected{% endif %}>禁用 (False)</option>
                                    <option value="true" {% if global_config.is_debug %}selected{% endif %}>启用 (True)</option>
                                </select>
                                <div class="form-text">启用后程序将模拟运行，不会实际下单</div>
                            </div>
                        </div>

                        <!-- 任务并发数 -->
                        <div class="col-md-6">
                            <div class="config-item p-3 border rounded-3">
                                <label for="job_num" class="form-label fw-semibold">
                                    <i class="bi bi-cpu text-success"></i>
                                    任务并发数
                                </label>
                                <input type="number" class="form-control" id="job_num" name="job_num"
                                       value="{{ global_config.job_num or 2 }}" min="1" max="16">
                                <div class="form-text">回测并行处理的任务数量，建议不超过CPU核心数</div>
                            </div>
                        </div>

                        <!-- 因子列数限制 -->
                        <div class="col-12">
                            <div class="config-item p-3 border rounded-3">
                                <label for="factor_col_limit" class="form-label fw-semibold">
                                    <i class="bi bi-memory text-danger"></i>
                                    因子列数限制
                                </label>
                                <input type="number" class="form-control" id="factor_col_limit" name="factor_col_limit"
                                       value="{{ global_config.factor_col_limit or 64 }}" min="16" max="256">
                                <div class="form-text">内存优化选项，一次性计算的因子列数，16GB内存建议64</div>
                            </div>
                        </div>
                    </div>

                    <!-- 模拟器配置 -->
                    <div class="mt-5">
                        <h6 class="fw-bold text-primary mb-3">
                            <i class="bi bi-gear-wide-connected"></i>
                            模拟器配置
                        </h6>
                        <div class="row g-3">
                            <!-- 初始USDT金额 -->
                            <div class="col-md-4">
                                <div class="config-item p-3 border rounded-3">
                                    <label for="initial_usdt" class="form-label fw-semibold">初始USDT金额</label>
                                    <input type="number" class="form-control" id="initial_usdt" name="simulator_config.initial_usdt"
                                           value="{{ global_config.simulator_config.initial_usdt if global_config.simulator_config else 2000 }}"
                                           min="100" step="100">
                                    <div class="form-text">模拟交易的初始资金</div>
                                </div>
                            </div>

                            <!-- 合约手续费率 -->
                            <div class="col-md-4">
                                <div class="config-item p-3 border rounded-3">
                                    <label for="swap_c_rate" class="form-label fw-semibold">合约手续费率</label>
                                    <input type="number" class="form-control" id="swap_c_rate" name="simulator_config.swap_c_rate"
                                           value="{{ global_config.simulator_config.swap_c_rate if global_config.simulator_config else 0.00085 }}"
                                           min="0" max="1" step="0.00001">
                                    <div class="form-text">合约交易手续费率（包含滑点）</div>
                                </div>
                            </div>

                            <!-- 现货手续费率 -->
                            <div class="col-md-4">
                                <div class="config-item p-3 border rounded-3">
                                    <label for="spot_c_rate" class="form-label fw-semibold">现货手续费率</label>
                                    <input type="number" class="form-control" id="spot_c_rate" name="simulator_config.spot_c_rate"
                                           value="{{ global_config.simulator_config.spot_c_rate if global_config.simulator_config else 0.002 }}"
                                           min="0" max="1" step="0.00001">
                                    <div class="form-text">现货交易手续费率（包含滑点）</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 数据配置 -->
                    <div class="mt-5">
                        <h6 class="fw-bold text-primary mb-3">
                            <i class="bi bi-database"></i>
                            数据配置
                        </h6>
                        <div class="row g-3">
                            <!-- 最小K线数量 -->
                            <div class="col-md-6">
                                <div class="config-item p-3 border rounded-3">
                                    <label for="min_kline_num" class="form-label fw-semibold">最小K线数量</label>
                                    <input type="number" class="form-control" id="min_kline_num" name="data_config.min_kline_num"
                                           value="{{ global_config.data_config.min_kline_num if global_config.data_config else 0 }}"
                                           min="0" step="1">
                                    <div class="form-text">剔除上市时间不足的新币，0表示不限制</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 代理配置 -->
                    <div class="mt-5">
                        <h6 class="fw-bold text-primary mb-3">
                            <i class="bi bi-shield-check"></i>
                            代理配置
                        </h6>
                        <div class="row g-3">
                            <!-- HTTP代理 -->
                            <div class="col-md-6">
                                <div class="config-item p-3 border rounded-3">
                                    <label for="proxy_http" class="form-label fw-semibold">HTTP代理</label>
                                    <input type="text" class="form-control" id="proxy_http" name="proxy.http"
                                           value="{{ global_config.proxy.http if global_config.proxy else '' }}"
                                           placeholder="http://127.0.0.1:7890">
                                    <div class="form-text">HTTP代理服务器地址，留空表示不使用代理</div>
                                </div>
                            </div>

                            <!-- HTTPS代理 -->
                            <div class="col-md-6">
                                <div class="config-item p-3 border rounded-3">
                                    <label for="proxy_https" class="form-label fw-semibold">HTTPS代理</label>
                                    <input type="text" class="form-control" id="proxy_https" name="proxy.https"
                                           value="{{ global_config.proxy.https if global_config.proxy else '' }}"
                                           placeholder="http://127.0.0.1:7890">
                                    <div class="form-text">HTTPS代理服务器地址，留空表示不使用代理</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end gap-2 mt-4">
                        <button type="button" class="modern-btn modern-btn-outline" onclick="refreshConfig()">
                            <i class="bi bi-arrow-clockwise"></i>
                            刷新
                        </button>
                        <button type="button" class="modern-btn modern-btn-outline" onclick="resetToDefault()">
                            <i class="bi bi-arrow-counterclockwise"></i>
                            重置
                        </button>
                        <button type="submit" class="modern-btn modern-btn-primary">
                            <i class="bi bi-check-lg"></i>
                            保存配置
                        </button>
                    </div>
                </form>
            </div>
        </div>



    </div>

    <!-- 右侧栏 - 配置管理工具 -->
    <div class="col-lg-4">
        <!-- 配置管理工具模块 -->
        <div class="modern-card">
            <div class="modern-card-header">
                <h5 class="mb-0">
                    <i class="bi bi-tools text-primary"></i>
                    配置管理工具
                </h5>
                <small class="text-muted">配置文件备份、验证和编辑工具</small>
            </div>
            <div class="modern-card-body">
                <!-- 配置文件状态 -->
                <div class="config-status-card p-3 border rounded-3 bg-light mb-4">
                    <h6 class="fw-bold mb-3">
                        <i class="bi bi-file-earmark-code text-info"></i>
                        配置文件状态
                    </h6>
                    <div class="row g-2 small">
                        <div class="col-6">
                            <div class="status-item">
                                <label class="form-label text-muted">文件名</label>
                                <div class="fw-semibold">config.py</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="status-item">
                                <label class="form-label text-muted">文件大小</label>
                                <div class="fw-semibold" id="file-size">2.4 KB</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="status-item">
                                <label class="form-label text-muted">最后修改</label>
                                <div class="fw-semibold" id="file-modified">2025/7/7 22:33:07</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="status-item">
                                <label class="form-label text-muted">状态</label>
                                <div class="fw-semibold text-success" id="config-file-status">正常</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 工具按钮组 -->
                <div class="row g-2 mb-4">
                    <!-- 备份当前配置 -->
                    <div class="col-12">
                        <button class="btn btn-primary w-100" onclick="createConfigBackup()">
                            <i class="bi bi-shield-check"></i>
                            备份当前配置
                        </button>
                    </div>
                    <!-- 导入配置文件 -->
                    <div class="col-12">
                        <button class="btn btn-secondary w-100" onclick="importConfigFile()">
                            <i class="bi bi-upload"></i>
                            导入配置文件
                        </button>
                    </div>
                    <!-- 验证配置有效性 -->
                    <div class="col-12">
                        <button class="btn btn-success w-100" onclick="validateConfigFile()">
                            <i class="bi bi-check-circle"></i>
                            验证配置有效性
                        </button>
                    </div>
                    <!-- 查看修改历史 -->
                    <div class="col-12">
                        <button class="btn btn-info w-100" onclick="viewConfigHistory()">
                            <i class="bi bi-clock-history"></i>
                            查看修改历史
                        </button>
                    </div>
                    <!-- 查看源文件 -->
                    <div class="col-12">
                        <button class="btn btn-outline-primary w-100" onclick="viewSourceFile()">
                            <i class="bi bi-file-earmark-text"></i>
                            查看源文件
                        </button>
                    </div>
                    <!-- 编辑源文件 -->
                    <div class="col-12">
                        <button class="btn btn-warning w-100" onclick="editSourceFile()">
                            <i class="bi bi-pencil-square"></i>
                            编辑源文件
                        </button>
                    </div>
                </div>

                <!-- 历史备份管理 -->
                <div class="mt-4">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="fw-bold mb-0">
                            <i class="bi bi-clock-history text-secondary"></i>
                            历史备份管理
                        </h6>
                        <button class="btn btn-outline-secondary btn-sm" onclick="refreshBackupList()">
                            <i class="bi bi-arrow-clockwise"></i>
                            刷新列表
                        </button>
                    </div>
                    <div id="backup-list-container">
                        <div class="text-center py-3 text-muted">
                            <i class="bi bi-hourglass-split"></i>
                            正在加载备份列表...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 配置预览模态框 -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-eye"></i>
                    配置更改预览
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="preview-content">
                    <!-- 动态内容 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="modern-btn modern-btn-primary" onclick="confirmSave()">
                    <i class="bi bi-check-lg"></i>
                    确认保存
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 配置帮助模态框 -->
<div class="modal fade" id="helpModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="helpModalTitle">
                    <i class="bi bi-question-circle"></i>
                    配置帮助
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="helpModalBody">
                <!-- 动态内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 配置文件查看模态框 -->
<div class="modal fade" id="viewConfigModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-file-earmark-code"></i>
                    查看配置文件 - config.py
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="row g-2 text-sm">
                        <div class="col-auto">
                            <span class="badge bg-info">文件大小: <span id="view-file-size">-</span></span>
                        </div>
                        <div class="col-auto">
                            <span class="badge bg-secondary">行数: <span id="view-line-count">-</span></span>
                        </div>
                        <div class="col-auto">
                            <span class="badge bg-success">最后修改: <span id="view-last-modified">-</span></span>
                        </div>
                    </div>
                </div>
                <pre class="code-viewer-container"><code class="language-python" id="config-file-content"></code></pre>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="switchToEditMode()">
                    <i class="bi bi-pencil"></i>
                    切换到编辑模式
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 配置文件编辑模态框 -->
<div class="modal fade" id="editConfigModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-pencil-square"></i>
                    编辑配置文件 - config.py
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>注意：</strong>直接编辑配置文件可能影响系统运行，保存前会自动创建备份并进行语法验证。
                </div>
                <div class="mb-3">
                    <div class="row g-2">
                        <div class="col-auto">
                            <button class="btn btn-sm btn-outline-primary" onclick="formatCode()">
                                <i class="bi bi-code"></i>
                                格式化代码
                            </button>
                        </div>
                        <div class="col-auto">
                            <button class="btn btn-sm btn-outline-success" onclick="validateSyntaxInEditor()">
                                <i class="bi bi-check-circle"></i>
                                验证语法
                            </button>
                        </div>
                    </div>
                </div>
                <textarea id="config-editor" class="form-control" rows="20" style="font-family: 'Courier New', monospace; font-size: 14px;"></textarea>
                <div id="syntax-validation-result" class="mt-2"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" onclick="saveConfigFile()">
                    <i class="bi bi-save"></i>
                    保存文件
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 验证结果模态框 -->
<div class="modal fade" id="validationResultModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-check-circle"></i>
                    配置验证结果
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="validation-result-content"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">确定</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 页面加载时初始化
    $(document).ready(function() {
        // 初始化所有功能
        loadGlobalConfig();      // 加载全局配置到表单
        updateConfigStats();     // 更新配置统计
        updateConfigStatus();    // 更新配置状态
        refreshBackupList();     // 立即加载备份列表

        // 全局配置表单提交
        $('#global-config-form').on('submit', function(e) {
            e.preventDefault();
            saveGlobalConfig();
        });

        // 初始化Bootstrap tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
    
    // 加载全局配置到表单
    function loadGlobalConfig() {
        $.ajax({
            url: '/api/config/global',
            method: 'GET',
            success: function(response) {
                if (response && typeof response === 'object') {
                    // 填充表单字段
                    if (response.realtime_data_path) {
                        $('#realtime_data_path').val(response.realtime_data_path);
                    }
                    if (response.error_webhook_url) {
                        $('#error_webhook_url').val(response.error_webhook_url);
                    }
                    if (typeof response.is_debug === 'boolean') {
                        $('#is_debug').prop('checked', response.is_debug);
                    }
                    if (response.job_num) {
                        $('#job_num').val(response.job_num);
                    }
                    if (response.factor_col_limit) {
                        $('#factor_col_limit').val(response.factor_col_limit);
                    }

                    // 填充simulator_config子字段
                    if (response.simulator_config) {
                        if (response.simulator_config.initial_usdt) {
                            $('#initial_usdt').val(response.simulator_config.initial_usdt);
                        }
                        if (response.simulator_config.swap_c_rate) {
                            $('#swap_c_rate').val(response.simulator_config.swap_c_rate);
                        }
                        if (response.simulator_config.spot_c_rate) {
                            $('#spot_c_rate').val(response.simulator_config.spot_c_rate);
                        }
                    }

                    // 填充data_config子字段
                    if (response.data_config && response.data_config.min_kline_num !== undefined) {
                        $('#min_kline_num').val(response.data_config.min_kline_num);
                    }

                    // 填充proxy配置
                    if (response.proxy) {
                        if (response.proxy.http) {
                            $('#proxy_http').val(response.proxy.http);
                        }
                        if (response.proxy.https) {
                            $('#proxy_https').val(response.proxy.https);
                        }
                    }
                }
            },
            error: function(xhr, status, error) {
                console.error('加载全局配置失败:', error);
                showMessage('加载配置失败，请检查网络连接', 'warning');
            }
        });
    }

    // 更新配置统计
    function updateConfigStats() {
        // 获取真实的备份数量
        $.ajax({
            url: '/api/config/backup-list',
            method: 'GET',
            success: function(response) {
                if (response.success && response.backups) {
                    document.getElementById('backup-count').textContent = response.backups.length;
                } else {
                    document.getElementById('backup-count').textContent = '0';
                }
            },
            error: function() {
                document.getElementById('backup-count').textContent = '0';
            }
        });

        // 更新其他统计信息
        document.getElementById('last-modified').textContent = '刚刚';
        document.getElementById('file-modified').textContent = new Date().toLocaleString('zh-CN');
    }
    
    // 保存全局配置
    function saveGlobalConfig() {
        const formData = new FormData(document.getElementById('global-config-form'));
        const config = {};

        for (let [key, value] of formData.entries()) {
            // 处理嵌套对象
            if (key.includes('.')) {
                const parts = key.split('.');
                if (parts.length === 2) {
                    const [parent, child] = parts;
                    if (!config[parent]) {
                        config[parent] = {};
                    }
                    // 转换数据类型
                    if (value === 'true') {
                        config[parent][child] = true;
                    } else if (value === 'false') {
                        config[parent][child] = false;
                    } else if (!isNaN(value) && value !== '') {
                        config[parent][child] = parseFloat(value);
                    } else {
                        config[parent][child] = value;
                    }
                }
            } else {
                // 处理顶级配置项
                if (value === 'true') {
                    config[key] = true;
                } else if (value === 'false') {
                    config[key] = false;
                } else if (!isNaN(value) && value !== '') {
                    config[key] = parseFloat(value);
                } else {
                    config[key] = value;
                }
            }
        }

        $.ajax({
            url: '/api/config/global',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(config),
            success: function(response) {
                if (response.success) {
                    showMessage('全局配置保存成功', 'success');
                    updateConfigStats();
                } else {
                    showMessage('保存失败: ' + response.message, 'danger');
                }
            },
            error: function() {
                showMessage('保存请求失败', 'danger');
            }
        });
    }
    
    // 刷新配置
    function refreshConfig() {
        location.reload();
    }
    
    // 重置为默认配置
    function resetToDefault() {
        if (confirm('确定要重置为默认配置吗？当前的更改将会丢失。')) {
            $.ajax({
                url: '/api/config/reset',
                method: 'POST',
                success: function(response) {
                    if (response.success) {
                        showMessage('配置已重置为默认值', 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showMessage('重置失败: ' + response.message, 'danger');
                    }
                },
                error: function() {
                    showMessage('重置请求失败', 'danger');
                }
            });
        }
    }
    
    // 预览更改
    function previewChanges() {
        const formData = new FormData(document.getElementById('global-config-form'));
        const changes = {};
        
        for (let [key, value] of formData.entries()) {
            changes[key] = value;
        }
        
        let previewHtml = '<div class="table-responsive"><table class="table table-striped"><thead><tr><th>配置项</th><th>新值</th></tr></thead><tbody>';
        
        for (let [key, value] of Object.entries(changes)) {
            previewHtml += `<tr><td>${key}</td><td><code>${value}</code></td></tr>`;
        }
        
        previewHtml += '</tbody></table></div>';
        
        document.getElementById('preview-content').innerHTML = previewHtml;
        new bootstrap.Modal(document.getElementById('previewModal')).show();
    }
    
    // 确认保存
    function confirmSave() {
        bootstrap.Modal.getInstance(document.getElementById('previewModal')).hide();
        saveGlobalConfig();
    }

    // ===== 配置管理工具功能 =====

    // 显示消息提示
    function showMessage(message, type = 'info') {
        // 创建消息提示元素
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type === 'danger' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // 添加到页面
        document.body.appendChild(alertDiv);

        // 3秒后自动移除
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 3000);
    }

    // 创建配置备份
    function createConfigBackup() {
        $.ajax({
            url: '/api/config/backup',
            method: 'POST',
            success: function(response) {
                if (response.success) {
                    showMessage('配置备份创建成功: ' + response.backup_file, 'success');
                    refreshBackupList();
                    updateConfigStatus();
                } else {
                    showMessage('备份失败: ' + response.message, 'danger');
                }
            },
            error: function() {
                showMessage('备份请求失败', 'danger');
            }
        });
    }

    // 验证配置文件
    function validateConfigFile() {
        $.ajax({
            url: '/api/config/validate-file',
            method: 'POST',
            success: function(response) {
                showValidationResult(response);
            },
            error: function() {
                showMessage('验证请求失败', 'danger');
            }
        });
    }

    // 显示验证结果
    function showValidationResult(result) {
        let content = '<div class="validation-result">';

        // 基本信息
        content += '<div class="mb-3">';
        content += '<h6>文件信息</h6>';
        content += '<div class="row g-2">';
        content += `<div class="col-auto"><span class="badge bg-info">文件大小: ${result.file_size || 0} 字节</span></div>`;
        content += `<div class="col-auto"><span class="badge bg-secondary">最后修改: ${result.last_modified || '-'}</span></div>`;
        content += '</div>';
        content += '</div>';

        // 验证状态
        content += '<div class="mb-3">';
        content += '<h6>验证状态</h6>';
        content += '<div class="row g-2">';
        content += `<div class="col-auto"><span class="badge ${result.syntax_valid ? 'bg-success' : 'bg-danger'}">语法检查: ${result.syntax_valid ? '通过' : '失败'}</span></div>`;
        content += `<div class="col-auto"><span class="badge ${result.runtime_valid ? 'bg-success' : 'bg-danger'}">运行时检查: ${result.runtime_valid ? '通过' : '失败'}</span></div>`;
        content += '</div>';
        content += '</div>';

        // 错误信息
        if (result.errors && result.errors.length > 0) {
            content += '<div class="mb-3">';
            content += '<h6 class="text-danger">错误信息</h6>';
            content += '<div class="alert alert-danger">';
            result.errors.forEach(error => {
                content += `<div><i class="bi bi-x-circle"></i> ${error}</div>`;
            });
            content += '</div>';
            content += '</div>';
        }

        // 警告信息
        if (result.warnings && result.warnings.length > 0) {
            content += '<div class="mb-3">';
            content += '<h6 class="text-warning">警告信息</h6>';
            content += '<div class="alert alert-warning">';
            result.warnings.forEach(warning => {
                content += `<div><i class="bi bi-exclamation-triangle"></i> ${warning}</div>`;
            });
            content += '</div>';
            content += '</div>';
        }

        // 成功信息
        if (result.success) {
            content += '<div class="alert alert-success">';
            content += '<i class="bi bi-check-circle"></i> 配置文件验证通过，所有检查项目正常！';
            content += '</div>';
        }

        content += '</div>';

        document.getElementById('validation-result-content').innerHTML = content;
        new bootstrap.Modal(document.getElementById('validationResultModal')).show();
    }

    // 查看源文件（别名函数）
    function viewSourceFile() {
        viewConfigFile();
    }

    // 编辑源文件（别名函数）
    function editSourceFile() {
        editConfigFile();
    }

    // 查看配置文件
    function viewConfigFile() {
        $.ajax({
            url: '/api/config/file-content',
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    document.getElementById('view-file-size').textContent = response.size_human;
                    document.getElementById('view-line-count').textContent = response.line_count;
                    document.getElementById('view-last-modified').textContent = response.last_modified;

                    const codeElement = document.getElementById('config-file-content');

                    // 清除所有内容和属性
                    codeElement.innerHTML = '';
                    codeElement.textContent = '';
                    codeElement.removeAttribute('data-highlighted');
                    codeElement.removeAttribute('style');

                    // 设置正确的class和内容
                    codeElement.className = 'language-python';
                    codeElement.textContent = response.content;

                    // 如果有Prism.js，进行语法高亮
                    if (typeof Prism !== 'undefined') {
                        // 使用setTimeout确保DOM更新完成
                        setTimeout(() => {
                            Prism.highlightElement(codeElement);
                        }, 10);
                    }

                    new bootstrap.Modal(document.getElementById('viewConfigModal')).show();
                } else {
                    showMessage('获取文件内容失败: ' + response.message, 'danger');
                }
            },
            error: function() {
                showMessage('获取文件内容请求失败', 'danger');
            }
        });
    }

    // 编辑配置文件
    function editConfigFile() {
        $.ajax({
            url: '/api/config/file-content',
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    document.getElementById('config-editor').value = response.content;
                    document.getElementById('syntax-validation-result').innerHTML = '';
                    new bootstrap.Modal(document.getElementById('editConfigModal')).show();
                } else {
                    showMessage('获取文件内容失败: ' + response.message, 'danger');
                }
            },
            error: function() {
                showMessage('获取文件内容请求失败', 'danger');
            }
        });
    }

    // 从查看模式切换到编辑模式
    function switchToEditMode() {
        const content = document.getElementById('config-file-content').textContent;
        document.getElementById('config-editor').value = content;
        document.getElementById('syntax-validation-result').innerHTML = '';

        // 关闭查看模态框，打开编辑模态框
        bootstrap.Modal.getInstance(document.getElementById('viewConfigModal')).hide();
        new bootstrap.Modal(document.getElementById('editConfigModal')).show();
    }

    // 在编辑器中验证语法
    function validateSyntaxInEditor() {
        const content = document.getElementById('config-editor').value;
        const resultDiv = document.getElementById('syntax-validation-result');

        // 显示验证中状态
        resultDiv.innerHTML = '<div class="alert alert-info"><i class="bi bi-hourglass-split"></i> 正在验证语法...</div>';

        // 先保存当前编辑的内容到临时文件进行验证
        $.ajax({
            url: '/api/config/file-content',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                content: content
            }),
            success: function(saveResponse) {
                if (saveResponse.success) {
                    // 保存成功后进行验证
                    $.ajax({
                        url: '/api/config/validate-file',
                        method: 'POST',
                        success: function(response) {
                            let resultHtml = '';

                            if (response.success) {
                                resultHtml = '<div class="alert alert-success"><i class="bi bi-check-circle"></i> 语法验证通过</div>';

                                // 显示警告信息（如果有）
                                if (response.warnings && response.warnings.length > 0) {
                                    resultHtml += '<div class="alert alert-warning mt-2"><i class="bi bi-exclamation-triangle"></i> 警告信息:<ul class="mb-0 mt-2">';
                                    response.warnings.forEach(warning => {
                                        resultHtml += `<li>${warning}</li>`;
                                    });
                                    resultHtml += '</ul></div>';
                                }
                            } else {
                                resultHtml = '<div class="alert alert-danger"><i class="bi bi-x-circle"></i> 语法验证失败</div>';

                                // 显示错误信息
                                if (response.errors && response.errors.length > 0) {
                                    resultHtml += '<div class="alert alert-danger mt-2"><strong>错误详情:</strong><ul class="mb-0 mt-2">';
                                    response.errors.forEach(error => {
                                        resultHtml += `<li>${error}</li>`;
                                    });
                                    resultHtml += '</ul></div>';
                                }
                            }

                            resultDiv.innerHTML = resultHtml;
                        },
                        error: function() {
                            resultDiv.innerHTML = '<div class="alert alert-danger"><i class="bi bi-x-circle"></i> 验证请求失败</div>';
                        }
                    });
                } else {
                    resultDiv.innerHTML = '<div class="alert alert-danger"><i class="bi bi-x-circle"></i> 保存临时文件失败，无法验证</div>';
                }
            },
            error: function() {
                resultDiv.innerHTML = '<div class="alert alert-danger"><i class="bi bi-x-circle"></i> 保存临时文件失败，无法验证</div>';
            }
        });
    }

    // 格式化代码
    function formatCode() {
        const editor = document.getElementById('config-editor');
        const content = editor.value;

        // 简单的Python代码格式化
        const lines = content.split('\n');
        const formatted = [];
        let indentLevel = 0;

        for (let line of lines) {
            const trimmed = line.trim();

            if (trimmed === '') {
                formatted.push('');
                continue;
            }

            if (trimmed.startsWith('#')) {
                formatted.push(trimmed);
                continue;
            }

            // 处理缩进
            if (trimmed.endsWith(':')) {
                formatted.push('    '.repeat(indentLevel) + trimmed);
                indentLevel++;
            } else if (trimmed.startsWith('}') || trimmed.startsWith(']') || trimmed.startsWith(')')){
                indentLevel = Math.max(0, indentLevel - 1);
                formatted.push('    '.repeat(indentLevel) + trimmed);
            } else {
                formatted.push('    '.repeat(indentLevel) + trimmed);
            }
        }

        editor.value = formatted.join('\n');
        showMessage('代码格式化完成', 'success');
    }

    // 保存配置文件
    function saveConfigFile() {
        const content = document.getElementById('config-editor').value;

        if (!content.trim()) {
            showMessage('配置文件内容不能为空', 'danger');
            return;
        }

        $.ajax({
            url: '/api/config/file-content',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({content: content}),
            success: function(response) {
                if (response.success) {
                    showMessage('配置文件保存成功' + (response.backup_created ? '，已创建备份: ' + response.backup_created : ''), 'success');
                    bootstrap.Modal.getInstance(document.getElementById('editConfigModal')).hide();
                    refreshBackupList();
                    updateConfigStatus();
                    loadGlobalConfig(); // 重新加载配置到表单
                } else {
                    showMessage('保存失败: ' + response.message, 'danger');
                }
            },
            error: function() {
                showMessage('保存请求失败', 'danger');
            }
        });
    }

    // 刷新备份列表
    function refreshBackupList() {
        $.ajax({
            url: '/api/config/backups',
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    displayBackupList(response.backups);
                } else {
                    document.getElementById('backup-list-container').innerHTML =
                        '<div class="text-center py-3 text-muted">获取备份列表失败</div>';
                }
            },
            error: function() {
                document.getElementById('backup-list-container').innerHTML =
                    '<div class="text-center py-3 text-muted">获取备份列表失败</div>';
            }
        });
    }

    // 显示备份列表
    function displayBackupList(backups) {
        const container = document.getElementById('backup-list-container');

        if (backups.length === 0) {
            container.innerHTML = '<div class="text-center py-3 text-muted">暂无备份文件</div>';
            return;
        }

        let html = '<div class="backup-list">';
        backups.forEach(backup => {
            html += `
                <div class="backup-item p-3 border rounded-3 mb-2">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="fw-semibold">${backup.filename}</div>
                            <small class="text-muted">
                                <i class="bi bi-clock"></i> ${backup.created_time} |
                                <i class="bi bi-file-earmark"></i> ${backup.size_human}
                            </small>
                        </div>
                        <div class="col-auto">
                            <div class="btn-group btn-group-sm" role="group">
                                <button class="btn btn-outline-primary"
                                        onclick="restoreBackup('${backup.filename}')"
                                        data-bs-toggle="tooltip"
                                        data-bs-placement="top"
                                        title="恢复此备份文件为当前配置">
                                    <i class="bi bi-arrow-clockwise me-1"></i>
                                    <span class="d-none d-md-inline">恢复</span>
                                </button>
                                <button class="btn btn-outline-info"
                                        onclick="viewBackup('${backup.filename}')"
                                        data-bs-toggle="tooltip"
                                        data-bs-placement="top"
                                        title="查看此备份文件的详细内容">
                                    <i class="bi bi-eye me-1"></i>
                                    <span class="d-none d-md-inline">查看</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
        html += '</div>';

        container.innerHTML = html;

        // 重新初始化新创建按钮的tooltips
        var tooltipTriggerList = [].slice.call(container.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    // 恢复备份
    function restoreBackup(filename) {
        if (!confirm(`确定要恢复备份 "${filename}" 吗？\n\n当前配置将被替换，系统会自动创建当前配置的备份。`)) {
            return;
        }

        $.ajax({
            url: '/api/config/restore',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({backup_filename: filename}),
            success: function(response) {
                if (response.success) {
                    showMessage('配置恢复成功' + (response.current_backup ? '，当前配置已备份为: ' + response.current_backup : ''), 'success');
                    refreshBackupList();
                    updateConfigStatus();
                    loadGlobalConfig(); // 重新加载配置到表单
                } else {
                    showMessage('恢复失败: ' + response.message, 'danger');
                }
            },
            error: function() {
                showMessage('恢复请求失败', 'danger');
            }
        });
    }

    // 查看备份内容
    function viewBackup(filename) {
        // 这里可以实现查看备份文件内容的功能
        showMessage('查看备份功能开发中...', 'info');
    }

    // 导入配置文件
    function importConfigFile() {
        // 创建文件输入元素
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.py,.txt';
        input.onchange = function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const content = e.target.result;
                    // 这里可以实现导入配置文件的功能
                    showMessage('导入配置文件功能开发中...', 'info');
                };
                reader.readAsText(file);
            }
        };
        input.click();
    }

    // 查看配置修改历史
    function viewConfigHistory() {
        // 这里可以实现查看配置修改历史的功能
        showMessage('查看修改历史功能开发中...', 'info');
    }

    // 更新配置文件状态
    function updateConfigStatus() {
        $.ajax({
            url: '/api/config/file-content',
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    document.getElementById('config-file-size').textContent = response.size_human;
                    document.getElementById('config-last-modified').textContent = response.last_modified;
                    document.getElementById('config-status').textContent = '正常';
                    document.getElementById('config-status').className = 'badge bg-success';
                } else {
                    document.getElementById('config-status').textContent = '异常';
                    document.getElementById('config-status').className = 'badge bg-danger';
                }
            },
            error: function() {
                document.getElementById('config-status').textContent = '未知';
                document.getElementById('config-status').className = 'badge bg-secondary';
            }
        });
    }
    
    // 显示配置帮助
    function showConfigHelp(configKey) {
        const helpContent = {
            'default': '这是一个系统配置参数，用于控制系统的行为。请根据实际需要进行调整。',
            // 可以根据实际配置项添加更多帮助内容
        };
        
        document.getElementById('helpModalTitle').innerHTML = `<i class="bi bi-question-circle"></i> ${configKey} - 配置帮助`;
        document.getElementById('helpModalBody').innerHTML = `
            <div class="alert alert-info">
                <h6>配置项：${configKey}</h6>
                <p>${helpContent[configKey] || helpContent['default']}</p>
            </div>
            <div class="mt-3">
                <h6>注意事项：</h6>
                <ul>
                    <li>修改配置后需要重启系统才能生效</li>
                    <li>建议在修改前先备份当前配置</li>
                    <li>如有疑问请参考系统文档</li>
                </ul>
            </div>
        `;
        
        new bootstrap.Modal(document.getElementById('helpModal')).show();
    }
    
    // 创建默认配置
    function createDefaultConfig() {
        if (confirm('确定要创建默认配置吗？')) {
            $.ajax({
                url: '/api/config/create-default',
                method: 'POST',
                success: function(response) {
                    if (response.success) {
                        showMessage('默认配置创建成功', 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showMessage('创建失败: ' + response.message, 'danger');
                    }
                },
                error: function() {
                    showMessage('创建请求失败', 'danger');
                }
            });
        }
    }


</script>
{% endblock %}
