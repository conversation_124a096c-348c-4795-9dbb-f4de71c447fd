"""
增强的进程管理模块
实现孤儿进程检测、资源清理和停止验证功能
"""

import os
import sys
import time
import glob
import logging
import threading
import subprocess
import traceback
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
import psutil

# 获取项目根目录
PROJECT_ROOT = Path(__file__).parent

class ProcessType(Enum):
    """进程类型枚举"""
    MANAGED = "managed"      # 托管进程（有句柄）
    ORPHAN = "orphan"        # 孤儿进程（无句柄）
    CHILD = "child"          # 子进程
    UNKNOWN = "unknown"      # 未知类型

@dataclass
class ProcessInfo:
    """进程信息数据类"""
    pid: int
    process_obj: Optional[psutil.Process]
    process_type: ProcessType
    cmdline: List[str]
    create_time: float
    parent_pid: Optional[int] = None
    managed_handle: Optional[subprocess.Popen] = None

class ProcessDetector(ABC):
    """进程检测器抽象基类"""
    
    @abstractmethod
    def detect_processes(self) -> List[ProcessInfo]:
        """检测相关进程"""
        pass

class TradingProcessDetector(ProcessDetector):
    """交易进程检测器"""
    
    def __init__(self, target_scripts: List[str] = None, managed_process: subprocess.Popen = None):
        self.target_scripts = target_scripts or [
            'startup.py', 
            'schedule_startup.py',
            'account_exec.py'
        ]
        self.managed_process = managed_process
        self.logger = logging.getLogger(__name__)
    
    def detect_processes(self) -> List[ProcessInfo]:
        """检测所有交易相关进程"""
        processes = []
        
        # 1. 检测托管进程
        managed_processes = self._detect_managed_processes()
        processes.extend(managed_processes)
        
        # 2. 检测孤儿进程
        orphan_processes = self._detect_orphan_processes(managed_processes)
        processes.extend(orphan_processes)
        
        # 3. 检测子进程
        child_processes = self._detect_child_processes(processes)
        processes.extend(child_processes)
        
        return processes
    
    def _detect_managed_processes(self) -> List[ProcessInfo]:
        """检测托管进程（有进程句柄的）"""
        managed_processes = []

        if self.managed_process and self.managed_process.poll() is None:
            try:
                proc = psutil.Process(self.managed_process.pid)
                process_info = ProcessInfo(
                    pid=self.managed_process.pid,
                    process_obj=proc,
                    process_type=ProcessType.MANAGED,
                    cmdline=proc.cmdline(),
                    create_time=proc.create_time(),
                    managed_handle=self.managed_process
                )
                managed_processes.append(process_info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass

        return managed_processes
    
    def _detect_orphan_processes(self, managed_processes: List[ProcessInfo]) -> List[ProcessInfo]:
        """检测孤儿进程"""
        orphan_processes = []
        managed_pids = {p.pid for p in managed_processes}
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time', 'ppid']):
            try:
                cmdline = proc.info['cmdline']
                if not cmdline:
                    continue
                
                cmdline_str = ' '.join(cmdline)
                
                # 检查是否是目标脚本
                if any(script in cmdline_str for script in self.target_scripts):
                    pid = proc.info['pid']
                    
                    # 排除已知的托管进程
                    if pid not in managed_pids:
                        process_info = ProcessInfo(
                            pid=pid,
                            process_obj=psutil.Process(pid),
                            process_type=ProcessType.ORPHAN,
                            cmdline=cmdline,
                            create_time=proc.info['create_time'],
                            parent_pid=proc.info['ppid']
                        )
                        orphan_processes.append(process_info)
                        
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        return orphan_processes
    
    def _detect_child_processes(self, parent_processes: List[ProcessInfo]) -> List[ProcessInfo]:
        """检测子进程"""
        child_processes = []
        parent_pids = {p.pid for p in parent_processes}
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time', 'ppid']):
            try:
                ppid = proc.info['ppid']
                if ppid in parent_pids:
                    child_info = ProcessInfo(
                        pid=proc.info['pid'],
                        process_obj=psutil.Process(proc.info['pid']),
                        process_type=ProcessType.CHILD,
                        cmdline=proc.info['cmdline'] or [],
                        create_time=proc.info['create_time'],
                        parent_pid=ppid
                    )
                    child_processes.append(child_info)
                    
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        return child_processes

class ProcessCleaner:
    """进程清理器"""
    
    def __init__(self, detector: ProcessDetector):
        self.detector = detector
        self.logger = logging.getLogger(__name__)
    
    def cleanup_all_processes(self) -> Dict[str, Any]:
        """清理所有相关进程"""
        start_time = time.time()
        
        # 1. 检测所有进程
        processes = self.detector.detect_processes()
        
        if not processes:
            return {
                "success": True,
                "message": "未发现需要清理的进程",
                "processes_found": 0,
                "cleanup_time": time.time() - start_time
            }
        
        # 2. 按类型分组清理
        cleanup_results = {
            "managed": [],
            "orphan": [],
            "child": []
        }
        
        # 3. 先清理子进程，再清理父进程
        cleanup_order = [
            (ProcessType.CHILD, "child"),
            (ProcessType.ORPHAN, "orphan"),
            (ProcessType.MANAGED, "managed")
        ]

        for process_type, type_key in cleanup_order:
            type_processes = [p for p in processes if p.process_type == process_type]
            for process_info in type_processes:
                result = self._cleanup_single_process(process_info)
                cleanup_results[type_key].append(result)
        
        # 4. 统计结果
        total_processes = len(processes)
        successful_cleanups = sum(
            len([r for r in results if r['success']])
            for results in cleanup_results.values()
        )
        
        return {
            "success": successful_cleanups == total_processes,
            "message": f"清理完成: {successful_cleanups}/{total_processes}",
            "processes_found": total_processes,
            "cleanup_results": cleanup_results,
            "cleanup_time": time.time() - start_time
        }
    
    def _cleanup_single_process(self, process_info: ProcessInfo) -> Dict[str, Any]:
        """清理单个进程"""
        pid = process_info.pid
        process_type = process_info.process_type
        
        try:
            # 1. 检查进程是否仍然存在
            if not process_info.process_obj.is_running():
                return {
                    "pid": pid,
                    "type": process_type.value,
                    "success": True,
                    "message": "进程已停止"
                }
            
            # 2. 根据进程类型选择清理策略
            if process_type == ProcessType.MANAGED:
                return self._cleanup_managed_process(process_info)
            elif process_type == ProcessType.ORPHAN:
                return self._cleanup_orphan_process(process_info)
            elif process_type == ProcessType.CHILD:
                return self._cleanup_child_process(process_info)
            else:
                return {
                    "pid": pid,
                    "type": process_type.value,
                    "success": False,
                    "message": "未知进程类型"
                }
                
        except psutil.NoSuchProcess:
            return {
                "pid": pid,
                "type": process_type.value,
                "success": True,
                "message": "进程已不存在"
            }
        except Exception as e:
            self.logger.error(f"清理进程 {pid} 时发生错误: {e}")
            return {
                "pid": pid,
                "type": process_type.value,
                "success": False,
                "message": f"清理失败: {str(e)}"
            }
    
    def _cleanup_managed_process(self, process_info: ProcessInfo) -> Dict[str, Any]:
        """清理托管进程"""
        # 使用进程句柄进行优雅停止
        if process_info.managed_handle:
            return self._graceful_stop_with_handle(process_info)
        else:
            return self._force_stop_process(process_info)
    
    def _cleanup_orphan_process(self, process_info: ProcessInfo) -> Dict[str, Any]:
        """清理孤儿进程"""
        # 孤儿进程直接使用psutil进行停止
        return self._force_stop_process(process_info)
    
    def _cleanup_child_process(self, process_info: ProcessInfo) -> Dict[str, Any]:
        """清理子进程"""
        # 子进程通常随父进程一起停止，但也可以单独清理
        return self._force_stop_process(process_info)
    
    def _graceful_stop_with_handle(self, process_info: ProcessInfo) -> Dict[str, Any]:
        """使用进程句柄优雅停止"""
        pid = process_info.pid
        handle = process_info.managed_handle
        
        try:
            # 1. 优雅停止
            handle.terminate()
            
            # 2. 等待停止
            try:
                handle.wait(timeout=10)
                return {
                    "pid": pid,
                    "type": process_info.process_type.value,
                    "success": True,
                    "message": "优雅停止成功"
                }
            except subprocess.TimeoutExpired:
                # 3. 强制停止
                handle.kill()
                handle.wait()
                return {
                    "pid": pid,
                    "type": process_info.process_type.value,
                    "success": True,
                    "message": "强制停止成功"
                }
                
        except Exception as e:
            return {
                "pid": pid,
                "type": process_info.process_type.value,
                "success": False,
                "message": f"停止失败: {str(e)}"
            }
    
    def _force_stop_process(self, process_info: ProcessInfo) -> Dict[str, Any]:
        """强制停止进程"""
        pid = process_info.pid
        process_obj = process_info.process_obj
        
        try:
            # 1. 尝试优雅停止
            process_obj.terminate()
            
            # 2. 等待停止
            try:
                process_obj.wait(timeout=5)
                return {
                    "pid": pid,
                    "type": process_info.process_type.value,
                    "success": True,
                    "message": "优雅停止成功"
                }
            except psutil.TimeoutExpired:
                # 3. 强制停止
                process_obj.kill()
                process_obj.wait(timeout=3)
                return {
                    "pid": pid,
                    "type": process_info.process_type.value,
                    "success": True,
                    "message": "强制停止成功"
                }
                
        except psutil.NoSuchProcess:
            return {
                "pid": pid,
                "type": process_info.process_type.value,
                "success": True,
                "message": "进程已不存在"
            }
        except Exception as e:
            return {
                "pid": pid,
                "type": process_info.process_type.value,
                "success": False,
                "message": f"停止失败: {str(e)}"
            }

class ResourceType(Enum):
    """资源类型枚举"""
    FILE_DESCRIPTOR = "file_descriptor"
    TEMP_FILE = "temp_file"
    MEMORY_BUFFER = "memory_buffer"
    THREAD_RESOURCE = "thread_resource"
    NETWORK_CONNECTION = "network_connection"

@dataclass
class ResourceInfo:
    """资源信息"""
    resource_id: str
    resource_type: ResourceType
    resource_obj: Any
    created_time: float
    metadata: Dict[str, Any] = None

class ResourceCleaner(ABC):
    """资源清理器抽象基类"""

    @abstractmethod
    def cleanup(self, resource_info: ResourceInfo) -> bool:
        """清理资源"""
        pass

    @abstractmethod
    def can_handle(self, resource_type: ResourceType) -> bool:
        """检查是否能处理指定类型的资源"""
        pass

class FileDescriptorCleaner(ResourceCleaner):
    """文件描述符清理器"""

    def can_handle(self, resource_type: ResourceType) -> bool:
        return resource_type == ResourceType.FILE_DESCRIPTOR

    def cleanup(self, resource_info: ResourceInfo) -> bool:
        """清理文件描述符"""
        try:
            fd_obj = resource_info.resource_obj

            # 处理不同类型的文件对象
            if hasattr(fd_obj, 'close') and callable(fd_obj.close):
                if not getattr(fd_obj, 'closed', False):
                    fd_obj.close()
                    return True
                else:
                    return True  # 已经关闭

            # 处理原始文件描述符
            if isinstance(fd_obj, int):
                try:
                    os.close(fd_obj)
                    return True
                except OSError:
                    # 文件描述符可能已经关闭
                    return True

            return False

        except Exception as e:
            logging.getLogger(__name__).error(f"清理文件描述符失败: {e}")
            return False

class TempFileCleaner(ResourceCleaner):
    """临时文件清理器"""

    def can_handle(self, resource_type: ResourceType) -> bool:
        return resource_type == ResourceType.TEMP_FILE

    def cleanup(self, resource_info: ResourceInfo) -> bool:
        """清理临时文件"""
        try:
            file_path = resource_info.resource_obj

            if isinstance(file_path, (str, Path)):
                path_obj = Path(file_path)
                if path_obj.exists():
                    if path_obj.is_file():
                        path_obj.unlink()
                    elif path_obj.is_dir():
                        import shutil
                        shutil.rmtree(path_obj)
                    return True

            return False

        except Exception as e:
            logging.getLogger(__name__).error(f"清理临时文件失败: {e}")
            return False

class MemoryBufferCleaner(ResourceCleaner):
    """内存缓冲区清理器"""

    def can_handle(self, resource_type: ResourceType) -> bool:
        return resource_type == ResourceType.MEMORY_BUFFER

    def cleanup(self, resource_info: ResourceInfo) -> bool:
        """清理内存缓冲区"""
        try:
            buffer_obj = resource_info.resource_obj

            # 清理不同类型的缓冲区
            if hasattr(buffer_obj, 'clear'):
                buffer_obj.clear()
                return True
            elif isinstance(buffer_obj, list):
                buffer_obj.clear()
                return True
            elif isinstance(buffer_obj, dict):
                buffer_obj.clear()
                return True

            # 对于其他对象，尝试删除引用
            del buffer_obj
            return True

        except Exception as e:
            logging.getLogger(__name__).error(f"清理内存缓冲区失败: {e}")
            return False

class ResourceManager:
    """资源管理器"""

    def __init__(self):
        self.resources: Dict[str, ResourceInfo] = {}
        self.cleaners: List[ResourceCleaner] = [
            FileDescriptorCleaner(),
            TempFileCleaner(),
            MemoryBufferCleaner()
        ]
        self.lock = threading.RLock()
        self.logger = logging.getLogger(__name__)

    def register_resource(self, resource_id: str, resource_type: ResourceType,
                         resource_obj: Any, metadata: Dict[str, Any] = None) -> bool:
        """注册资源"""
        try:
            with self.lock:
                resource_info = ResourceInfo(
                    resource_id=resource_id,
                    resource_type=resource_type,
                    resource_obj=resource_obj,
                    created_time=time.time(),
                    metadata=metadata or {}
                )
                self.resources[resource_id] = resource_info
                self.logger.debug(f"注册资源: {resource_id} ({resource_type.value})")
                return True

        except Exception as e:
            self.logger.error(f"注册资源失败: {e}")
            return False

    def unregister_resource(self, resource_id: str) -> bool:
        """注销资源"""
        try:
            with self.lock:
                if resource_id in self.resources:
                    del self.resources[resource_id]
                    self.logger.debug(f"注销资源: {resource_id}")
                    return True
                return False

        except Exception as e:
            self.logger.error(f"注销资源失败: {e}")
            return False

    def cleanup_all_resources(self) -> Dict[str, Any]:
        """清理所有资源"""
        start_time = time.time()
        cleanup_results = {}

        try:
            with self.lock:
                total_resources = len(self.resources)
                successful_cleanups = 0

                for resource_id, resource_info in list(self.resources.items()):
                    result = self._cleanup_single_resource(resource_info)
                    cleanup_results[resource_id] = result

                    if result['success']:
                        successful_cleanups += 1
                        # 清理成功后从注册表中移除
                        del self.resources[resource_id]

                return {
                    "success": successful_cleanups == total_resources,
                    "total_resources": total_resources,
                    "successful_cleanups": successful_cleanups,
                    "cleanup_results": cleanup_results,
                    "cleanup_time": time.time() - start_time
                }

        except Exception as e:
            self.logger.error(f"清理所有资源时发生错误: {e}")
            return {
                "success": False,
                "error": str(e),
                "cleanup_results": cleanup_results
            }

    def _cleanup_single_resource(self, resource_info: ResourceInfo) -> Dict[str, Any]:
        """清理单个资源"""
        resource_id = resource_info.resource_id
        resource_type = resource_info.resource_type

        try:
            # 找到合适的清理器
            cleaner = self._find_cleaner(resource_type)
            if not cleaner:
                return {
                    "success": False,
                    "message": f"未找到适合的清理器: {resource_type.value}"
                }

            # 执行清理
            success = cleaner.cleanup(resource_info)

            return {
                "success": success,
                "resource_type": resource_type.value,
                "message": "清理成功" if success else "清理失败"
            }

        except Exception as e:
            self.logger.error(f"清理资源 {resource_id} 时发生错误: {e}")
            return {
                "success": False,
                "resource_type": resource_type.value,
                "message": f"清理异常: {str(e)}"
            }

    def _find_cleaner(self, resource_type: ResourceType) -> Optional[ResourceCleaner]:
        """查找合适的资源清理器"""
        for cleaner in self.cleaners:
            if cleaner.can_handle(resource_type):
                return cleaner
        return None

    def get_resource_summary(self) -> Dict[str, Any]:
        """获取资源摘要"""
        with self.lock:
            type_counts = {}
            for resource_info in self.resources.values():
                resource_type = resource_info.resource_type.value
                type_counts[resource_type] = type_counts.get(resource_type, 0) + 1

            return {
                "total_resources": len(self.resources),
                "type_counts": type_counts,
                "resource_ids": list(self.resources.keys())
            }

class ProcessResourceTracker:
    """进程资源跟踪器"""

    def __init__(self):
        self.resource_manager = ResourceManager()
        self.logger = logging.getLogger(__name__)

    def track_process_resources(self, process: subprocess.Popen) -> None:
        """跟踪进程资源"""
        try:
            # 1. 跟踪文件描述符
            if process.stdout:
                self.resource_manager.register_resource(
                    f"stdout_{process.pid}",
                    ResourceType.FILE_DESCRIPTOR,
                    process.stdout,
                    {"process_pid": process.pid, "fd_type": "stdout"}
                )

            if process.stderr:
                self.resource_manager.register_resource(
                    f"stderr_{process.pid}",
                    ResourceType.FILE_DESCRIPTOR,
                    process.stderr,
                    {"process_pid": process.pid, "fd_type": "stderr"}
                )

            if process.stdin:
                self.resource_manager.register_resource(
                    f"stdin_{process.pid}",
                    ResourceType.FILE_DESCRIPTOR,
                    process.stdin,
                    {"process_pid": process.pid, "fd_type": "stdin"}
                )

            # 2. 跟踪临时文件
            self._track_temp_files(process.pid)

        except Exception as e:
            self.logger.error(f"跟踪进程资源失败: {e}")

    def _track_temp_files(self, pid: int) -> None:
        """跟踪临时文件"""
        try:
            # 查找可能的临时文件路径
            temp_patterns = [
                str(PROJECT_ROOT / "logs" / "*.tmp"),
                str(PROJECT_ROOT / "data" / "runtime" / "*"),
            ]

            for pattern in temp_patterns:
                for temp_file in glob.glob(pattern):
                    self.resource_manager.register_resource(
                        f"temp_file_{temp_file}",
                        ResourceType.TEMP_FILE,
                        temp_file,
                        {"process_pid": pid, "pattern": pattern}
                    )

        except Exception as e:
            self.logger.debug(f"跟踪临时文件时发生错误: {e}")

    def cleanup_process_resources(self, pid: int) -> Dict[str, Any]:
        """清理指定进程的资源"""
        try:
            # 获取该进程相关的所有资源
            process_resources = {}

            with self.resource_manager.lock:
                for resource_id, resource_info in self.resource_manager.resources.items():
                    metadata = resource_info.metadata or {}
                    if metadata.get("process_pid") == pid:
                        process_resources[resource_id] = resource_info

            # 清理这些资源
            cleanup_results = {}
            successful_cleanups = 0

            for resource_id, resource_info in process_resources.items():
                result = self.resource_manager._cleanup_single_resource(resource_info)
                cleanup_results[resource_id] = result

                if result['success']:
                    successful_cleanups += 1
                    self.resource_manager.unregister_resource(resource_id)

            return {
                "success": successful_cleanups == len(process_resources),
                "process_pid": pid,
                "total_resources": len(process_resources),
                "successful_cleanups": successful_cleanups,
                "cleanup_results": cleanup_results
            }

        except Exception as e:
            self.logger.error(f"清理进程 {pid} 资源时发生错误: {e}")
            return {
                "success": False,
                "process_pid": pid,
                "error": str(e)
            }

class VerificationLevel(Enum):
    """验证级别"""
    BASIC = "basic"           # 基础验证
    STANDARD = "standard"     # 标准验证
    COMPREHENSIVE = "comprehensive"  # 全面验证

class VerificationResult:
    """验证结果"""

    def __init__(self, check_name: str, success: bool, message: str,
                 details: Dict[str, Any] = None, severity: str = "info"):
        self.check_name = check_name
        self.success = success
        self.message = message
        self.details = details or {}
        self.severity = severity  # info, warning, error, critical
        self.timestamp = time.time()

class StopVerifier(ABC):
    """停止验证器抽象基类"""

    @abstractmethod
    def verify(self) -> VerificationResult:
        """执行验证"""
        pass

    @abstractmethod
    def get_verification_level(self) -> VerificationLevel:
        """获取验证级别"""
        pass

class ProcessStopVerifier(StopVerifier):
    """进程停止验证器"""

    def __init__(self, process_detector: ProcessDetector):
        self.process_detector = process_detector
        self.logger = logging.getLogger(__name__)

    def get_verification_level(self) -> VerificationLevel:
        return VerificationLevel.BASIC

    def verify(self) -> VerificationResult:
        """验证所有进程是否已停止"""
        try:
            # 检测剩余进程
            remaining_processes = self.process_detector.detect_processes()

            if not remaining_processes:
                return VerificationResult(
                    check_name="process_stop_check",
                    success=True,
                    message="所有进程已成功停止",
                    details={"remaining_processes": 0}
                )
            else:
                process_details = [
                    {
                        "pid": p.pid,
                        "type": p.process_type.value,
                        "cmdline": " ".join(p.cmdline)
                    }
                    for p in remaining_processes
                ]

                return VerificationResult(
                    check_name="process_stop_check",
                    success=False,
                    message=f"发现 {len(remaining_processes)} 个未停止的进程",
                    details={
                        "remaining_processes": len(remaining_processes),
                        "process_details": process_details
                    },
                    severity="error"
                )

        except Exception as e:
            return VerificationResult(
                check_name="process_stop_check",
                success=False,
                message=f"进程停止验证失败: {str(e)}",
                severity="critical"
            )

class ResourceCleanupVerifier(StopVerifier):
    """资源清理验证器"""

    def __init__(self, resource_manager: ResourceManager):
        self.resource_manager = resource_manager
        self.logger = logging.getLogger(__name__)

    def get_verification_level(self) -> VerificationLevel:
        return VerificationLevel.STANDARD

    def verify(self) -> VerificationResult:
        """验证资源是否已清理"""
        try:
            resource_summary = self.resource_manager.get_resource_summary()
            remaining_resources = resource_summary["total_resources"]

            if remaining_resources == 0:
                return VerificationResult(
                    check_name="resource_cleanup_check",
                    success=True,
                    message="所有资源已成功清理",
                    details=resource_summary
                )
            else:
                return VerificationResult(
                    check_name="resource_cleanup_check",
                    success=False,
                    message=f"发现 {remaining_resources} 个未清理的资源",
                    details=resource_summary,
                    severity="warning"
                )

        except Exception as e:
            return VerificationResult(
                check_name="resource_cleanup_check",
                success=False,
                message=f"资源清理验证失败: {str(e)}",
                severity="critical"
            )

class MonitoringStopVerifier(StopVerifier):
    """监控停止验证器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def get_verification_level(self) -> VerificationLevel:
        return VerificationLevel.BASIC

    def verify(self) -> VerificationResult:
        """验证监控线程是否已停止"""
        try:
            # 导入全局变量（需要在web_manager.py中定义）
            try:
                from web_manager import is_monitoring, process_monitor_thread, log_monitor_thread
            except ImportError:
                # 如果无法导入，返回跳过验证的结果
                return VerificationResult(
                    check_name="monitoring_stop_check",
                    success=True,
                    message="监控线程验证跳过（无法访问全局变量）",
                    severity="info"
                )

            issues = []

            # 检查监控标志
            if is_monitoring:
                issues.append("监控标志仍为True")

            # 检查监控线程状态
            if process_monitor_thread and process_monitor_thread.is_alive():
                issues.append("进程监控线程仍在运行")

            if log_monitor_thread and log_monitor_thread.is_alive():
                issues.append("日志监控线程仍在运行")

            if not issues:
                return VerificationResult(
                    check_name="monitoring_stop_check",
                    success=True,
                    message="所有监控线程已成功停止",
                    details={
                        "is_monitoring": is_monitoring,
                        "process_monitor_alive": process_monitor_thread.is_alive() if process_monitor_thread else False,
                        "log_monitor_alive": log_monitor_thread.is_alive() if log_monitor_thread else False
                    }
                )
            else:
                return VerificationResult(
                    check_name="monitoring_stop_check",
                    success=False,
                    message=f"监控停止验证失败: {'; '.join(issues)}",
                    details={"issues": issues},
                    severity="warning"
                )

        except Exception as e:
            return VerificationResult(
                check_name="monitoring_stop_check",
                success=False,
                message=f"监控停止验证异常: {str(e)}",
                severity="critical"
            )

class StopVerificationManager:
    """停止验证管理器"""

    def __init__(self, process_detector: ProcessDetector, resource_manager: ResourceManager):
        self.verifiers = [
            ProcessStopVerifier(process_detector),
            ResourceCleanupVerifier(resource_manager),
            MonitoringStopVerifier()
        ]
        self.logger = logging.getLogger(__name__)

    def verify_stop_operation(self, verification_level: VerificationLevel = VerificationLevel.STANDARD) -> Dict[str, Any]:
        """执行停止操作验证"""
        start_time = time.time()
        verification_results = []

        try:
            # 根据验证级别筛选验证器
            active_verifiers = [
                v for v in self.verifiers
                if self._should_run_verifier(v, verification_level)
            ]

            # 执行验证
            for verifier in active_verifiers:
                try:
                    result = verifier.verify()
                    verification_results.append(result)
                except Exception as e:
                    error_result = VerificationResult(
                        check_name=f"{verifier.__class__.__name__}_error",
                        success=False,
                        message=f"验证器执行异常: {str(e)}",
                        severity="critical"
                    )
                    verification_results.append(error_result)

            # 统计结果
            total_checks = len(verification_results)
            successful_checks = sum(1 for r in verification_results if r.success)
            critical_failures = [r for r in verification_results if not r.success and r.severity == "critical"]

            overall_success = successful_checks == total_checks and len(critical_failures) == 0

            return {
                "success": overall_success,
                "verification_level": verification_level.value,
                "total_checks": total_checks,
                "successful_checks": successful_checks,
                "critical_failures": len(critical_failures),
                "verification_results": [
                    {
                        "check_name": r.check_name,
                        "success": r.success,
                        "message": r.message,
                        "severity": r.severity,
                        "details": r.details,
                        "timestamp": r.timestamp
                    }
                    for r in verification_results
                ],
                "verification_time": time.time() - start_time
            }

        except Exception as e:
            self.logger.error(f"停止验证管理器执行失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "verification_results": verification_results
            }

    def _should_run_verifier(self, verifier: StopVerifier, target_level: VerificationLevel) -> bool:
        """判断是否应该运行指定的验证器"""
        verifier_level = verifier.get_verification_level()
        level_order = {
            VerificationLevel.BASIC: 1,
            VerificationLevel.STANDARD: 2,
            VerificationLevel.COMPREHENSIVE: 3
        }
        return level_order[verifier_level] <= level_order[target_level]

class EnhancedProcessManager:
    """增强的进程管理器"""

    def __init__(self):
        self.process = None
        self.process_id = None
        self.process_detector = None
        self.process_cleaner = None
        self.resource_tracker = ProcessResourceTracker()
        self.verification_manager = None
        self.logger = logging.getLogger(__name__)

        # 初始化组件
        self._initialize_components()

    def _initialize_components(self):
        """初始化组件"""
        self.process_detector = TradingProcessDetector(managed_process=self.process)
        self.process_cleaner = ProcessCleaner(self.process_detector)
        self.verification_manager = StopVerificationManager(
            self.process_detector,
            self.resource_tracker.resource_manager
        )

    def start_trading(self) -> Dict[str, Any]:
        """启动交易程序（保持原有接口）"""
        try:
            if self.is_running():
                return {"success": False, "message": "交易程序已在运行中"}

            # 启动交易程序
            startup_script = PROJECT_ROOT / "startup.py"
            if not startup_script.exists():
                return {"success": False, "message": "找不到启动脚本文件"}

            # 使用当前Python解释器启动，添加-u参数确保输出不被缓冲
            cmd = [sys.executable, "-u", str(startup_script)]

            # 创建日志目录
            logs_dir = PROJECT_ROOT / "logs"
            logs_dir.mkdir(exist_ok=True)

            self.process = subprocess.Popen(
                cmd,
                cwd=str(PROJECT_ROOT),
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            self.process_id = self.process.pid

            # 注意：日志监控需要在web_manager.py中的API路由中启动
            # 这里不直接启动日志监控，避免循环导入

            # 跟踪进程资源
            self.resource_tracker.track_process_resources(self.process)

            # 更新检测器的托管进程
            self.process_detector.managed_process = self.process

            return {
                "success": True,
                "message": f"交易程序启动成功，进程ID: {self.process_id}",
                "pid": self.process_id
            }

        except Exception as e:
            return {"success": False, "message": f"启动失败: {str(e)}"}

    def start_schedule_debug(self) -> Dict[str, Any]:
        """启动调度调试"""
        try:
            if self.is_running():
                return {"success": False, "message": "已有程序在运行中，请先停止"}

            # 启动调度调试程序
            schedule_script = PROJECT_ROOT / "schedule_startup.py"
            if not schedule_script.exists():
                return {"success": False, "message": "找不到调度脚本文件"}

            # 使用当前Python解释器启动，添加-u参数确保输出不被缓冲
            cmd = [sys.executable, "-u", str(schedule_script)]

            # 创建日志目录
            logs_dir = PROJECT_ROOT / "logs"
            logs_dir.mkdir(exist_ok=True)

            self.process = subprocess.Popen(
                cmd,
                cwd=str(PROJECT_ROOT),
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,  # 将stderr重定向到stdout
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            self.process_id = self.process.pid

            # 跟踪进程资源
            self.resource_tracker.track_process_resources(self.process)

            # 更新检测器的托管进程
            self.process_detector.managed_process = self.process

            return {
                "success": True,
                "message": f"调度调试启动成功，进程ID: {self.process_id}",
                "pid": self.process_id
            }

        except Exception as e:
            return {"success": False, "message": f"启动调度调试失败: {str(e)}"}

    def start_schedule_debug_test(self) -> Dict[str, Any]:
        """启动调度调试测试"""
        try:
            if self.is_running():
                return {"success": False, "message": "已有程序在运行中，请先停止"}

            # 启动调度调试测试程序
            test_script = PROJECT_ROOT / "schedule_startup_test.py"
            if not test_script.exists():
                return {"success": False, "message": "找不到测试脚本文件"}

            # 使用当前Python解释器启动，添加-u参数确保输出不被缓冲
            cmd = [sys.executable, "-u", str(test_script)]

            # 创建日志目录
            logs_dir = PROJECT_ROOT / "logs"
            logs_dir.mkdir(exist_ok=True)

            self.process = subprocess.Popen(
                cmd,
                cwd=str(PROJECT_ROOT),
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,  # 将stderr重定向到stdout
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            self.process_id = self.process.pid

            # 跟踪进程资源
            self.resource_tracker.track_process_resources(self.process)

            # 更新检测器的托管进程
            self.process_detector.managed_process = self.process

            return {
                "success": True,
                "message": f"调度调试测试启动成功，进程ID: {self.process_id}",
                "pid": self.process_id
            }

        except Exception as e:
            return {"success": False, "message": f"启动调度调试测试失败: {str(e)}"}

    def stop_trading_enhanced(self) -> Dict[str, Any]:
        """增强的停止交易程序方法"""
        try:
            self.logger.info("开始增强停止流程")
            stop_start_time = time.time()

            # 1. 使用进程清理器清理所有进程
            cleanup_result = self.process_cleaner.cleanup_all_processes()

            # 2. 清理本地资源
            resource_cleanup_result = self._cleanup_local_resources()

            # 3. 验证停止结果
            verification_result = self.verification_manager.verify_stop_operation(
                VerificationLevel.STANDARD
            )

            # 4. 统计总体结果
            overall_success = (
                cleanup_result["success"] and
                resource_cleanup_result["success"] and
                verification_result["success"]
            )

            stop_duration = time.time() - stop_start_time

            result = {
                "success": overall_success,
                "message": "增强停止流程完成" if overall_success else "停止流程存在问题",
                "stop_duration": stop_duration,
                "cleanup_result": cleanup_result,
                "resource_cleanup_result": resource_cleanup_result,
                "verification_result": verification_result
            }

            # 5. 记录详细日志
            self._log_stop_result(result)

            return result

        except Exception as e:
            self.logger.error(f"增强停止流程失败: {e}")
            self.logger.debug(traceback.format_exc())
            return {
                "success": False,
                "message": f"停止失败: {str(e)}"
            }

    def _cleanup_local_resources(self) -> Dict[str, Any]:
        """清理本地资源"""
        try:
            cleanup_results = []

            # 1. 清理进程句柄
            if self.process:
                # 关闭文件描述符
                if hasattr(self.process, 'stdout') and self.process.stdout:
                    self.process.stdout.close()
                if hasattr(self.process, 'stderr') and self.process.stderr:
                    self.process.stderr.close()
                if hasattr(self.process, 'stdin') and self.process.stdin:
                    self.process.stdin.close()

                self.process = None
                cleanup_results.append("进程句柄已清理")

            # 2. 重置进程ID
            if self.process_id:
                old_pid = self.process_id
                self.process_id = None
                cleanup_results.append(f"进程ID {old_pid} 已重置")

            # 3. 清理资源跟踪器中的资源
            resource_result = self.resource_tracker.resource_manager.cleanup_all_resources()
            cleanup_results.append(f"资源管理器清理: {resource_result['successful_cleanups']}/{resource_result['total_resources']}")

            # 4. 重置内部状态
            self._reset_internal_state()
            cleanup_results.append("内部状态已重置")

            return {
                "success": True,
                "message": "本地资源清理完成",
                "cleanup_details": cleanup_results
            }

        except Exception as e:
            self.logger.error(f"清理本地资源时发生错误: {e}")
            return {
                "success": False,
                "message": f"本地资源清理失败: {str(e)}"
            }

    def _reset_internal_state(self):
        """重置内部状态"""
        # 重新初始化组件
        self._initialize_components()

    def _log_stop_result(self, result: Dict[str, Any]):
        """记录停止结果的详细日志"""
        self.logger.info("=" * 50)
        self.logger.info("增强停止流程结果")
        self.logger.info(f"总体成功: {result['success']}")
        self.logger.info(f"停止耗时: {result['stop_duration']:.2f}秒")

        # 记录进程清理结果
        cleanup_result = result.get('cleanup_result', {})
        self.logger.info(f"进程清理: {cleanup_result.get('processes_found', 0)} 个进程，成功: {cleanup_result.get('success', False)}")

        # 记录资源清理结果
        resource_result = result.get('resource_cleanup_result', {})
        self.logger.info(f"资源清理: 成功: {resource_result.get('success', False)}")

        # 记录验证结果
        verification_result = result.get('verification_result', {})
        self.logger.info(f"停止验证: {verification_result.get('successful_checks', 0)}/{verification_result.get('total_checks', 0)} 通过")

        self.logger.info("=" * 50)

    def is_running(self) -> bool:
        """检查交易程序是否在运行（保持原有逻辑）"""
        if self.process:
            return self.process.poll() is None

        # 检查是否有startup.py或schedule_startup.py进程在运行
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = proc.info['cmdline']
                if cmdline:
                    cmdline_str = ' '.join(cmdline)
                    if 'startup.py' in cmdline_str or 'schedule_startup.py' in cmdline_str:
                        self.process_id = proc.info['pid']
                        return True
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

        return False

    def get_status(self) -> Dict[str, Any]:
        """获取程序状态（保持原有接口）"""
        is_running = self.is_running()

        status = {
            "is_running": is_running,
            "pid": self.process_id if is_running else None,
            "status_text": "运行中" if is_running else "已停止",
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }

        if is_running and self.process_id:
            try:
                proc = psutil.Process(self.process_id)
                status.update({
                    "cpu_percent": proc.cpu_percent(),
                    "memory_mb": proc.memory_info().rss / 1024 / 1024,
                    "start_time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(proc.create_time()))
                })
            except psutil.NoSuchProcess:
                status["is_running"] = False
                status["status_text"] = "进程不存在"

        return status
