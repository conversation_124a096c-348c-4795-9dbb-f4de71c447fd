#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
量化交易系统认证管理模块
提供用户认证、会话管理、Google Authenticator等功能
"""

import os
import sys
import hashlib
import uuid
import time
import json
import secrets
import base64
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import importlib.util
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

try:
    import pyotp
    import qrcode
    from PIL import Image
    import io
except ImportError as e:
    print(f"警告: 缺少认证相关依赖包: {e}")
    print("请运行: pip install pyotp qrcode[pil] Pillow")


class UserManager:
    """用户管理类"""
    
    def __init__(self):
        self.config_file = PROJECT_ROOT / "users_config.py"
        self.users_data = None
        self.auth_config = None
        self.security_config = None
        self._load_config()
    
    def _load_config(self):
        """加载用户配置"""
        try:
            # 动态导入用户配置
            spec = importlib.util.spec_from_file_location("users_config", self.config_file)
            users_config = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(users_config)
            
            self.users_data = users_config.user_list
            self.auth_config = users_config.AUTH_CONFIG
            self.security_config = users_config.SECURITY_CONFIG
            
        except Exception as e:
            print(f"加载用户配置失败: {e}")
            self.users_data = []
            self.auth_config = {}
            self.security_config = {}
    
    def _save_config(self):
        """保存用户配置到文件"""
        try:
            # 创建备份
            import shutil
            backup_file = str(self.config_file) + '.backup'
            shutil.copy2(self.config_file, backup_file)

            # 重新生成完整的配置文件内容
            config_content = self._generate_config_content()

            # 写入文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                f.write(config_content)

        except Exception as e:
            print(f"保存用户配置失败: {e}")
            # 尝试恢复备份
            try:
                backup_file = str(self.config_file) + '.backup'
                if Path(backup_file).exists():
                    shutil.copy2(backup_file, self.config_file)
                    print("已恢复配置文件备份")
            except:
                pass
            return False
        return True

    def _generate_config_content(self):
        """生成配置文件内容"""

        # 格式化用户列表
        users_lines = []
        users_lines.append("user_list = [")

        for i, user in enumerate(self.users_data):
            users_lines.append("    {")
            users_lines.append("        # 基础用户信息")
            users_lines.append(f"        'user_name': {repr(user.get('user_name', ''))},")
            users_lines.append(f"        'password': {repr(user.get('password', ''))},")
            users_lines.append(f"        'role': {repr(user.get('role', ''))},")
            users_lines.append("")
            users_lines.append("        # Google Authenticator双因素认证")
            users_lines.append(f"        'google_secret_key': {repr(user.get('google_secret_key', ''))},")
            users_lines.append(f"        'is_google_auth': {repr(user.get('is_google_auth', 'N'))},")
            users_lines.append(f"        'google_backup_codes': {user.get('google_backup_codes', [])},")
            users_lines.append(f"        'force_google_setup': {user.get('force_google_setup', True)},")
            users_lines.append("")
            users_lines.append("        # 会话管理")
            users_lines.append(f"        'session_expire_list': {user.get('session_expire_list', [])},")
            users_lines.append("")
            users_lines.append("        # 安全设置")
            users_lines.append(f"        'failed_login_attempts': {user.get('failed_login_attempts', 0)},")
            users_lines.append(f"        'locked_until': {repr(user.get('locked_until'))},")
            users_lines.append(f"        'last_login_time': {repr(user.get('last_login_time', ''))},")
            users_lines.append(f"        'last_login_ip': {repr(user.get('last_login_ip', ''))},")
            users_lines.append(f"        'password_changed_time': {repr(user.get('password_changed_time', ''))},")
            users_lines.append("")
            users_lines.append("        # 登录历史记录")
            users_lines.append(f"        'login_history': {user.get('login_history', [])},")
            users_lines.append("")
            users_lines.append("        # 用户偏好设置")
            prefs = user.get('preferences', {})
            users_lines.append("        'preferences': {")
            for key, value in prefs.items():
                users_lines.append(f"            {repr(key)}: {repr(value)},")
            users_lines.append("        },")
            users_lines.append("")
            users_lines.append("        # 权限设置")
            perms = user.get('permissions', {})
            users_lines.append("        'permissions': {")
            for key, value in perms.items():
                users_lines.append(f"            {repr(key)}: {value},")
            users_lines.append("        }")

            if i < len(self.users_data) - 1:
                users_lines.append("    },")
            else:
                users_lines.append("    }")

        users_lines.append("]")

        # 生成AUTH_CONFIG的Python格式字符串
        auth_config_lines = self._format_python_dict(self.auth_config, "AUTH_CONFIG")

        # 生成SECURITY_CONFIG的Python格式字符串
        security_config_lines = self._format_python_dict(self.security_config, "SECURITY_CONFIG")

        # 生成完整内容
        content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
量化交易系统用户配置文件
包含用户认证、权限管理、会话管理等功能
"""

''' + '\n'.join(users_lines) + '''

# 认证系统配置
''' + '\n'.join(auth_config_lines) + '''

# 安全配置
''' + '\n'.join(security_config_lines)

        return content

    def _format_python_dict(self, data, var_name, indent=0):
        """将字典格式化为Python代码字符串"""
        lines = []
        indent_str = "    " * indent

        if indent == 0:
            lines.append(f"{var_name} = {{")
        else:
            lines.append(f"{indent_str}{{")

        for key, value in data.items():
            key_str = repr(key)

            if isinstance(value, dict):
                lines.append(f"{indent_str}    {key_str}: {{")
                for sub_key, sub_value in value.items():
                    sub_key_str = repr(sub_key)
                    sub_value_str = self._format_python_value(sub_value)
                    lines.append(f"{indent_str}        {sub_key_str}: {sub_value_str},")
                lines.append(f"{indent_str}    }},")
            else:
                value_str = self._format_python_value(value)
                lines.append(f"{indent_str}    {key_str}: {value_str},")

        if indent == 0:
            lines.append("}")
        else:
            lines.append(f"{indent_str}}}")

        return lines

    def _format_python_value(self, value):
        """将值格式化为Python代码字符串"""
        if isinstance(value, bool):
            return "True" if value else "False"
        elif isinstance(value, (int, float)):
            return str(value)
        elif isinstance(value, str):
            return repr(value)
        elif isinstance(value, list):
            if not value:
                return "[]"
            else:
                # 格式化列表
                items = [self._format_python_value(item) for item in value]
                return "[" + ", ".join(items) + "]"
        elif isinstance(value, dict):
            if not value:
                return "{}"
            else:
                # 格式化字典
                items = [f"{repr(k)}: {self._format_python_value(v)}" for k, v in value.items()]
                return "{" + ", ".join(items) + "}"
        elif value is None:
            return "None"
        else:
            return repr(value)
    
    def get_user(self, username: str) -> Optional[Dict]:
        """获取用户信息"""
        for user in self.users_data:
            if user.get('user_name') == username:
                return user
        return None
    
    def verify_password(self, username: str, password: str) -> bool:
        """验证用户密码"""
        user = self.get_user(username)
        if not user:
            return False
        
        # 计算密码的MD5哈希
        password_hash = hashlib.md5(password.encode('utf-8')).hexdigest()
        return user.get('password') == password_hash
    
    def is_account_locked(self, username: str) -> bool:
        """检查账户是否被锁定"""
        user = self.get_user(username)
        if not user:
            return False

        locked_until = user.get('locked_until')
        if not locked_until:
            return False

        try:
            lock_time = datetime.strptime(locked_until, '%Y-%m-%d %H:%M:%S')
            return datetime.now() < lock_time
        except:
            return False

    def get_lockout_remaining_time(self, username: str) -> int:
        """获取账户锁定剩余时间（分钟）"""
        user = self.get_user(username)
        if not user:
            return 0

        locked_until = user.get('locked_until')
        if not locked_until:
            return 0

        try:
            lock_time = datetime.strptime(locked_until, '%Y-%m-%d %H:%M:%S')
            remaining = lock_time - datetime.now()
            if remaining.total_seconds() > 0:
                return max(1, int(remaining.total_seconds() / 60))
            return 0
        except:
            return 0

    def get_remaining_login_attempts(self, username: str) -> int:
        """获取剩余登录尝试次数"""
        user = self.get_user(username)
        if not user:
            return 0

        max_attempts = self.auth_config.get('max_login_attempts', 5)
        failed_attempts = user.get('failed_login_attempts', 0)
        return max(0, max_attempts - failed_attempts)
    
    def record_login_attempt(self, username: str, success: bool, ip: str, user_agent: str, failure_reason: str = ''):
        """记录登录尝试"""
        user = self.get_user(username)
        if not user:
            return
        
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 记录登录历史
        login_record = {
            'login_time': current_time,
            'ip': ip,
            'user_agent': user_agent,
            'success': success,
            'failure_reason': failure_reason
        }
        
        if 'login_history' not in user:
            user['login_history'] = []
        
        user['login_history'].insert(0, login_record)
        
        # 限制历史记录数量
        max_history = self.auth_config.get('login_history_limit', 20)
        user['login_history'] = user['login_history'][:max_history]
        
        if success:
            # 登录成功，重置失败次数
            user['failed_login_attempts'] = 0
            user['locked_until'] = None
            user['last_login_time'] = current_time
            user['last_login_ip'] = ip
        else:
            # 登录失败，增加失败次数
            user['failed_login_attempts'] = user.get('failed_login_attempts', 0) + 1
            
            # 检查是否需要锁定账户
            max_attempts = self.auth_config.get('max_login_attempts', 5)
            if user['failed_login_attempts'] >= max_attempts:
                lockout_duration = self.auth_config.get('lockout_duration', 600)
                lock_until = datetime.now() + timedelta(seconds=lockout_duration)
                user['locked_until'] = lock_until.strftime('%Y-%m-%d %H:%M:%S')
        
        self._save_config()
    
    def change_password(self, username: str, new_password: str) -> bool:
        """修改用户密码"""
        user = self.get_user(username)
        if not user:
            return False
        
        # 验证密码长度
        min_length = self.auth_config.get('password_min_length', 6)
        if len(new_password) < min_length:
            return False
        
        # 更新密码
        password_hash = hashlib.md5(new_password.encode('utf-8')).hexdigest()
        user['password'] = password_hash
        user['password_changed_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        return self._save_config()
    
    def update_user_preferences(self, username: str, preferences: Dict) -> bool:
        """更新用户偏好设置"""
        user = self.get_user(username)
        if not user:
            return False
        
        if 'preferences' not in user:
            user['preferences'] = {}
        
        user['preferences'].update(preferences)
        return self._save_config()
    
    def get_user_permissions(self, username: str) -> Dict:
        """获取用户权限"""
        user = self.get_user(username)
        if not user:
            return {}
        
        return user.get('permissions', {})
    
    def has_permission(self, username: str, permission: str) -> bool:
        """检查用户是否有特定权限"""
        permissions = self.get_user_permissions(username)
        return permissions.get(permission, False)


class SessionManager:
    """会话管理类"""
    
    def __init__(self, user_manager: UserManager):
        self.user_manager = user_manager
    
    def create_session(self, username: str, ip: str, user_agent: str) -> str:
        """创建新会话"""
        user = self.user_manager.get_user(username)
        if not user:
            return None
        
        # 生成唯一的session ID
        session_id = str(uuid.uuid4())
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 计算过期时间
        session_lifetime = self.user_manager.auth_config.get('session_lifetime', 10800)  # 3小时
        expire_time = (datetime.now() + timedelta(seconds=session_lifetime)).strftime('%Y-%m-%d %H:%M:%S')
        
        # 生成设备指纹
        device_fingerprint = hashlib.md5(f"{ip}:{user_agent}".encode('utf-8')).hexdigest()
        
        # 创建session记录
        session_data = {
            'ip': ip,
            'expire_time': expire_time,
            'first_login_time': current_time,
            'session_id': session_id,
            'google_first_auth_time': '',  # 稍后设置
            'user_agent': user_agent,
            'last_activity': current_time,
            'device_fingerprint': device_fingerprint
        }
        
        # 添加到用户的session列表
        if 'session_expire_list' not in user:
            user['session_expire_list'] = []
        
        user['session_expire_list'].append(session_data)
        
        # 限制每用户的最大session数
        max_sessions = self.user_manager.auth_config.get('max_sessions_per_user', 3)
        if len(user['session_expire_list']) > max_sessions:
            # 删除最旧的session
            user['session_expire_list'] = sorted(
                user['session_expire_list'], 
                key=lambda x: x['first_login_time'], 
                reverse=True
            )[:max_sessions]
        
        self.user_manager._save_config()
        return session_id
    
    def validate_session(self, username: str, session_id: str, ip: str) -> bool:
        """验证会话有效性"""
        user = self.user_manager.get_user(username)
        if not user:
            return False
        
        sessions = user.get('session_expire_list', [])
        current_time = datetime.now()
        
        for session in sessions:
            if session.get('session_id') == session_id:
                # 检查IP是否匹配
                if session.get('ip') != ip:
                    return False
                
                # 检查是否过期
                try:
                    expire_time = datetime.strptime(session['expire_time'], '%Y-%m-%d %H:%M:%S')
                    if current_time > expire_time:
                        return False
                except:
                    return False
                
                # 更新最后活动时间
                session['last_activity'] = current_time.strftime('%Y-%m-%d %H:%M:%S')
                self.user_manager._save_config()
                return True
        
        return False
    
    def update_google_auth_time(self, username: str, session_id: str):
        """更新Google认证时间"""
        user = self.user_manager.get_user(username)
        if not user:
            return
        
        sessions = user.get('session_expire_list', [])
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        for session in sessions:
            if session.get('session_id') == session_id:
                session['google_first_auth_time'] = current_time
                break
        
        self.user_manager._save_config()
    
    def is_google_auth_required(self, username: str, session_id: str) -> bool:
        """检查是否需要Google认证"""
        user = self.user_manager.get_user(username)
        if not user:
            return True
        
        # 检查用户是否启用了Google认证
        if user.get('is_google_auth') != 'Y':
            return False
        
        sessions = user.get('session_expire_list', [])
        google_auth_window = self.user_manager.auth_config.get('google_auth_window', 600)  # 10分钟
        
        for session in sessions:
            if session.get('session_id') == session_id:
                google_auth_time = session.get('google_first_auth_time')
                if not google_auth_time:
                    return True
                
                try:
                    auth_time = datetime.strptime(google_auth_time, '%Y-%m-%d %H:%M:%S')
                    time_diff = (datetime.now() - auth_time).total_seconds()
                    return time_diff > google_auth_window
                except:
                    return True
        
        return True
    
    def cleanup_expired_sessions(self):
        """清理过期的会话"""
        current_time = datetime.now()
        
        for user in self.user_manager.users_data:
            sessions = user.get('session_expire_list', [])
            valid_sessions = []
            
            for session in sessions:
                try:
                    expire_time = datetime.strptime(session['expire_time'], '%Y-%m-%d %H:%M:%S')
                    if current_time <= expire_time:
                        valid_sessions.append(session)
                except:
                    continue  # 跳过无效的时间格式
            
            user['session_expire_list'] = valid_sessions
        
        self.user_manager._save_config()
    
    def revoke_session(self, username: str, session_id: str) -> bool:
        """撤销指定会话"""
        user = self.user_manager.get_user(username)
        if not user:
            return False
        
        sessions = user.get('session_expire_list', [])
        user['session_expire_list'] = [s for s in sessions if s.get('session_id') != session_id]
        
        return self.user_manager._save_config()
    
    def revoke_all_sessions(self, username: str) -> bool:
        """撤销用户的所有会话"""
        user = self.user_manager.get_user(username)
        if not user:
            return False
        
        user['session_expire_list'] = []
        return self.user_manager._save_config()


class GoogleAuthManager:
    """Google Authenticator管理类"""

    def __init__(self, user_manager: UserManager):
        self.user_manager = user_manager

    def generate_secret_key(self) -> str:
        """生成Google Authenticator密钥"""
        return pyotp.random_base32()

    def generate_qr_code(self, username: str, secret_key: str, issuer: str = "量化交易系统") -> str:
        """生成二维码数据URL"""
        try:
            # 创建TOTP对象
            totp = pyotp.TOTP(secret_key)

            # 生成provisioning URI
            provisioning_uri = totp.provisioning_uri(
                name=username,
                issuer_name=issuer
            )

            # 生成二维码
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(provisioning_uri)
            qr.make(fit=True)

            # 创建二维码图像
            img = qr.make_image(fill_color="black", back_color="white")

            # 转换为base64字符串
            buffer = io.BytesIO()
            img.save(buffer, format='PNG')
            img_str = base64.b64encode(buffer.getvalue()).decode()

            return f"data:image/png;base64,{img_str}"

        except Exception as e:
            print(f"生成二维码失败: {e}")
            return None

    def verify_totp_code(self, username: str, code: str) -> bool:
        """验证TOTP验证码"""
        user = self.user_manager.get_user(username)
        if not user:
            return False

        secret_key = user.get('google_secret_key')
        if not secret_key:
            return False

        try:
            totp = pyotp.TOTP(secret_key)
            return totp.verify(code, valid_window=1)  # 允许前后30秒的时间窗口
        except Exception as e:
            print(f"验证TOTP码失败: {e}")
            return False

    def setup_google_auth(self, username: str, secret_key: str) -> bool:
        """设置用户的Google认证"""
        user = self.user_manager.get_user(username)
        if not user:
            return False

        user['google_secret_key'] = secret_key
        user['is_google_auth'] = 'Y'
        user['force_google_setup'] = False

        # 生成备用码
        backup_codes = self.generate_backup_codes()
        user['google_backup_codes'] = backup_codes

        return self.user_manager._save_config()

    def generate_backup_codes(self) -> List[str]:
        """生成Google认证备用码"""
        backup_codes = []
        codes_count = self.user_manager.auth_config.get('backup_codes_count', 10)

        for _ in range(codes_count):
            # 生成8位数字备用码
            code = ''.join([str(secrets.randbelow(10)) for _ in range(8)])
            backup_codes.append(code)

        return backup_codes

    def verify_backup_code(self, username: str, code: str) -> bool:
        """验证备用码"""
        user = self.user_manager.get_user(username)
        if not user:
            return False

        backup_codes = user.get('google_backup_codes', [])
        if code in backup_codes:
            # 使用后删除备用码
            backup_codes.remove(code)
            user['google_backup_codes'] = backup_codes
            self.user_manager._save_config()
            return True

        return False

    def disable_google_auth(self, username: str) -> bool:
        """禁用Google认证"""
        user = self.user_manager.get_user(username)
        if not user:
            return False

        user['google_secret_key'] = ''
        user['is_google_auth'] = 'N'
        user['google_backup_codes'] = []
        user['force_google_setup'] = False

        return self.user_manager._save_config()

    def is_google_auth_enabled(self, username: str) -> bool:
        """检查用户是否启用了Google认证"""
        user = self.user_manager.get_user(username)
        if not user:
            return False

        return user.get('is_google_auth') == 'Y'

    def is_google_setup_required(self, username: str) -> bool:
        """检查是否需要强制设置Google认证"""
        user = self.user_manager.get_user(username)
        if not user:
            return False

        # 检查是否要求Google认证
        require_google = self.user_manager.auth_config.get('require_google_auth', True)
        if not require_google:
            return False

        # 检查是否已启用
        if self.is_google_auth_enabled(username):
            return False

        # 检查是否强制设置
        return user.get('force_google_setup', True)


class SecurityManager:
    """安全管理类"""

    def __init__(self, user_manager: UserManager):
        self.user_manager = user_manager
        self.rate_limit_data = {}  # 存储速率限制数据

    def check_rate_limit(self, ip: str) -> bool:
        """检查速率限制"""
        if not self.user_manager.security_config.get('rate_limiting', {}).get('enabled', True):
            return True

        current_time = time.time()
        max_requests = self.user_manager.security_config.get('rate_limiting', {}).get('max_requests', 100)
        window = self.user_manager.security_config.get('rate_limiting', {}).get('window', 60)

        if ip not in self.rate_limit_data:
            self.rate_limit_data[ip] = []

        # 清理过期的请求记录
        self.rate_limit_data[ip] = [
            req_time for req_time in self.rate_limit_data[ip]
            if current_time - req_time < window
        ]

        # 检查是否超过限制
        if len(self.rate_limit_data[ip]) >= max_requests:
            return False

        # 记录当前请求
        self.rate_limit_data[ip].append(current_time)
        return True

    def check_ip_whitelist(self, ip: str) -> bool:
        """检查IP白名单"""
        whitelist = self.user_manager.security_config.get('ip_whitelist', [])
        if not whitelist:  # 空白名单表示不限制
            return True

        return ip in whitelist

    def generate_csrf_token(self) -> str:
        """生成CSRF令牌"""
        return secrets.token_urlsafe(32)

    def validate_csrf_token(self, token: str, session_token: str) -> bool:
        """验证CSRF令牌"""
        return token == session_token

    def hash_password(self, password: str) -> str:
        """计算密码哈希"""
        return hashlib.md5(password.encode('utf-8')).hexdigest()

    def generate_device_fingerprint(self, ip: str, user_agent: str) -> str:
        """生成设备指纹"""
        return hashlib.md5(f"{ip}:{user_agent}".encode('utf-8')).hexdigest()


# 全局认证管理器实例
user_manager = UserManager()
session_manager = SessionManager(user_manager)
google_auth_manager = GoogleAuthManager(user_manager)
security_manager = SecurityManager(user_manager)
