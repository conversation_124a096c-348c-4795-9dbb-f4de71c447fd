# 量化交易系统 Web 管理界面

## 项目概述

本项目为现有的量化交易系统添加了一个功能完整的中文Web管理界面，实现了从命令行脚本操作到图形化Web界面的升级。通过这个界面，用户可以方便地控制交易程序、管理配置文件、监控系统状态等。

## 🚀 主要功能

### 1. 程序控制
- ✅ **一键启动/停止交易程序**: 通过Web界面按钮控制startup.py的运行
- ✅ **实时状态监控**: 显示程序运行状态、进程信息、资源使用情况
- ✅ **WebSocket实时更新**: 状态信息自动刷新，无需手动刷新页面

### 2. 配置管理
- ✅ **全局配置管理**: 管理系统全局参数（数据路径、调试模式、通知设置等）
- ✅ **账户配置管理**: 创建、编辑、删除交易账户配置文件
- ✅ **配置文件导入/导出**: 支持配置的备份和恢复
- ✅ **配置验证**: 自动检查配置的正确性

### 3. 系统监控
- ✅ **实时日志查看**: 在线查看系统运行日志，支持多个日志文件
- ✅ **系统信息显示**: 显示服务器硬件信息和资源使用情况
- ✅ **活动记录**: 记录重要操作的历史记录

## 📁 项目结构

```
├── web_manager.py              # 主Web服务器文件
├── start_web_manager.py        # 启动脚本（自动安装依赖）
├── requirements.txt            # Python依赖包列表
├── templates/                  # HTML模板文件
│   ├── base.html              # 基础模板（导航栏、通用功能）
│   ├── index.html             # 主页（程序控制面板）
│   └── config.html            # 配置管理页面
├── static/                     # 静态资源文件
│   └── css/
│       └── style.css          # 自定义样式文件
├── logs/                       # 日志文件目录
│   └── system.log             # 示例日志文件
├── accounts/                   # 账户配置文件目录
│   └── 测试账户.py             # 示例账户配置
├── WEB管理界面使用说明.md       # 详细使用说明
└── README_WEB管理界面.md       # 项目说明文件
```

## 🛠️ 技术架构

### 后端技术栈
- **Flask 3.0.0**: 轻量级Web框架
- **Flask-SocketIO 5.3.6**: WebSocket支持，实现实时通信
- **psutil 5.9.6**: 系统监控和进程管理
- **PyYAML 6.0.1**: YAML配置文件支持
- **python-dotenv 1.0.0**: 环境变量管理

### 前端技术栈
- **Bootstrap 5**: 响应式UI框架
- **jQuery**: JavaScript库
- **Socket.IO**: 客户端WebSocket库
- **Bootstrap Icons**: 图标库

### 核心组件
1. **ProcessManager**: 负责交易程序的启动、停止和状态监控
2. **ConfigManager**: 负责配置文件的读取、写入和管理
3. **WebSocket Handler**: 负责实时状态更新和通信

## 🚀 快速开始

### 方法一：使用启动脚本（推荐）

```bash
# 运行启动脚本，自动安装依赖并启动服务
python start_web_manager.py
```

### 方法二：手动安装

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 启动Web服务
python web_manager.py
```

### 访问界面

启动成功后，在浏览器中访问：
- 本地访问: http://localhost:5000
- 局域网访问: http://[服务器IP]:5000

## 📋 功能详解

### 1. 控制面板（主页）
- **程序控制区域**: 启动/停止按钮，状态显示
- **系统监控区域**: CPU、内存使用率，连接状态
- **快速操作区域**: 查看日志、配置管理等快捷入口
- **活动记录区域**: 显示最近的操作历史

### 2. 配置管理页面
- **全局配置**: 修改系统全局参数
- **账户管理**: 添加、编辑、删除交易账户
- **配置状态**: 显示配置文件状态和最后修改时间
- **快速操作**: 导入/导出配置，配置验证

### 3. 日志查看功能
- **日志文件列表**: 显示所有可用的日志文件
- **实时日志查看**: 显示最新的日志内容
- **日志刷新**: 手动刷新获取最新日志

## 🔧 配置说明

### 全局配置项
- **实盘数据路径**: 数据中心的数据存储路径
- **错误通知URL**: 企业微信机器人通知地址
- **调试模式**: 启用后不会实际下单
- **清理启动**: 启动时是否删除缓存
- **并行任务数**: 回测时的并行处理数量
- **因子列限制**: 内存优化参数

### 账户配置项
- **API密钥**: 交易所API Key和Secret Key
- **账户类型**: 普通账户或统一账户
- **小时偏移**: 时间偏移设置
- **杠杆倍数**: 交易杠杆设置
- **通知设置**: 企业微信机器人URL
- **手续费设置**: BNB抵扣手续费配置

## 🔒 安全注意事项

1. **网络安全**
   - 默认绑定到所有网络接口，生产环境建议限制访问
   - 考虑使用HTTPS和反向代理
   - 设置防火墙规则

2. **配置安全**
   - API密钥等敏感信息会保存在配置文件中
   - 建议定期备份配置文件
   - 不要在公共网络环境下使用

3. **访问控制**
   - 当前版本未包含用户认证
   - 建议在受信任的网络环境中使用

## 🐛 故障排除

### 常见问题

1. **无法启动Web服务**
   - 检查端口5000是否被占用
   - 确认Python版本 >= 3.8
   - 检查依赖包是否正确安装

2. **无法启动交易程序**
   - 确认startup.py文件存在
   - 检查账户配置是否正确
   - 查看错误日志获取详细信息

3. **配置保存失败**
   - 检查文件权限
   - 确认配置格式正确
   - 查看浏览器控制台错误信息

## 📈 未来规划

### 短期计划
- [ ] 添加用户认证和权限管理
- [ ] 增加更多的系统监控指标
- [ ] 优化移动端显示效果
- [ ] 添加配置文件版本控制

### 长期计划
- [ ] 集成交易数据可视化
- [ ] 添加策略回测功能
- [ ] 支持多语言界面
- [ ] 开发移动端APP

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

### 开发环境设置
1. Fork本项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 📄 许可证

本项目基于原有量化交易系统开发，请遵守相关许可证条款。

## 📞 支持与反馈

如果您在使用过程中遇到问题或有改进建议，请：
1. 查看使用说明文档
2. 检查故障排除部分
3. 提交Issue或联系开发者

---

**注意**: 本Web管理界面是对原有量化交易系统的补充，不会影响原有的命令行操作方式。您仍然可以通过传统方式运行和管理系统。
