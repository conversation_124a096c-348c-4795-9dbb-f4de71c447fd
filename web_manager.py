#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
量化交易系统 Web 管理界面
作者: AI Assistant
功能: 提供Web界面来管理量化交易程序的启动/停止、配置管理等功能
"""

import os
import sys
import json
import time
import subprocess
import threading
import importlib.util
import re
import glob
import shutil
import secrets
import platform
import pandas as pd
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

import psutil
from flask import Flask, render_template, request, jsonify, g, session, redirect
from flask_socketio import SocketIO, emit

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

# 导入增强的进程管理器
try:
    from enhanced_process_manager import EnhancedProcessManager, VerificationLevel
    ENHANCED_PROCESS_MANAGER_ENABLED = True
    print("✓ 增强进程管理器加载成功")
except ImportError as e:
    print(f"⚠ 增强进程管理器加载失败: {e}")
    print("系统将使用原有的进程管理器")
    ENHANCED_PROCESS_MANAGER_ENABLED = False

# 导入认证系统
try:
    from auth_manager import user_manager, session_manager, google_auth_manager, security_manager
    from auth_middleware import auth_middleware, login_required, permission_required, google_auth_required, admin_required
    from auth_routes import auth_bp
    AUTH_ENABLED = True
    print("✓ 认证系统加载成功")
except ImportError as e:
    print(f"⚠ 认证系统加载失败: {e}")
    print("系统将在无认证模式下运行")
    AUTH_ENABLED = False

# 不直接导入config.py，避免其中的exit()逻辑
# 我们将通过文件读取的方式来获取配置信息
config = None

# Flask应用初始化
app = Flask(__name__, static_folder='static', static_url_path='/static')

# 生成更安全的SECRET_KEY
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY') or secrets.token_hex(32)
app.config['WTF_CSRF_ENABLED'] = True

# 会话配置
app.config['SESSION_COOKIE_SECURE'] = False  # 生产环境应设为True（需要HTTPS）
app.config['SESSION_COOKIE_HTTPONLY'] = True
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'
app.config['PERMANENT_SESSION_LIFETIME'] = 10800  # 3小时

# 性能优化配置
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 31536000  # 静态文件缓存1年
app.config['TEMPLATES_AUTO_RELOAD'] = True  # 开发环境启用模板自动重载

# 初始化认证系统
if AUTH_ENABLED:
    auth_middleware.init_app(app)
    app.register_blueprint(auth_bp)

# SocketIO初始化（用于实时状态更新）
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')

# 全局变量
trading_process = None
process_monitor_thread = None
log_monitor_thread = None
is_monitoring = False

class ScheduleScriptBackupManager:
    """调度脚本备份管理器"""

    def __init__(self):
        self.script_path = PROJECT_ROOT / 'schedule_startup.py'
        self.backup_dir = PROJECT_ROOT / 'scheduler_backups'
        self.backup_dir.mkdir(exist_ok=True)
        self.max_backups = 3

    def create_backup(self, comment: str = "") -> Dict[str, Any]:
        """创建备份文件"""
        try:
            if not self.script_path.exists():
                return {"success": False, "message": "原始脚本文件不存在"}

            # 生成备份文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"schedule_startup_backup_{timestamp}.py"
            backup_path = self.backup_dir / backup_filename

            # 复制文件
            shutil.copy2(self.script_path, backup_path)

            # 如果有备注，创建对应的备注文件
            if comment:
                comment_path = self.backup_dir / f"{backup_filename}.comment"
                with open(comment_path, 'w', encoding='utf-8') as f:
                    f.write(comment)

            # 清理旧备份
            self._cleanup_old_backups()

            return {
                "success": True,
                "message": "备份创建成功",
                "backup_file": backup_filename
            }

        except Exception as e:
            return {"success": False, "message": f"创建备份失败: {str(e)}"}

    def get_backup_list(self) -> List[Dict[str, Any]]:
        """获取备份文件列表"""
        try:
            backups = []
            backup_files = list(self.backup_dir.glob("schedule_startup_backup_*.py"))

            for backup_file in backup_files:
                # 解析时间戳
                filename = backup_file.name
                timestamp_str = filename.replace("schedule_startup_backup_", "").replace(".py", "")

                try:
                    timestamp = datetime.strptime(timestamp_str, '%Y%m%d_%H%M%S')
                    formatted_time = timestamp.strftime('%Y-%m-%d %H:%M:%S')
                except:
                    formatted_time = "未知时间"

                # 获取文件大小
                file_size = backup_file.stat().st_size
                size_str = self._format_file_size(file_size)

                # 获取备注
                comment_path = self.backup_dir / f"{filename}.comment"
                comment = ""
                if comment_path.exists():
                    try:
                        with open(comment_path, 'r', encoding='utf-8') as f:
                            comment = f.read().strip()
                    except:
                        pass

                backups.append({
                    "filename": filename,
                    "timestamp": timestamp_str,
                    "formatted_time": formatted_time,
                    "size": file_size,
                    "size_str": size_str,
                    "comment": comment
                })

            # 按时间倒序排列
            backups.sort(key=lambda x: x["timestamp"], reverse=True)
            return backups

        except Exception:
            return []

    def get_backup_content(self, filename: str) -> Dict[str, Any]:
        """获取备份文件内容"""
        try:
            backup_path = self.backup_dir / filename
            if not backup_path.exists():
                return {"success": False, "message": "备份文件不存在"}

            with open(backup_path, 'r', encoding='utf-8') as f:
                content = f.read()

            return {"success": True, "content": content}

        except Exception as e:
            return {"success": False, "message": f"读取备份文件失败: {str(e)}"}

    def restore_backup(self, filename: str) -> Dict[str, Any]:
        """恢复备份文件"""
        try:
            backup_path = self.backup_dir / filename
            if not backup_path.exists():
                return {"success": False, "message": "备份文件不存在"}

            # 在恢复前创建当前版本的备份
            current_backup = self.create_backup("恢复前自动备份")
            if not current_backup["success"]:
                return {"success": False, "message": f"创建当前版本备份失败: {current_backup['message']}"}

            # 恢复备份文件
            shutil.copy2(backup_path, self.script_path)

            return {"success": True, "message": "备份恢复成功"}

        except Exception as e:
            return {"success": False, "message": f"恢复备份失败: {str(e)}"}

    def delete_backup(self, filename: str) -> Dict[str, Any]:
        """删除备份文件"""
        try:
            backup_path = self.backup_dir / filename
            comment_path = self.backup_dir / f"{filename}.comment"

            if backup_path.exists():
                backup_path.unlink()

            if comment_path.exists():
                comment_path.unlink()

            return {"success": True, "message": "备份删除成功"}

        except Exception as e:
            return {"success": False, "message": f"删除备份失败: {str(e)}"}

    def delete_multiple_backups(self, filenames: List[str]) -> Dict[str, Any]:
        """批量删除备份文件"""
        try:
            deleted_count = 0
            errors = []

            for filename in filenames:
                result = self.delete_backup(filename)
                if result["success"]:
                    deleted_count += 1
                else:
                    errors.append(f"{filename}: {result['message']}")

            if errors:
                return {
                    "success": False,
                    "message": f"部分删除失败，成功删除 {deleted_count} 个文件。错误: {'; '.join(errors)}"
                }
            else:
                return {
                    "success": True,
                    "message": f"成功删除 {deleted_count} 个备份文件"
                }

        except Exception as e:
            return {"success": False, "message": f"批量删除失败: {str(e)}"}

    def cleanup_old_backups(self, keep_count: int = None) -> Dict[str, Any]:
        """清理旧备份，保留指定数量的最新备份"""
        try:
            if keep_count is None:
                keep_count = self.max_backups

            backups = self.get_backup_list()
            if len(backups) <= keep_count:
                return {"success": True, "message": "无需清理"}

            # 删除多余的备份
            to_delete = backups[keep_count:]
            filenames_to_delete = [backup["filename"] for backup in to_delete]

            return self.delete_multiple_backups(filenames_to_delete)

        except Exception as e:
            return {"success": False, "message": f"清理备份失败: {str(e)}"}

    def _cleanup_old_backups(self):
        """内部方法：自动清理旧备份"""
        try:
            self.cleanup_old_backups()
        except:
            pass  # 静默处理清理错误

    def _format_file_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        else:
            return f"{size_bytes / (1024 * 1024):.1f} MB"

class ProcessManager:
    """进程管理器 - 负责启动、停止和监控交易程序"""
    
    def __init__(self):
        self.process = None
        self.process_id = None
        
    def start_trading(self) -> Dict[str, Any]:
        """启动交易程序"""
        try:
            if self.is_running():
                return {"success": False, "message": "交易程序已在运行中"}

            # 启动交易程序
            startup_script = PROJECT_ROOT / "startup.py"
            if not startup_script.exists():
                return {"success": False, "message": "找不到启动脚本文件"}

            # 使用当前Python解释器启动，添加-u参数确保输出不被缓冲
            cmd = [sys.executable, "-u", str(startup_script)]

            # 创建日志目录
            logs_dir = PROJECT_ROOT / "logs"
            logs_dir.mkdir(exist_ok=True)

            # 创建日志文件
            log_file = logs_dir / "trading_program.log"

            self.process = subprocess.Popen(
                cmd,
                cwd=str(PROJECT_ROOT),
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,  # 将stderr重定向到stdout
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            self.process_id = self.process.pid

            # 启动日志监控线程
            start_log_monitoring(self.process, log_file)

            return {
                "success": True,
                "message": f"交易程序启动成功，进程ID: {self.process_id}",
                "pid": self.process_id
            }

        except Exception as e:
            return {"success": False, "message": f"启动失败: {str(e)}"}
    
    def stop_trading(self) -> Dict[str, Any]:
        """停止交易程序"""
        try:
            if not self.is_running():
                return {"success": False, "message": "交易程序未在运行"}
            
            if self.process:
                # 优雅停止
                self.process.terminate()
                
                # 等待进程结束，最多等待10秒
                try:
                    self.process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    # 强制杀死进程
                    self.process.kill()
                    self.process.wait()
                
                self.process = None
                self.process_id = None
                
                return {"success": True, "message": "交易程序已停止"}
            else:
                return {"success": False, "message": "无法找到进程句柄"}
                
        except Exception as e:
            return {"success": False, "message": f"停止失败: {str(e)}"}

    def start_schedule_debug(self) -> Dict[str, Any]:
        """启动调度调试"""
        try:
            if self.is_running():
                return {"success": False, "message": "已有程序在运行中，请先停止"}

            # 启动调度调试程序
            schedule_script = PROJECT_ROOT / "schedule_startup.py"
            if not schedule_script.exists():
                return {"success": False, "message": "找不到调度脚本文件"}

            # 使用当前Python解释器启动，添加-u参数确保输出不被缓冲
            cmd = [sys.executable, "-u", str(schedule_script)]

            # 创建日志目录
            logs_dir = PROJECT_ROOT / "logs"
            logs_dir.mkdir(exist_ok=True)

            # 创建日志文件
            log_file = logs_dir / "schedule_debug.log"

            self.process = subprocess.Popen(
                cmd,
                cwd=str(PROJECT_ROOT),
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,  # 将stderr重定向到stdout
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            self.process_id = self.process.pid

            # 立即发送启动确认消息
            startup_msg = f"调度调试启动成功，进程ID: {self.process_id}"
            processed_log = {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'level': 'info',
                'message': startup_msg,
                'raw_message': startup_msg
            }
            socketio.emit('log_update', processed_log)

            # 启动日志监控线程
            start_log_monitoring(self.process, log_file)

            return {
                "success": True,
                "message": f"调度调试启动成功，进程ID: {self.process_id}",
                "pid": self.process_id
            }

        except Exception as e:
            return {"success": False, "message": f"启动调度调试失败: {str(e)}"}

    def start_schedule_debug_test(self) -> Dict[str, Any]:
        """启动调度调试测试"""
        try:
            if self.is_running():
                return {"success": False, "message": "已有程序在运行中，请先停止"}

            # 启动调度调试测试程序
            test_script = PROJECT_ROOT / "schedule_startup_test.py"
            if not test_script.exists():
                return {"success": False, "message": "找不到测试脚本文件"}

            # 使用当前Python解释器启动，添加-u参数确保输出不被缓冲
            cmd = [sys.executable, "-u", str(test_script)]

            # 创建日志目录
            logs_dir = PROJECT_ROOT / "logs"
            logs_dir.mkdir(exist_ok=True)

            # 创建日志文件
            log_file = logs_dir / "schedule_debug_test.log"

            self.process = subprocess.Popen(
                cmd,
                cwd=str(PROJECT_ROOT),
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,  # 将stderr重定向到stdout
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            self.process_id = self.process.pid

            # 立即发送启动确认消息
            startup_msg = f"调度调试测试启动成功，进程ID: {self.process_id}"
            processed_log = {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'level': 'info',
                'message': startup_msg,
                'raw_message': startup_msg
            }
            socketio.emit('log_update', processed_log)

            # 启动日志监控线程
            start_log_monitoring(self.process, log_file)

            return {
                "success": True,
                "message": f"调度调试测试启动成功，进程ID: {self.process_id}",
                "pid": self.process_id
            }

        except Exception as e:
            return {"success": False, "message": f"启动调度调试测试失败: {str(e)}"}

    def is_running(self) -> bool:
        """检查交易程序是否在运行"""
        if self.process:
            return self.process.poll() is None

        # 检查是否有startup.py或schedule_startup.py进程在运行
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = proc.info['cmdline']
                if cmdline:
                    cmdline_str = ' '.join(cmdline)
                    if 'startup.py' in cmdline_str or 'schedule_startup.py' in cmdline_str:
                        self.process_id = proc.info['pid']
                        return True
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

        return False
    
    def get_status(self) -> Dict[str, Any]:
        """获取程序状态"""
        is_running = self.is_running()
        
        status = {
            "is_running": is_running,
            "pid": self.process_id if is_running else None,
            "status_text": "运行中" if is_running else "已停止",
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        if is_running and self.process_id:
            try:
                proc = psutil.Process(self.process_id)
                status.update({
                    "cpu_percent": proc.cpu_percent(),
                    "memory_mb": proc.memory_info().rss / 1024 / 1024,
                    "start_time": datetime.fromtimestamp(proc.create_time()).strftime("%Y-%m-%d %H:%M:%S")
                })
            except psutil.NoSuchProcess:
                status["is_running"] = False
                status["status_text"] = "进程不存在"
        
        return status

class ConfigManager:
    """配置管理器 - 负责读取和修改配置文件"""

    def __init__(self):
        self.project_root = PROJECT_ROOT  # 添加project_root属性
        self.config_file = PROJECT_ROOT / "config.py"
        self.json_config_file = PROJECT_ROOT / "config.json"
        self.accounts_dir = PROJECT_ROOT / "accounts"
        self.accounts_json_file = PROJECT_ROOT / "accounts.json"
    
    def get_global_config(self) -> Dict[str, Any]:
        """获取全局配置 - 直接从config.py文件解析"""
        try:
            # 直接从config.py文件中解析配置（不导入，避免exit()）
            config = {}
            if self.config_file.exists():
                try:
                    with open(self.config_file, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # 使用正则表达式解析配置项

                    # 基础配置项
                    patterns = {
                        'realtime_data_path': r"realtime_data_path\s*=\s*r?['\"]([^'\"]*)['\"]",
                        'error_webhook_url': r"error_webhook_url\s*=\s*['\"]([^'\"]*)['\"]",
                        'is_debug': r"is_debug\s*=\s*(True|False)",
                        'clean_start': r"clean_start\s*=\s*(True|False)",
                        'factor_col_limit': r"factor_col_limit\s*=\s*(\d+)",
                    }

                    for key, pattern in patterns.items():
                        match = re.search(pattern, content)
                        if match:
                            value = match.group(1)
                            if key in ['is_debug', 'clean_start']:
                                config[key] = value == 'True'
                            elif key in ['factor_col_limit']:
                                config[key] = int(value)
                            else:
                                config[key] = value

                    # 特殊处理job_num，因为它可能有复杂的表达式
                    # 首先查找复杂表达式（非注释行）
                    complex_pattern = r"^job_num\s*=\s*max\(os\.cpu_count\(\)\s*-\s*1,\s*1\)"
                    complex_match = re.search(complex_pattern, content, re.MULTILINE)
                    if complex_match:
                        # 计算表达式的值
                        import os
                        config['job_num'] = max(os.cpu_count() - 1, 1)
                    else:
                        # 查找简单数字（非注释行）
                        simple_pattern = r"^job_num\s*=\s*(\d+)"
                        simple_match = re.search(simple_pattern, content, re.MULTILINE)
                        if simple_match:
                            config['job_num'] = int(simple_match.group(1))
                        else:
                            # 使用默认值
                            config['job_num'] = 2

                    # 解析simulator_config字典
                    simulator_match = re.search(r'simulator_config\s*=\s*dict\((.*?)\n\)', content, re.DOTALL)
                    if simulator_match:
                        simulator_content = simulator_match.group(1)
                        simulator_config = {}

                        # 解析simulator_config中的各个字段
                        simulator_patterns = {
                            'initial_usdt': r'initial_usdt\s*=\s*([0-9_,]+)',
                            'swap_c_rate': r'swap_c_rate\s*=\s*([\d.]+\s*/\s*[\d.]+|[\d.]+)',
                            'spot_c_rate': r'spot_c_rate\s*=\s*([\d.]+\s*/\s*[\d.]+|[\d.]+)',
                        }

                        for key, pattern in simulator_patterns.items():
                            match = re.search(pattern, simulator_content)
                            if match:
                                value = match.group(1)
                                if key == 'initial_usdt':
                                    # 处理数字中的下划线和逗号
                                    value = value.replace('_', '').replace(',', '')
                                    simulator_config[key] = int(value)
                                elif key in ['swap_c_rate', 'spot_c_rate']:
                                    # 处理分数表达式
                                    if '/' in value:
                                        parts = value.split('/')
                                        simulator_config[key] = float(parts[0].strip()) / float(parts[1].strip())
                                    else:
                                        simulator_config[key] = float(value)

                        config['simulator_config'] = simulator_config

                    # 解析data_config字典中的min_kline_num
                    data_config_match = re.search(r'data_config\s*=\s*dict\((.*?)\)', content, re.DOTALL)
                    if data_config_match:
                        data_content = data_config_match.group(1)
                        min_kline_match = re.search(r'min_kline_num\s*=\s*(\d+)', data_content)
                        if min_kline_match:
                            config['data_config'] = {
                                'min_kline_num': int(min_kline_match.group(1))
                            }

                    # 解析proxy配置
                    proxy_match = re.search(r'proxy\s*=\s*\{([^}]*)\}', content)
                    if proxy_match:
                        proxy_content = proxy_match.group(1).strip()
                        if proxy_content:
                            # 简单解析proxy字典
                            proxy_config = {}
                            http_match = re.search(r"['\"]http['\"]:\s*['\"]([^'\"]*)['\"]", proxy_content)
                            https_match = re.search(r"['\"]https['\"]:\s*['\"]([^'\"]*)['\"]", proxy_content)
                            if http_match:
                                proxy_config['http'] = http_match.group(1)
                            if https_match:
                                proxy_config['https'] = https_match.group(1)
                            config['proxy'] = proxy_config
                        else:
                            config['proxy'] = {}

                except Exception:
                    pass  # 静默处理解析错误

            # 直接返回从config.py解析的配置
            return config

        except Exception:
            return {}  # 静默处理读取错误
    
    def save_global_config(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """直接保存全局配置到config.py文件"""
        try:
            # 验证配置数据
            validated_config = self._validate_global_config(config_data)

            # 创建备份
            backup_result = self.create_config_backup()
            if not backup_result['success']:
                return backup_result

            # 直接修改config.py文件
            result = self._update_config_py(validated_config)
            if result['success']:
                result['backup_created'] = backup_result.get('backup_filename', '')

            return result
        except Exception as e:
            return {"success": False, "message": f"保存失败: {str(e)}"}

    def _validate_global_config(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """验证全局配置数据"""
        validated = {}

        # 基础配置项验证
        if 'realtime_data_path' in config_data:
            validated['realtime_data_path'] = str(config_data['realtime_data_path'])

        if 'error_webhook_url' in config_data:
            validated['error_webhook_url'] = str(config_data['error_webhook_url'])

        if 'is_debug' in config_data:
            validated['is_debug'] = bool(config_data['is_debug'])

        if 'job_num' in config_data:
            try:
                validated['job_num'] = int(config_data['job_num'])
            except (ValueError, TypeError):
                validated['job_num'] = 2  # 默认值

        if 'factor_col_limit' in config_data:
            try:
                validated['factor_col_limit'] = int(config_data['factor_col_limit'])
            except (ValueError, TypeError):
                validated['factor_col_limit'] = 64  # 默认值

        # simulator_config验证
        if 'simulator_config' in config_data:
            simulator = config_data['simulator_config']
            validated_simulator = {}

            if 'initial_usdt' in simulator:
                try:
                    validated_simulator['initial_usdt'] = int(simulator['initial_usdt'])
                except (ValueError, TypeError):
                    validated_simulator['initial_usdt'] = 2000

            if 'swap_c_rate' in simulator:
                try:
                    validated_simulator['swap_c_rate'] = float(simulator['swap_c_rate'])
                except (ValueError, TypeError):
                    validated_simulator['swap_c_rate'] = 0.00085

            if 'spot_c_rate' in simulator:
                try:
                    validated_simulator['spot_c_rate'] = float(simulator['spot_c_rate'])
                except (ValueError, TypeError):
                    validated_simulator['spot_c_rate'] = 0.002

            if validated_simulator:
                validated['simulator_config'] = validated_simulator

        # data_config验证
        if 'data_config' in config_data:
            data_config = config_data['data_config']
            validated_data = {}

            if 'min_kline_num' in data_config:
                try:
                    validated_data['min_kline_num'] = int(data_config['min_kline_num'])
                except (ValueError, TypeError):
                    validated_data['min_kline_num'] = 0

            if validated_data:
                validated['data_config'] = validated_data

        # proxy验证
        if 'proxy' in config_data:
            proxy = config_data['proxy']
            if isinstance(proxy, dict):
                validated_proxy = {}
                if 'http' in proxy:
                    validated_proxy['http'] = str(proxy['http'])
                if 'https' in proxy:
                    validated_proxy['https'] = str(proxy['https'])
                validated['proxy'] = validated_proxy

        return validated

    def _update_config_py(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """直接更新config.py文件中的配置项"""
        try:

            # 读取当前config.py文件内容
            with open(self.config_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 更新基础配置项
            if 'realtime_data_path' in config_data:
                content = self._update_python_variable(content, 'realtime_data_path',
                                                     repr(config_data['realtime_data_path']))

            if 'error_webhook_url' in config_data:
                content = self._update_python_variable(content, 'error_webhook_url',
                                                     repr(config_data['error_webhook_url']))

            if 'is_debug' in config_data:
                content = self._update_python_variable(content, 'is_debug',
                                                     str(config_data['is_debug']))

            if 'job_num' in config_data:
                # 更新job_num，需要特殊处理
                content = self._update_job_num(content, config_data['job_num'])

            if 'factor_col_limit' in config_data:
                content = self._update_python_variable(content, 'factor_col_limit',
                                                     str(config_data['factor_col_limit']))

            # 更新simulator_config字典
            if 'simulator_config' in config_data:
                content = self._update_simulator_config(content, config_data['simulator_config'])

            # 更新data_config字典中的min_kline_num
            if 'data_config' in config_data and 'min_kline_num' in config_data['data_config']:
                content = self._update_data_config(content, config_data['data_config'])

            # 更新proxy配置
            if 'proxy' in config_data:
                content = self._update_proxy_config(content, config_data['proxy'])

            # 写回文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                f.write(content)

            return {"success": True, "message": "配置文件更新成功"}

        except Exception as e:
            return {"success": False, "message": f"更新配置文件失败: {str(e)}"}

    def _update_python_variable(self, content: str, var_name: str, new_value: str) -> str:
        """更新Python文件中的变量值"""

        # 匹配变量赋值行，支持注释
        pattern = rf'^({var_name}\s*=\s*)([^#\n]*)(.*?)$'
        replacement = rf'\g<1>{new_value}  \g<3>'

        updated_content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
        return updated_content

    def _update_simulator_config(self, content: str, simulator_config: Dict[str, Any]) -> str:
        """更新simulator_config字典中的配置项"""

        # 找到simulator_config字典的开始和结束位置
        pattern = r'(simulator_config\s*=\s*dict\s*\()(.*?)(\))'
        match = re.search(pattern, content, re.DOTALL)

        if not match:
            return content

        dict_content = match.group(2)

        # 更新字典中的各个配置项
        for key, value in simulator_config.items():
            if key == 'initial_usdt':
                # 处理带下划线的数字格式
                formatted_value = f"{value:_}" if isinstance(value, int) and value >= 1000 else str(value)
                dict_content = self._update_dict_item(dict_content, key, formatted_value)
            elif key in ['swap_c_rate', 'spot_c_rate']:
                # 处理费率格式，保持原有的分数表示法
                if key == 'swap_c_rate':
                    # 转换为 x.x / 10000 格式
                    rate_value = value * 10000
                    formatted_value = f"{rate_value} / 10000"
                else:  # spot_c_rate
                    # 转换为 x / 1000 格式
                    rate_value = value * 1000
                    formatted_value = f"{int(rate_value)} / 1000"
                dict_content = self._update_dict_item(dict_content, key, formatted_value)

        # 重新组装内容
        updated_content = content[:match.start()] + match.group(1) + dict_content + match.group(3) + content[match.end():]
        return updated_content

    def _update_dict_item(self, dict_content: str, key: str, new_value: str) -> str:
        """更新字典中的单个配置项"""

        # 匹配字典项，支持注释
        pattern = rf'(\s*{key}\s*=\s*)([^,\n#]*)(.*?)(?=,|\n|$)'
        replacement = rf'\g<1>{new_value}\g<3>'

        updated_content = re.sub(pattern, replacement, dict_content, flags=re.MULTILINE)
        return updated_content

    def _update_data_config(self, content: str, data_config: Dict[str, Any]) -> str:
        """更新data_config字典中的min_kline_num"""

        if 'min_kline_num' not in data_config:
            return content

        # 找到data_config字典的定义
        pattern = r'(data_config\s*=\s*dict\s*\()(.*?)(\))'
        match = re.search(pattern, content, re.DOTALL)

        if not match:
            return content

        dict_content = match.group(2)
        new_value = str(data_config['min_kline_num'])

        # 更新min_kline_num
        dict_content = self._update_dict_item(dict_content, 'min_kline_num', new_value)

        # 重新组装内容
        updated_content = content[:match.start()] + match.group(1) + dict_content + match.group(3) + content[match.end():]
        return updated_content

    def _update_proxy_config(self, content: str, proxy_config: Dict[str, str]) -> str:
        """更新proxy配置"""

        # 找到proxy变量的赋值行
        pattern = r'(proxy\s*=\s*)(\{[^}]*\}|\{\})'

        # 构建新的proxy字典字符串
        if proxy_config and (proxy_config.get('http') or proxy_config.get('https')):
            proxy_items = []
            if proxy_config.get('http'):
                proxy_items.append(f"'http': '{proxy_config['http']}'")
            if proxy_config.get('https'):
                proxy_items.append(f"'https': '{proxy_config['https']}'")
            new_proxy = "{" + ", ".join(proxy_items) + "}"
        else:
            new_proxy = "{}"

        replacement = rf'\g<1>{new_proxy}'
        updated_content = re.sub(pattern, replacement, content, flags=re.MULTILINE)

        return updated_content

    def _update_job_num(self, content: str, new_value: int) -> str:
        """特殊处理job_num的更新"""

        # 查找当前的job_num设置
        lines = content.split('\n')
        updated_lines = []
        job_num_updated = False
        skip_next_job_num = False

        for line in lines:
            # 如果上一行已经处理了job_num，跳过重复的job_num行
            if skip_next_job_num and re.match(r'^job_num\s*=', line.strip()):
                skip_next_job_num = False
                continue

            # 如果是复杂表达式的job_num行，注释掉它
            if re.match(r'^job_num\s*=\s*max\(', line.strip()):
                updated_lines.append(f'# {line}')
                # 在下一行添加新的简单设置
                updated_lines.append(f'job_num = {new_value}  # 回测并行数量')
                job_num_updated = True
                skip_next_job_num = True
            # 如果是简单的job_num设置，直接更新
            elif re.match(r'^job_num\s*=\s*\d+', line.strip()) and not job_num_updated:
                updated_lines.append(f'job_num = {new_value}  # 回测并行数量')
                job_num_updated = True
            # 如果是注释的job_num行，取消注释并更新
            elif re.match(r'^#\s*job_num\s*=\s*\d+', line.strip()) and not job_num_updated:
                updated_lines.append(f'job_num = {new_value}  # 回测并行数量')
                job_num_updated = True
            # 跳过已经存在的重复job_num行
            elif re.match(r'^job_num\s*=', line.strip()) and job_num_updated:
                continue
            else:
                updated_lines.append(line)

        # 如果没有找到job_num设置，在适当位置添加
        if not job_num_updated:
            # 在factor_col_limit之前添加
            for i, line in enumerate(updated_lines):
                if 'factor_col_limit' in line:
                    updated_lines.insert(i, f'job_num = {new_value}  # 回测并行数量')
                    updated_lines.insert(i+1, '')
                    break

        return '\n'.join(updated_lines)

    def create_config_backup(self) -> Dict[str, Any]:
        """创建配置文件备份"""
        try:
            # 确保备份目录存在
            backup_dir = Path(self.project_root) / 'config_backups'
            backup_dir.mkdir(exist_ok=True)

            # 生成备份文件名（时间戳格式）
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f'config_backup_{timestamp}.py'
            backup_path = backup_dir / backup_filename

            # 复制当前配置文件
            config_path = Path(self.project_root) / 'config.py'
            if not config_path.exists():
                return {'success': False, 'message': '配置文件config.py不存在'}

            import shutil
            shutil.copy2(config_path, backup_path)

            # 实现循环备份机制：最多保留3个备份
            self._cleanup_old_backups(backup_dir)

            return {
                'success': True,
                'message': f'配置备份创建成功: {backup_filename}',
                'backup_file': backup_filename,
                'backup_path': str(backup_path)
            }

        except Exception as e:
            return {'success': False, 'message': f'创建备份失败: {str(e)}'}

    def _cleanup_old_backups(self, backup_dir: Path):
        """清理旧备份文件，保留最新的3个"""
        try:
            # 获取所有备份文件
            backup_files = list(backup_dir.glob('config_backup_*.py'))

            # 按文件名中的时间戳排序（最新的在前）
            # 文件名格式: config_backup_YYYYMMDD_HHMMSS.py
            def extract_timestamp(filepath):
                try:
                    filename = filepath.name
                    # 提取时间戳部分: config_backup_20250707_231133.py -> 20250707_231133
                    timestamp_part = filename.replace('config_backup_', '').replace('.py', '')
                    return timestamp_part
                except:
                    return '00000000_000000'  # 默认值，确保排序正常

            backup_files.sort(key=extract_timestamp, reverse=True)

            # 删除超过3个的旧备份
            if len(backup_files) > 3:
                for old_backup in backup_files[3:]:
                    old_backup.unlink()

        except Exception:
            pass  # 静默处理清理错误

    def list_config_backups(self) -> Dict[str, Any]:
        """列出所有配置备份文件"""
        try:
            backup_dir = Path(self.project_root) / 'config_backups'
            if not backup_dir.exists():
                return {'success': True, 'backups': []}

            backup_files = list(backup_dir.glob('config_backup_*.py'))
            backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)

            backups = []
            for backup_file in backup_files:
                stat = backup_file.stat()
                backups.append({
                    'filename': backup_file.name,
                    'path': str(backup_file),
                    'size': stat.st_size,
                    'size_human': self._format_file_size(stat.st_size),
                    'created_time': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
                    'timestamp': stat.st_mtime
                })

            return {'success': True, 'backups': backups}

        except Exception as e:
            return {'success': False, 'message': f'获取备份列表失败: {str(e)}'}

    def _format_file_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        else:
            return f"{size_bytes / (1024 * 1024):.1f} MB"

    def restore_config_backup(self, backup_filename: str) -> Dict[str, Any]:
        """从备份恢复配置文件"""
        try:
            backup_dir = Path(self.project_root) / 'config_backups'
            backup_path = backup_dir / backup_filename

            if not backup_path.exists():
                return {'success': False, 'message': f'备份文件不存在: {backup_filename}'}

            # 在恢复前先创建当前配置的备份
            current_backup_result = self.create_config_backup()
            if not current_backup_result['success']:
                return {'success': False, 'message': f'无法创建当前配置备份: {current_backup_result["message"]}'}

            # 恢复备份文件
            config_path = Path(self.project_root) / 'config.py'
            import shutil
            shutil.copy2(backup_path, config_path)

            return {
                'success': True,
                'message': f'配置已从备份 {backup_filename} 恢复成功',
                'current_backup': current_backup_result['backup_file']
            }

        except Exception as e:
            return {'success': False, 'message': f'恢复备份失败: {str(e)}'}

    def validate_config(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """验证配置的有效性"""
        errors = []
        warnings = []

        try:
            # 验证必需的配置项
            if 'realtime_data_path' in config_data:
                path = config_data['realtime_data_path']
                if not path:
                    errors.append("实时数据路径不能为空")
                elif not Path(path).exists():
                    warnings.append(f"实时数据路径不存在: {path}")

            # 验证webhook URL格式
            if 'error_webhook_url' in config_data:
                url = config_data['error_webhook_url']
                if url and not url.startswith(('http://', 'https://')):
                    errors.append("webhook URL格式不正确，必须以http://或https://开头")

            # 验证数值范围
            if 'job_num' in config_data:
                job_num = config_data['job_num']
                if not isinstance(job_num, (int, float)) or job_num < 1 or job_num > 32:
                    errors.append("任务并发数必须在1-32之间")

            if 'factor_col_limit' in config_data:
                limit = config_data['factor_col_limit']
                if not isinstance(limit, (int, float)) or limit < 16 or limit > 1024:
                    errors.append("因子列数限制必须在16-1024之间")

            # 验证simulator_config
            if 'simulator_config' in config_data:
                sim_config = config_data['simulator_config']
                if isinstance(sim_config, dict):
                    if 'initial_usdt' in sim_config:
                        initial = sim_config['initial_usdt']
                        if not isinstance(initial, (int, float)) or initial < 100:
                            errors.append("初始USDT金额不能少于100")

                    if 'swap_c_rate' in sim_config:
                        rate = sim_config['swap_c_rate']
                        if not isinstance(rate, (int, float)) or rate < 0 or rate > 1:
                            errors.append("合约手续费率必须在0-1之间")

                    if 'spot_c_rate' in sim_config:
                        rate = sim_config['spot_c_rate']
                        if not isinstance(rate, (int, float)) or rate < 0 or rate > 1:
                            errors.append("现货手续费率必须在0-1之间")

            # 验证data_config
            if 'data_config' in config_data:
                data_config = config_data['data_config']
                if isinstance(data_config, dict):
                    if 'min_kline_num' in data_config:
                        min_kline = data_config['min_kline_num']
                        if not isinstance(min_kline, (int, float)) or min_kline < 0:
                            errors.append("最小K线数量不能为负数")

            # 验证proxy配置
            if 'proxy' in config_data:
                proxy = config_data['proxy']
                if isinstance(proxy, dict):
                    for key, value in proxy.items():
                        if value and not value.startswith(('http://', 'https://')):
                            errors.append(f"代理地址格式不正确: {key}")

            # 返回验证结果
            if errors:
                return {
                    'success': False,
                    'message': '配置验证失败',
                    'errors': errors,
                    'warnings': warnings
                }
            else:
                return {
                    'success': True,
                    'message': '配置验证通过',
                    'warnings': warnings
                }

        except Exception as e:
            return {
                'success': False,
                'message': f'验证过程中发生错误: {str(e)}',
                'errors': [str(e)]
            }

    def validate_config_file_syntax(self) -> Dict[str, Any]:
        """验证config.py文件的Python语法"""
        try:
            config_path = Path(self.project_root) / 'config.py'
            if not config_path.exists():
                return {'success': False, 'message': '配置文件config.py不存在'}

            # 读取配置文件内容
            with open(config_path, 'r', encoding='utf-8') as f:
                config_content = f.read()

            # 进行Python语法检查
            import ast
            try:
                ast.parse(config_content)
                syntax_valid = True
                syntax_error = None
            except SyntaxError as e:
                syntax_valid = False
                syntax_error = f"第{e.lineno}行: {e.msg}"

            # 尝试导入配置模块进行运行时检查
            runtime_valid = True
            runtime_error = None
            try:
                # 检查是否包含exit()调用，如果有则跳过运行时检查
                if 'exit()' in config_content:
                    runtime_valid = True  # 语法正确但跳过运行时检查
                    runtime_error = "配置文件包含exit()调用，跳过运行时验证"
                else:
                    # 临时导入配置模块
                    spec = importlib.util.spec_from_file_location("temp_config", config_path)
                    temp_config = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(temp_config)
            except SystemExit:
                runtime_valid = True  # 遇到exit()调用，认为语法正确
                runtime_error = "配置文件包含exit()调用"
            except Exception as e:
                runtime_valid = False
                runtime_error = str(e)

            # 检查必需的配置项
            required_items = ['realtime_data_path', 'error_webhook_url', 'is_debug']
            missing_items = []

            if syntax_valid and runtime_valid:
                try:
                    current_config = self.get_global_config()
                    for item in required_items:
                        if item not in current_config:
                            missing_items.append(item)
                except Exception:
                    pass

            # 汇总验证结果
            errors = []
            warnings = []

            if not syntax_valid:
                errors.append(f"语法错误: {syntax_error}")

            if not runtime_valid:
                errors.append(f"运行时错误: {runtime_error}")

            if missing_items:
                warnings.append(f"缺少推荐配置项: {', '.join(missing_items)}")

            # 如果语法和运行时都正确，进行配置值验证
            if syntax_valid and runtime_valid:
                try:
                    config_data = self.get_global_config()
                    value_validation = self.validate_config(config_data)
                    if not value_validation['success']:
                        errors.extend(value_validation.get('errors', []))
                    warnings.extend(value_validation.get('warnings', []))
                except Exception as e:
                    warnings.append(f"配置值验证时出错: {str(e)}")

            return {
                'success': len(errors) == 0,
                'message': '配置文件验证完成',
                'syntax_valid': syntax_valid,
                'runtime_valid': runtime_valid,
                'errors': errors,
                'warnings': warnings,
                'file_size': config_path.stat().st_size,
                'last_modified': datetime.fromtimestamp(config_path.stat().st_mtime).strftime('%Y-%m-%d %H:%M:%S')
            }

        except Exception as e:
            return {
                'success': False,
                'message': f'验证配置文件时发生错误: {str(e)}',
                'errors': [str(e)]
            }

    def get_config_file_content(self) -> Dict[str, Any]:
        """获取配置文件内容"""
        try:
            config_path = Path(self.project_root) / 'config.py'
            if not config_path.exists():
                return {'success': False, 'message': '配置文件config.py不存在'}

            with open(config_path, 'r', encoding='utf-8') as f:
                content = f.read()

            stat = config_path.stat()
            return {
                'success': True,
                'content': content,
                'file_size': stat.st_size,
                'size_human': self._format_file_size(stat.st_size),
                'last_modified': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
                'line_count': len(content.splitlines())
            }

        except Exception as e:
            return {'success': False, 'message': f'读取配置文件失败: {str(e)}'}

    def save_config_file_content(self, content: str) -> Dict[str, Any]:
        """保存配置文件内容"""
        try:
            # 首先验证语法
            import ast
            try:
                ast.parse(content)
            except SyntaxError as e:
                return {
                    'success': False,
                    'message': f'语法错误，无法保存: 第{e.lineno}行 - {e.msg}'
                }

            # 创建当前配置的备份
            backup_result = self.create_config_backup()
            if not backup_result['success']:
                return {
                    'success': False,
                    'message': f'无法创建备份，保存中止: {backup_result["message"]}'
                }

            # 保存新内容
            config_path = Path(self.project_root) / 'config.py'
            with open(config_path, 'w', encoding='utf-8') as f:
                f.write(content)

            return {
                'success': True,
                'message': '配置文件保存成功',
                'backup_created': backup_result['backup_file']
            }

        except Exception as e:
            return {'success': False, 'message': f'保存配置文件失败: {str(e)}'}
    
    def get_account_list(self) -> List[Dict[str, Any]]:
        """获取交易账户列表（包括启用和禁用的账户）"""
        accounts = []
        try:
            # 获取启用的账户
            if self.accounts_dir.exists():
                for account_file in self.accounts_dir.glob("*.py"):
                    if not account_file.name.startswith('_'):
                        account_info = self._parse_account_file(account_file, enabled=True)
                        if account_info:
                            accounts.append(account_info)

            # 获取禁用的账户
            disabled_dir = self.accounts_dir.parent / "accounts_disabled"
            if disabled_dir.exists():
                for account_file in disabled_dir.glob("*.py"):
                    if not account_file.name.startswith('_'):
                        account_info = self._parse_account_file(account_file, enabled=False)
                        if account_info:
                            accounts.append(account_info)
        except Exception:
            pass  # 静默处理读取错误

        return sorted(accounts, key=lambda x: (not x.get('enabled', True), x['name']))

    def _parse_account_file(self, account_file: Path, enabled: bool = True) -> Dict[str, Any]:
        """解析交易账户配置文件"""
        try:
            # 读取Python配置文件
            spec = importlib.util.spec_from_file_location("account_module", account_file)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)

            # 提取基本信息
            account_config = getattr(module, 'account_config', {})
            strategy_config = getattr(module, 'strategy_config', {})
            strategy_pool = getattr(module, 'strategy_pool', [])
            leverage = getattr(module, 'leverage', 1)

            return {
                "name": account_file.stem,
                "filename": account_file.name,
                "path": str(account_file),
                "modified": datetime.fromtimestamp(account_file.stat().st_mtime).strftime("%Y-%m-%d %H:%M:%S"),
                "account_type": account_config.get('account_type', '普通账户'),
                "api_configured": bool(account_config.get('apiKey') and account_config.get('secret')),
                "strategy_name": strategy_config.get('name', '未配置'),
                "hold_period": strategy_config.get('hold_period', '未设置'),
                "strategy_count": len(strategy_pool[0].get('strategy_list', [])) if strategy_pool else 0,
                "leverage": leverage,
                "wechat_enabled": bool(account_config.get('wechat_webhook_url')),
                "bnb_burn_enabled": account_config.get('if_use_bnb_burn', False),
                "status": "已配置" if account_config.get('apiKey') else "未配置",
                "enabled": enabled
            }
        except Exception as e:
            return {
                "name": account_file.stem,
                "filename": account_file.name,
                "path": str(account_file),
                "modified": datetime.fromtimestamp(account_file.stat().st_mtime).strftime("%Y-%m-%d %H:%M:%S"),
                "status": "解析失败",
                "error": str(e),
                "enabled": enabled
            }

    def save_account_list(self, accounts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """保存账户列表到JSON文件"""
        try:
            with open(self.accounts_json_file, 'w', encoding='utf-8') as f:
                json.dump(accounts, f, ensure_ascii=False, indent=2)

            return {"success": True, "message": "账户列表保存成功"}
        except Exception as e:
            return {"success": False, "message": f"保存失败: {str(e)}"}

    def get_account_config(self, account_name: str) -> Dict[str, Any]:
        """获取指定账户的配置"""
        try:
            account_file = self.accounts_dir / f"{account_name}.py"
            if not account_file.exists():
                return {"error": "账户配置文件不存在"}

            # 使用动态模块加载方式解析配置文件
            spec = importlib.util.spec_from_file_location("account_module", account_file)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)

            # 提取配置信息
            account_config = getattr(module, 'account_config', {})
            strategy_config = getattr(module, 'strategy_config', {})
            backtest_name = getattr(module, 'backtest_name', '')
            get_kline_num = getattr(module, 'get_kline_num', 0)
            leverage = getattr(module, 'leverage', 1)
            black_list = getattr(module, 'black_list', [])
            white_list = getattr(module, 'white_list', [])

            # 返回完整的配置信息
            return {
                'account_config': account_config,
                'strategy_config': strategy_config,
                'backtest_name': backtest_name,
                'get_kline_num': get_kline_num,
                'leverage': leverage,
                'black_list': black_list,
                'white_list': white_list,
                # 为了兼容性，也提供扁平化的字段
                'account_type': account_config.get('account_type', '普通账户'),
                'apiKey': account_config.get('apiKey', ''),
                'secret': account_config.get('secret', ''),
                'strategy_name': strategy_config.get('name', '未配置'),
                'hold_period': strategy_config.get('hold_period', '未设置')
            }

        except Exception as e:
            return {"error": f"读取账户配置失败: {str(e)}"}

    def save_account_config(self, account_name: str, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """保存账户配置"""
        try:
            account_file = self.accounts_dir / f"{account_name}.py"

            # 生成Python配置文件内容
            backtest_name = config_data.get("backtest_name", account_name)
            strategy_name = config_data.get("strategy_name", "FixedRatioStrategy")
            hold_period = config_data.get("hold_period", "1H")

            # 构建account_config部分
            account_config_lines = [
                "account_config = {",
                "    # 交易所API配置",
                f"    'apiKey': '{config_data.get('apiKey', '')}',",
                f"    'secret': '{config_data.get('secret', '')}',",
                f"    'account_type': '{config_data.get('account_type', '普通账户')}',",
            ]

            # 添加统一账户保证金配置
            if config_data.get("account_type") == "统一账户" and config_data.get("coin_margin"):
                account_config_lines.extend([
                    "    # ++++ 统一账户保证金配置 ++++",
                    "    # 仅在account_type为'统一账户'时生效",
                    "    'coin_margin': {"
                ])
                coin_margin = config_data.get("coin_margin", {})
                for symbol, amount in coin_margin.items():
                    account_config_lines.append(f"        '{symbol}': {amount},  # 为{symbol}交易对设置{amount} USDT作为保证金")
                account_config_lines.extend([
                    "        # 可以继续添加其他交易对的保证金配置",
                    "    },"
                ])

            # 添加其他配置
            hour_offset = config_data.get('hour_offset', '0m')
            wechat_url = config_data.get('wechat_webhook_url', '')
            max_order_amount = config_data.get('max_one_order_amount', 100)
            twap_interval = config_data.get('twap_interval', 2)
            use_bnb_burn = str(config_data.get('if_use_bnb_burn', True))
            buy_bnb_value = config_data.get('buy_bnb_value', 11)
            transfer_bnb = str(config_data.get('if_transfer_bnb', True))

            account_config_lines.extend([
                "    # ++++ 分钟偏移功能 ++++",
                "    # 支持任意时间开始的小时级别K线",
                f'    "hour_offset": \'{hour_offset}\',  # 分钟偏移设置，可以自由设置时间，配置必须是kline脚本中interval的倍数。默认：0m，表示不偏移。15m，表示每个小时偏移15m下单。',
                "    # ++++ 企业微信机器人功能 ++++",
                f'    "wechat_webhook_url": \'{wechat_url}\',',
                "",
                "    # ++++ 下单时动态拆单功能 ++++",
                f'    "max_one_order_amount": {max_order_amount},  # 最大拆单金额。',
                f'    "twap_interval": {twap_interval},  # 下单间隔',
                "",
                "    # ++++ BNB抵扣手续费功能 ++++",
                f'    "if_use_bnb_burn": {use_bnb_burn},  # 是否开启BNB燃烧，抵扣手续费',
                f'    "buy_bnb_value": {buy_bnb_value},  # 买多少U的bnb来抵扣手续费。建议最低11U，现货最小下单量限制10U',
                "",
                "    # ++++ 小额资产自动兑换BNB功能 ++++",
                "    # 当且仅当 `if_use_bnb_burn` 为 True 时生效",
                f'    "if_transfer_bnb": {transfer_bnb},  # 是否开启小额资产兑换BNB功能。仅现货模式下生效',
                "}"
            ])

            account_config_str = "\n".join(account_config_lines)

            # 处理策略池配置
            if config_data.get('strategy_pool_code'):
                # 使用用户提供的策略池代码
                strategy_pool_section = config_data.get('strategy_pool_code')
            else:
                # 使用默认策略池配置
                strategy_pool_section = f'''# 全部策略混合
strategy_pool = [
    dict(
        name='{backtest_name}',
        strategy_list=[
        # ===========================================================
        # !!! 实盘前先回测一下，不要无脑跑案例策略，没人能保证案例策略能赚钱 !!!
        # 以下配置非官方提供的案例策略，这里只是举例如何配置，具体配置请自行处理
        # ===========================================================
        {{
            # 策略名称。与strategy目录中的策略文件名保持一致。
            "strategy": "Strategy_Spot_80",
            "offset_list": [0],
            "hold_period": "24H",
            "is_use_spot": False,
            # 资金权重。程序会自动根据这个权重计算你的策略占比，具体可以看1.8的直播讲解
            "cap_weight": 1,
            # 选币数量，1，2，3等数字代表选币的个数，0.1，0.2代表选币的比例。
            # 选币可以为0。空头为0代表纯多，多头为0代表纯空。
            "long_cap_weight": 1,  # 策略内多头资金权重
            "short_cap_weight": 0,  # 策略内空头资金权重
            "long_select_coin_num": 0.1,
            "short_select_coin_num": 0,
            # 选币因子信息列表，用于`2_选币_单offset.py`，`3_计算多offset资金曲线.py`共用计算资金曲线
            "factor_list": [
                ('PctChange', True, 80, 1)  # 多头因子名（和factors文件中相同），排序方式，参数，权重。
            ],
            "filter_list": [
                ('PctChange', 80)  # 因子名（和factors文件中相同），参数
            ],
            "use_custom_func": False  # 使用系统内置因子计算、过滤函数
        }}]
    )
]'''

            leverage_value = config_data.get('leverage', 1)
            get_kline_num = config_data.get('get_kline_num', 5000)

            # 处理黑白名单配置
            black_list = config_data.get('black_list', [])
            white_list = config_data.get('white_list', [])

            # 格式化黑白名单为Python列表格式
            black_list_str = str(black_list) if black_list else '[]'
            white_list_str = str(white_list) if white_list else '[]'

            config_content = f'''# ====================================================================================================
# ** 账户配置 **
# ====================================================================================================
{account_config_str}

# ====================================================================================================
# ** 策略细节配置 **
# 案例策略，需要自己探索，不保证可用
# ====================================================================================================
backtest_name = '{backtest_name}'  # 策略名称。用于标识当前运行的策略配置
get_kline_num = {get_kline_num}  # 获取K线数量。日线策略代表日线K线数量，小时策略代表小时K线数量
strategy_config = {{
    'name': '{strategy_name}',  # *必填。资金平衡方式，用于管理账户资金分配策略
    'hold_period': '{hold_period}',  # *必填。资金平衡周期。资金重新平衡的时间间隔，例：1H，6H，3D，7D......
    'params': {{  # 非必填。资金平衡策略的参数配置
        'cap_ratios': [1.0]
    }}
}}

{strategy_pool_section}

leverage = {leverage_value}  # 杠杆数。我看哪个赌狗要把这里改成大于1的。高杠杆如梦幻泡影。不要想着一夜暴富，脚踏实地赚自己该赚的钱。
black_list = {black_list_str}  # 拉黑名单，永远不会交易。不喜欢的币、异常的币。例：LUNA-USDT, 这里与实盘不太一样，需要有'-'
white_list = {white_list_str}  # 如果不为空，即只交易这些币，只在这些币当中进行选币。例：LUNA-USDT, 这里与实盘不太一样，需要有'-'
'''

            # 确保accounts目录存在
            self.accounts_dir.mkdir(exist_ok=True)

            # 写入文件
            with open(account_file, 'w', encoding='utf-8') as f:
                f.write(config_content)

            return {"success": True, "message": f"账户配置 {account_name} 保存成功"}

        except Exception as e:
            return {"success": False, "message": f"保存失败: {str(e)}"}

    def delete_account_config(self, account_name: str) -> Dict[str, Any]:
        """删除账户配置"""
        try:
            account_file = self.accounts_dir / f"{account_name}.py"
            if account_file.exists():
                account_file.unlink()
                return {"success": True, "message": f"账户配置 {account_name} 删除成功"}
            else:
                return {"success": False, "message": "账户配置文件不存在"}
        except Exception as e:
            return {"success": False, "message": f"删除失败: {str(e)}"}

    def rename_account_config(self, old_name: str, new_name: str) -> Dict[str, Any]:
        """重命名账户配置"""
        try:
            # 验证新名称的合法性
            if not new_name or not new_name.strip():
                return {"success": False, "message": "新账户名称不能为空"}

            # 清理新名称，移除非法字符
            new_name = re.sub(r'[<>:"/\\|?*]', '', new_name.strip())
            if not new_name:
                return {"success": False, "message": "账户名称包含非法字符"}

            old_file = self.accounts_dir / f"{old_name}.py"
            new_file = self.accounts_dir / f"{new_name}.py"

            # 检查原文件是否存在
            if not old_file.exists():
                return {"success": False, "message": "原账户配置文件不存在"}

            # 检查新文件名是否已存在
            if new_file.exists():
                return {"success": False, "message": "新账户名称已存在"}

            # 读取原文件内容并更新账户名称
            with open(old_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 更新文件中的账户名称引用
            content = content.replace(f"name='{old_name}'", f"name='{new_name}'")
            content = content.replace(f'name="{old_name}"', f'name="{new_name}"')

            # 写入新文件
            with open(new_file, 'w', encoding='utf-8') as f:
                f.write(content)

            # 删除原文件
            old_file.unlink()

            return {"success": True, "message": f"账户 {old_name} 重命名为 {new_name} 成功"}

        except Exception as e:
            return {"success": False, "message": f"重命名失败: {str(e)}"}

    def toggle_account_status(self, account_name: str, enable: bool) -> Dict[str, Any]:
        """启用或禁用账户配置"""
        try:
            # 确保accounts_disabled目录存在
            disabled_dir = self.accounts_dir.parent / "accounts_disabled"
            disabled_dir.mkdir(exist_ok=True)

            if enable:
                # 启用账户：从accounts_disabled目录移回accounts目录
                disabled_file = disabled_dir / f"{account_name}.py"
                enabled_file = self.accounts_dir / f"{account_name}.py"

                if not disabled_file.exists():
                    return {"success": False, "message": "禁用的账户配置文件不存在"}

                if enabled_file.exists():
                    return {"success": False, "message": "账户配置文件已存在于主目录"}

                # 移动文件
                disabled_file.rename(enabled_file)
                action = "启用"
            else:
                # 禁用账户：从accounts目录移到accounts_disabled目录
                enabled_file = self.accounts_dir / f"{account_name}.py"
                disabled_file = disabled_dir / f"{account_name}.py"

                if not enabled_file.exists():
                    return {"success": False, "message": "账户配置文件不存在"}

                if disabled_file.exists():
                    return {"success": False, "message": "账户配置文件已存在于禁用目录"}

                # 移动文件
                enabled_file.rename(disabled_file)
                action = "禁用"

            return {"success": True, "message": f"账户 {account_name} {action}成功"}

        except Exception as e:
            return {"success": False, "message": f"{action}失败: {str(e)}"}

    def get_account_status(self, account_name: str) -> Dict[str, Any]:
        """获取账户状态（启用/禁用）"""
        try:
            enabled_file = self.accounts_dir / f"{account_name}.py"
            disabled_file = self.accounts_dir.parent / "accounts_disabled" / f"{account_name}.py"

            if enabled_file.exists():
                return {"enabled": True, "exists": True}
            elif disabled_file.exists():
                return {"enabled": False, "exists": True}
            else:
                return {"enabled": False, "exists": False}

        except Exception as e:
            return {"enabled": False, "exists": False, "error": str(e)}

class FundCurveManager:
    """资金曲线管理器 - 负责处理资金曲线数据的读取、解析和计算"""

    def __init__(self):
        self.project_root = PROJECT_ROOT
        self.data_dir = PROJECT_ROOT / "data"
        self.backtest_results_dir = self.data_dir / "子策略回测结果"

    def find_equity_files(self, root_dir: Path = None, max_depth: int = None) -> List[str]:
        """递归查找指定目录下的资金曲线CSV文件"""
        if root_dir is None:
            root_dir = self.backtest_results_dir

        if not root_dir.exists():
            return []

        equity_files = []
        pattern = str(root_dir / "**" / "资金曲线.csv")

        for file_path in glob.glob(pattern, recursive=True):
            # 计算相对于根目录的深度
            rel_path = os.path.relpath(file_path, root_dir)
            depth = len(rel_path.split(os.sep)) - 1  # 减1是因为文件名本身占一层

            # 如果指定了最大深度且深度超过最大深度，则跳过
            if max_depth is not None and depth > max_depth:
                continue

            equity_files.append(file_path)

        return equity_files

    def get_directory_levels(self, file_path: str, root_dir: Path) -> List[str]:
        """获取文件路径相对于根目录的各级目录名"""
        rel_path = os.path.relpath(file_path, root_dir)
        directories = rel_path.split(os.sep)[:-1]  # 排除文件名
        return directories

    def generate_strategy_options(self, equity_files: List[str], root_dir: Path = None, level: int = 1) -> Dict[str, str]:
        """根据选定的目录级别生成策略选项名称"""
        if root_dir is None:
            root_dir = self.backtest_results_dir

        options = {}
        for file_path in equity_files:
            directories = self.get_directory_levels(file_path, root_dir)
            if len(directories) >= level:
                # 使用选定级别的目录名作为显示名称
                display_name = " > ".join(directories[:level])
                # 如果有重名，加上后面的路径区分
                if display_name in options:
                    full_path = " > ".join(directories)
                    options[full_path] = file_path
                else:
                    options[display_name] = file_path
            else:
                # 如果目录层级不足，则使用相对路径
                rel_path = os.path.relpath(file_path, root_dir)
                options[rel_path] = file_path

        return options

    def get_available_strategies(self) -> Dict[str, Any]:
        """获取所有可用的策略列表"""
        try:
            equity_files = self.find_equity_files()

            if not equity_files:
                return {
                    "success": True,
                    "strategies": [],
                    "message": "未找到任何资金曲线文件"
                }

            # 生成策略选项
            strategy_options = self.generate_strategy_options(equity_files, level=2)

            strategies = []
            for display_name, file_path in strategy_options.items():
                try:
                    # 读取文件基本信息
                    file_stat = os.stat(file_path)

                    # 尝试读取CSV文件获取数据范围
                    df = pd.read_csv(file_path)
                    if 'candle_begin_time' in df.columns and '净值' in df.columns:
                        df['candle_begin_time'] = pd.to_datetime(df['candle_begin_time'])
                        start_time = df['candle_begin_time'].min()
                        end_time = df['candle_begin_time'].max()
                        data_points = len(df)

                        # 计算基本指标
                        initial_value = df['净值'].iloc[0]
                        final_value = df['净值'].iloc[-1]
                        total_return = (final_value / initial_value - 1) * 100

                        strategies.append({
                            "name": display_name,
                            "file_path": file_path,
                            "start_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
                            "end_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
                            "data_points": data_points,
                            "total_return": round(total_return, 2),
                            "file_size": file_stat.st_size,
                            "modified_time": datetime.fromtimestamp(file_stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                        })
                    else:
                        strategies.append({
                            "name": display_name,
                            "file_path": file_path,
                            "error": "文件格式不正确，缺少必要的列"
                        })

                except Exception as e:
                    strategies.append({
                        "name": display_name,
                        "file_path": file_path,
                        "error": f"读取文件失败: {str(e)}"
                    })

            return {
                "success": True,
                "strategies": strategies,
                "total_count": len(strategies)
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"获取策略列表失败: {str(e)}"
            }

    def delete_strategy_file(self, file_path: str) -> Dict[str, Any]:
        """删除指定的策略目录（包含资金曲线文件）"""
        try:
            if not os.path.exists(file_path):
                return {
                    "success": False,
                    "message": "文件不存在"
                }

            # 检查文件是否在允许的目录范围内
            abs_file_path = os.path.abspath(file_path)
            abs_backtest_dir = os.path.abspath(self.backtest_results_dir)

            if not abs_file_path.startswith(abs_backtest_dir):
                return {
                    "success": False,
                    "message": "不允许删除此目录外的文件"
                }

            # 检查是否为资金曲线文件
            if not abs_file_path.endswith('资金曲线.csv'):
                return {
                    "success": False,
                    "message": "只能删除资金曲线.csv文件"
                }

            # 获取策略目录（资金曲线.csv文件的父目录）
            strategy_dir = os.path.dirname(file_path)
            abs_strategy_dir = os.path.abspath(strategy_dir)
            
            # 确保不会删除根目录 data/子策略回测结果/
            if abs_strategy_dir == abs_backtest_dir:
                return {
                    "success": False,
                    "message": "不能删除根目录"
                }

            # 获取策略组目录（策略目录的父目录，如 BEL_黄果树混合）
            strategy_group_dir = os.path.dirname(strategy_dir)
            abs_strategy_group_dir = os.path.abspath(strategy_group_dir)

            # 删除整个策略目录
            shutil.rmtree(strategy_dir)

            # 检查策略组目录是否只剩下通用文件，如果是则删除整个策略组目录
            try:
                # 确保策略组目录不是根目录 data/子策略回测结果/
                if (abs_strategy_group_dir != abs_backtest_dir and 
                    os.path.exists(strategy_group_dir)):
                    
                    remaining_items = os.listdir(strategy_group_dir)
                    
                    # 定义通用文件列表（这些文件不算作子策略）
                    common_files = {'仓位比例.csv', '仓位比例-原始.csv'}
                    
                    # 检查是否只剩下通用文件
                    remaining_dirs = [item for item in remaining_items 
                                    if os.path.isdir(os.path.join(strategy_group_dir, item))]
                    remaining_other_files = [item for item in remaining_items 
                                           if item not in common_files and 
                                           os.path.isfile(os.path.join(strategy_group_dir, item))]
                    
                    # 如果没有子策略目录且没有其他重要文件，则删除整个策略组目录
                    if not remaining_dirs and not remaining_other_files:
                        shutil.rmtree(strategy_group_dir)
                        return {
                            "success": True,
                            "message": "策略目录及策略组目录删除成功"
                        }
            except OSError:
                pass  # 如果删除失败，忽略错误

            return {
                "success": True,
                "message": "策略目录删除成功"
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"删除策略目录失败: {str(e)}"
            }

    def get_strategy_data(self, file_path: str, start_time: str = None, end_time: str = None) -> Dict[str, Any]:
        """获取指定策略的资金曲线数据"""
        try:
            if not os.path.exists(file_path):
                return {
                    "success": False,
                    "message": "文件不存在"
                }

            # 读取CSV文件
            df = pd.read_csv(file_path)

            # 检查必要的列
            if 'candle_begin_time' not in df.columns or '净值' not in df.columns:
                return {
                    "success": False,
                    "message": "文件格式不正确，缺少必要的列"
                }

            # 转换时间列
            df['candle_begin_time'] = pd.to_datetime(df['candle_begin_time'])
            if df['candle_begin_time'].dt.tz is not None:
                df['candle_begin_time'] = df['candle_begin_time'].dt.tz_localize(None)

            # 时间范围过滤
            if start_time:
                start_datetime = pd.to_datetime(start_time)
                df = df[df['candle_begin_time'] >= start_datetime]

            if end_time:
                end_datetime = pd.to_datetime(end_time)
                df = df[df['candle_begin_time'] <= end_datetime]

            if len(df) == 0:
                return {
                    "success": False,
                    "message": "所选时间范围内没有数据"
                }

            # 重新计算净值，从1开始
            initial_value = df['净值'].iloc[0]
            df['重置净值'] = df['净值'] / initial_value

            # 计算关键指标
            final_equity = df['重置净值'].iloc[-1]
            days = (df['candle_begin_time'].iloc[-1] - df['candle_begin_time'].iloc[0]).days

            # 年化收益率（按照参考代码的计算方式）
            if days > 0:
                annual_return = (final_equity / 1.0) ** (365 / days) - 1
            else:
                annual_return = 0

            # 最大回撤
            df['净值_cummax'] = df['重置净值'].cummax()
            df['drawdown'] = (df['重置净值'] - df['净值_cummax']) / df['净值_cummax']
            max_drawdown = df['drawdown'].min()

            # 当前回撤
            current_drawdown = df['drawdown'].iloc[-1]

            # 收益率标准差（按照参考代码的计算方式）
            df['daily_return'] = df['重置净值'].pct_change()
            std_dev = df['daily_return'].std() * (252 ** 0.5)  # 年化标准差

            # 夏普比率计算（使用无风险利率，通常为3%年化）
            risk_free_rate = 0.03  # 3%无风险利率
            sharpe_ratio = (annual_return - risk_free_rate) / std_dev if std_dev > 0 else 0

            # 准备图表数据 - 使用Chart.js时间轴格式
            chart_data = {
                "labels": df['candle_begin_time'].dt.strftime('%Y-%m-%d %H:%M:%S').tolist(),
                "datasets": [{
                    "label": "净值",
                    "data": [{"x": time_str, "y": value} for time_str, value in
                            zip(df['candle_begin_time'].dt.strftime('%Y-%m-%d %H:%M:%S'), df['重置净值'])],
                    "borderColor": "#007bff",
                    "backgroundColor": "#007bff20",
                    "fill": False,
                    "tension": 0.1
                }]
            }

            # 计算更多统计指标（参考标准代码）

            # 找到最大回撤的开始和结束时间
            max_dd_end_idx = df['drawdown'].idxmin()
            max_dd_end_time = df.loc[max_dd_end_idx, 'candle_begin_time']

            # 找到最大回撤开始时间（净值达到高点的时间）
            temp_df = df.loc[:max_dd_end_idx]
            max_dd_start_idx = temp_df['重置净值'].idxmax()
            max_dd_start_time = df.loc[max_dd_start_idx, 'candle_begin_time']

            # 计算回撤持续天数
            max_dd_duration = (max_dd_end_time - max_dd_start_time).total_seconds() / (24 * 3600)

            # 计算最长不创新高时间
            df['is_new_high'] = df['重置净值'] >= df['重置净值'].cummax()
            df['high_group'] = (df['is_new_high'] != df['is_new_high'].shift()).cumsum()

            # 找出每个非新高组的开始和结束时间
            no_new_high_periods = []
            for group in df[~df['is_new_high']]['high_group'].unique():
                group_data = df[df['high_group'] == group]
                if not group_data['is_new_high'].any():  # 确保这是一个非新高组
                    start_time_period = group_data['candle_begin_time'].min()
                    end_time_period = group_data['candle_begin_time'].max()
                    duration = (end_time_period - start_time_period).total_seconds() / (24 * 3600)
                    no_new_high_periods.append((start_time_period, end_time_period, duration))

            # 找出最长的不创新高期间
            if no_new_high_periods:
                longest_period = max(no_new_high_periods, key=lambda x: x[2])
                longest_no_new_high_duration = longest_period[2]
            else:
                longest_no_new_high_duration = 0

            # 找出最新一次新高时间
            if df['is_new_high'].any():
                latest_high_idx = df[df['is_new_high']].index[-1]
                latest_high_time = df.loc[latest_high_idx, 'candle_begin_time']
                latest_high_time_str = latest_high_time.strftime('%Y-%m-%d %H:%M:%S')
                days_since_high = (df['candle_begin_time'].iloc[-1] - latest_high_time).total_seconds() / (24 * 3600)
            else:
                latest_high_time_str = "无"
                days_since_high = days  # 如果没有新高，则距离前高天数等于总天数

            # 格式化最大回撤开始和结束时间
            max_dd_start_time_str = max_dd_start_time.strftime('%Y-%m-%d %H:%M:%S')
            max_dd_end_time_str = max_dd_end_time.strftime('%Y-%m-%d %H:%M:%S')

            # 计算最长不创新高的开始和结束时间
            if no_new_high_periods:
                longest_period = max(no_new_high_periods, key=lambda x: x[2])
                longest_no_new_high_start_str = longest_period[0].strftime('%Y-%m-%d %H:%M:%S')
                longest_no_new_high_end_str = longest_period[1].strftime('%Y-%m-%d %H:%M:%S')
            else:
                longest_no_new_high_start_str = "无"
                longest_no_new_high_end_str = "无"

            # 计算距离最大回撤空间
            # 公式：(100-最大回撤%)/(100-当前回撤%)-1
            max_dd_pct = abs(max_drawdown) * 100
            current_dd_pct = abs(current_drawdown) * 100

            if current_dd_pct < 100:  # 避免除零错误
                drawdown_space = (100 - max_dd_pct) / (100 - current_dd_pct) - 1
            else:
                drawdown_space = 0  # 如果当前回撤已经100%，则空间为0

            # 计算统计指标（使用参考代码的标准字段名称）
            statistics = {
                "总收益": round((final_equity - 1) * 100, 2),
                "年化收益": round(annual_return * 100, 2),
                "最大回撤": round(abs(max_drawdown) * 100, 2),  # 使用绝对值，显示为正数
                "当前回撤": round(abs(current_drawdown) * 100, 2),
                "距离最大回撤空间": round(drawdown_space * 100, 2),  # 新增指标，转换为百分比
                "收益率标准差": round(std_dev, 4),  # 按照参考代码，保留4位小数
                "夏普比率": round(sharpe_ratio, 2),  # 修正后的夏普比率
                "最大回撤持续天数": round(max_dd_duration, 1),
                "最长不创新高天数": round(longest_no_new_high_duration, 1),
                "距离前高天数": round(days_since_high, 1),
                "最新一次新高时间": latest_high_time_str,
                "最大回撤开始时间": max_dd_start_time_str,
                "最大回撤结束时间": max_dd_end_time_str,
                "最长不创新高开始时间": longest_no_new_high_start_str,
                "最长不创新高结束时间": longest_no_new_high_end_str,
                "交易天数": days,
                "数据点数": len(df),
                "开始时间": df['candle_begin_time'].iloc[0].strftime('%Y-%m-%d %H:%M:%S'),
                "结束时间": df['candle_begin_time'].iloc[-1].strftime('%Y-%m-%d %H:%M:%S')
            }

            # 添加最大回撤区域标注数据
            max_drawdown_annotation = {
                "start_time": max_dd_start_time_str,
                "end_time": max_dd_end_time_str,
                "start_index": None,
                "end_index": None,
                "drawdown_value": round(abs(max_drawdown) * 100, 2),
                "duration_days": round(max_dd_duration, 1),
                "start_equity": None,
                "lowest_equity": None,
                "current_equity": round(df['重置净值'].iloc[-1], 4),
                "is_recovered": 0
            }

            # 找到对应的数据点索引和净值信息
            try:
                start_idx = df[df['candle_begin_time'] == max_dd_start_time].index[0]
                end_idx = df[df['candle_begin_time'] == max_dd_end_time].index[0]
                max_drawdown_annotation["start_index"] = df.index.get_loc(start_idx)
                max_drawdown_annotation["end_index"] = df.index.get_loc(end_idx)

                # 获取净值信息
                start_equity = df.loc[start_idx, '重置净值']
                lowest_equity = df.loc[end_idx, '重置净值']
                max_drawdown_annotation["start_equity"] = round(start_equity, 4)
                max_drawdown_annotation["lowest_equity"] = round(lowest_equity, 4)

                # 判断是否已恢复到回撤前水平
                current_equity = df['重置净值'].iloc[-1]
                max_drawdown_annotation["is_recovered"] = int(current_equity >= start_equity)

            except (IndexError, KeyError):
                # 如果找不到精确匹配，使用近似索引
                pass

            return {
                "success": True,
                "chart_data": chart_data,
                "statistics": statistics,
                "max_drawdown_annotation": max_drawdown_annotation
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"读取策略数据失败: {str(e)}"
            }

    def compare_strategies(self, file_paths: List[str], start_time: str = None, end_time: str = None) -> Dict[str, Any]:
        """对比多个策略的资金曲线"""
        try:
            if not file_paths:
                return {
                    "success": False,
                    "message": "未选择任何策略"
                }

            # 如果没有指定时间范围，计算所有策略的交集时间范围
            if not start_time or not end_time:
                strategy_time_ranges = []

                for file_path in file_paths:
                    try:
                        df = pd.read_csv(file_path)
                        df['candle_begin_time'] = pd.to_datetime(df['candle_begin_time'])
                        if df['candle_begin_time'].dt.tz is not None:
                            df['candle_begin_time'] = df['candle_begin_time'].dt.tz_localize(None)

                        strategy_start = df['candle_begin_time'].min()
                        strategy_end = df['candle_begin_time'].max()
                        strategy_time_ranges.append((strategy_start, strategy_end))
                    except Exception as e:
                        continue

                if strategy_time_ranges:
                    # 计算交集时间范围
                    all_starts = [time_range[0] for time_range in strategy_time_ranges]
                    all_ends = [time_range[1] for time_range in strategy_time_ranges]
                    intersection_start = max(all_starts)
                    intersection_end = min(all_ends)

                    if intersection_start <= intersection_end:
                        start_time = intersection_start.strftime('%Y-%m-%dT%H:%M:%S')
                        end_time = intersection_end.strftime('%Y-%m-%dT%H:%M:%S')

            strategies_data = []
            chart_data = {
                "labels": [],
                "datasets": []
            }

            # 颜色列表用于区分不同策略
            colors = [
                '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
                '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
            ]

            # 生成策略名称映射
            equity_files = self.find_equity_files()
            strategy_options = self.generate_strategy_options(equity_files, level=2)
            file_to_name = {file_path: display_name for display_name, file_path in strategy_options.items()}

            for i, file_path in enumerate(file_paths):
                strategy_result = self.get_strategy_data(file_path, start_time, end_time)

                if strategy_result["success"]:
                    # 使用与策略列表一致的名称生成方式
                    strategy_name = file_to_name.get(file_path, os.path.basename(os.path.dirname(file_path)))

                    # 添加到策略数据列表
                    strategies_data.append({
                        "name": strategy_name,
                        "statistics": strategy_result["statistics"]
                    })

                    # 添加到图表数据
                    if i == 0:
                        # 第一个策略设置时间标签
                        chart_data["labels"] = strategy_result["chart_data"]["labels"]

                    # 从新的数据格式中提取净值数据
                    equity_data = strategy_result["chart_data"]["datasets"][0]["data"]

                    chart_data["datasets"].append({
                        "label": strategy_name,
                        "data": equity_data,
                        "borderColor": colors[i % len(colors)],
                        "backgroundColor": colors[i % len(colors)] + '20',  # 添加透明度
                        "fill": False,
                        "tension": 0.1
                    })

            return {
                "success": True,
                "chart_data": chart_data,
                "strategies": strategies_data,
                "total_strategies": len(strategies_data)
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"策略对比失败: {str(e)}"
            }

    def analyze_drawdown_events(self, file_path: str, start_time: str = None, end_time: str = None,
                               min_drawdown: float = 0.5, max_drawdown: float = 20,
                               min_drawdown_days: float = 0, max_drawdown_days: float = 30,
                               min_high_interval: float = 0, max_high_interval: float = 100) -> Dict[str, Any]:
        """分析回撤事件 - 参考参考代码的回撤事件检测算法"""
        try:
            if not os.path.exists(file_path):
                return {
                    "success": False,
                    "message": "文件不存在"
                }

            # 读取CSV文件
            df = pd.read_csv(file_path)

            # 检查必要的列
            if 'candle_begin_time' not in df.columns or '净值' not in df.columns:
                return {
                    "success": False,
                    "message": "文件格式不正确，缺少必要的列"
                }

            # 转换时间列
            df['candle_begin_time'] = pd.to_datetime(df['candle_begin_time'])
            if df['candle_begin_time'].dt.tz is not None:
                df['candle_begin_time'] = df['candle_begin_time'].dt.tz_localize(None)

            # 时间范围过滤
            if start_time:
                start_datetime = pd.to_datetime(start_time)
                df = df[df['candle_begin_time'] >= start_datetime]

            if end_time:
                end_datetime = pd.to_datetime(end_time)
                df = df[df['candle_begin_time'] <= end_datetime]

            if len(df) == 0:
                return {
                    "success": False,
                    "message": "指定时间范围内没有数据"
                }

            # 重置净值（从1开始）
            df = df.copy()
            initial_value = df['净值'].iloc[0]
            df['重置净值'] = df['净值'] / initial_value

            # 检测新高点和回撤事件 - 参考参考代码算法
            drawdown_events = []

            # 识别所有新高点
            df['is_new_high'] = df['重置净值'] >= df['重置净值'].cummax()
            df['high_group'] = (df['is_new_high'] != df['is_new_high'].shift()).cumsum()

            # 找出所有新高点
            high_points = []
            for idx, row in df[df['is_new_high']].iterrows():
                # 只记录每组新高的最后一个点（即该组的最高点）
                group = row['high_group']
                group_data = df[df['high_group'] == group]
                if idx == group_data.index[-1]:  # 如果是该组的最后一个点
                    high_points.append({
                        'time': row['candle_begin_time'],
                        'value': row['重置净值'],
                        'index': idx
                    })

            # 检测回撤事件（从一个新高到下一个新高之间的最大回撤）
            if len(high_points) >= 2:
                for i in range(len(high_points) - 1):
                    current_high = high_points[i]
                    next_high = high_points[i + 1]

                    # 获取两个新高之间的数据
                    period_data = df.loc[current_high['index']:next_high['index']]

                    if len(period_data) > 1:  # 确保有足够的数据点
                        # 找到期间最低点
                        min_idx = period_data['重置净值'].idxmin()
                        min_value = period_data.loc[min_idx, '重置净值']
                        min_time = period_data.loc[min_idx, 'candle_begin_time']

                        # 计算最大回撤
                        max_drawdown_pct = (min_value - current_high['value']) / current_high['value'] * 100

                        # 计算新高间隔天数
                        high_interval = (next_high['time'] - current_high['time']).total_seconds() / (24 * 3600)

                        # 计算从回撤最低点到下一个新高的天数
                        recovery_duration = (next_high['time'] - min_time).total_seconds() / (24 * 3600)

                        # 计算从回撤开始到最低点的天数（真正的回撤持续天数）
                        drawdown_duration = (min_time - current_high['time']).total_seconds() / (24 * 3600)

                        # 检查是否满足筛选条件
                        if (abs(max_drawdown_pct) >= min_drawdown and
                            abs(max_drawdown_pct) <= max_drawdown and
                            drawdown_duration >= min_drawdown_days and
                            drawdown_duration <= max_drawdown_days and
                            high_interval >= min_high_interval and
                            high_interval <= max_high_interval):

                            drawdown_events.append({
                                'dd_start_time': current_high['time'].strftime('%Y-%m-%d %H:%M:%S'),
                                'dd_lowest_time': min_time.strftime('%Y-%m-%d %H:%M:%S'),
                                'dd_end_time': next_high['time'].strftime('%Y-%m-%d %H:%M:%S'),
                                'max_drawdown': max_drawdown_pct,
                                'drawdown_duration': drawdown_duration,
                                'recovery_duration': recovery_duration,
                                'high_interval': high_interval,
                                'is_last_point': False
                            })

            # 添加最后一个高点到当前数据点的回撤（如果存在）
            if len(high_points) > 0:
                last_high = high_points[-1]
                # 获取最后一个高点之后的数据
                last_period_data = df.loc[last_high['index']:]

                if len(last_period_data) > 1:  # 确保有足够的数据点
                    # 计算当前回撤
                    current_value = last_period_data['重置净值'].iloc[-1]
                    current_time = last_period_data['candle_begin_time'].iloc[-1]

                    # 找到期间最低点
                    min_idx = last_period_data['重置净值'].idxmin()
                    min_value = last_period_data.loc[min_idx, '重置净值']
                    min_time = last_period_data.loc[min_idx, 'candle_begin_time']

                    # 计算最大回撤
                    max_drawdown_pct = (min_value - last_high['value']) / last_high['value'] * 100

                    # 计算回撤持续天数（从最后高点到最低点）
                    drawdown_duration = (min_time - last_high['time']).total_seconds() / (24 * 3600)

                    # 计算从最低点到当前的天数
                    recovery_duration = (current_time - min_time).total_seconds() / (24 * 3600)

                    # 计算从最后高点到当前的天数
                    high_interval = (current_time - last_high['time']).total_seconds() / (24 * 3600)

                    # 如果当前不是新高且回撤满足条件
                    if (current_value < last_high['value'] and
                        abs(max_drawdown_pct) >= min_drawdown and
                        abs(max_drawdown_pct) <= max_drawdown and
                        drawdown_duration >= min_drawdown_days and
                        drawdown_duration <= max_drawdown_days and
                        high_interval >= min_high_interval and
                        high_interval <= max_high_interval):

                        drawdown_events.append({
                            'dd_start_time': last_high['time'].strftime('%Y-%m-%d %H:%M:%S'),
                            'dd_lowest_time': min_time.strftime('%Y-%m-%d %H:%M:%S'),
                            'dd_end_time': current_time.strftime('%Y-%m-%d %H:%M:%S'),
                            'max_drawdown': max_drawdown_pct,
                            'drawdown_duration': drawdown_duration,
                            'recovery_duration': recovery_duration,
                            'high_interval': high_interval,
                            'is_last_point': True
                        })

            return {
                "success": True,
                "events": drawdown_events,
                "total_events": len(drawdown_events),
                "filter_conditions": {
                    "min_drawdown": min_drawdown,
                    "max_drawdown": max_drawdown,
                    "min_drawdown_days": min_drawdown_days,
                    "max_drawdown_days": max_drawdown_days,
                    "min_high_interval": min_high_interval,
                    "max_high_interval": max_high_interval
                }
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"分析回撤事件失败: {str(e)}"
            }

# 初始化管理器
if ENHANCED_PROCESS_MANAGER_ENABLED:
    process_manager = EnhancedProcessManager()
    print("✓ 使用增强进程管理器")
else:
    process_manager = ProcessManager()
    print("✓ 使用原有进程管理器")

config_manager = ConfigManager()
fund_curve_manager = FundCurveManager()

# Web路由定义
@app.route('/')
@login_required if AUTH_ENABLED else lambda f: f
def index():
    """主页 - 程序控制面板"""
    try:
        status = process_manager.get_status()
        global_config = config_manager.get_global_config()
        accounts = config_manager.get_account_list()

        # 检查是否使用现代化界面
        use_modern = request.args.get('modern', 'true').lower() == 'true'
        template_name = 'index_modern.html' if use_modern else 'index.html'

        # 添加用户信息到模板上下文
        template_context = {
            'status': status,
            'global_config': global_config,
            'accounts': accounts
        }

        if AUTH_ENABLED and g.get('current_user'):
            template_context['current_user'] = g.current_user
            template_context['user_permissions'] = user_manager.get_user_permissions(g.current_user.get('user_name'))
            # 添加CSRF token用于Google认证
            template_context['csrf_token'] = security_manager.generate_csrf_token()
            session['csrf_token'] = template_context['csrf_token']

        return render_template(template_name, **template_context)
    except Exception:
        # 默认使用现代化界面
        try:
            template_context = {
                'status': None,
                'global_config': {},
                'accounts': []
            }

            if AUTH_ENABLED and g.get('current_user'):
                template_context['current_user'] = g.current_user
                template_context['user_permissions'] = user_manager.get_user_permissions(g.current_user.get('user_name'))

            return render_template('index_modern.html', **template_context)
        except Exception as e:
            return f"页面加载失败: {str(e)}", 500

@app.route('/config')
@login_required if AUTH_ENABLED else lambda f: f
@permission_required('global_config') if AUTH_ENABLED else lambda f: f
def config_page():
    """配置管理页面"""
    try:
        global_config = config_manager.get_global_config()
        accounts = config_manager.get_account_list()

        # 检查是否使用现代化界面
        use_modern = request.args.get('modern', 'true').lower() == 'true'
        template_name = 'config_modern.html' if use_modern else 'config.html'

        # 添加用户信息到模板上下文
        template_context = {
            'global_config': global_config,
            'accounts': accounts
        }

        if AUTH_ENABLED and g.get('current_user'):
            template_context['current_user'] = g.current_user
            template_context['user_permissions'] = user_manager.get_user_permissions(g.current_user.get('user_name'))

        return render_template(template_name, **template_context)
    except Exception:
        # 默认使用现代化界面
        try:
            template_context = {
                'global_config': {},
                'accounts': []
            }

            if AUTH_ENABLED and g.get('current_user'):
                template_context['current_user'] = g.current_user
                template_context['user_permissions'] = user_manager.get_user_permissions(g.current_user.get('user_name'))

            return render_template('config_modern.html', **template_context)
        except Exception as e:
            return f"页面加载失败: {str(e)}", 500

@app.route('/favicon.ico')
def favicon():
    """返回favicon，避免404错误"""
    return '', 204

@app.route('/design')
def design_showcase():
    """设计展示页面"""
    return render_template('design_showcase.html')

@app.route('/accounts')
@login_required if AUTH_ENABLED else lambda f: f
@permission_required('account_management') if AUTH_ENABLED else lambda f: f
def accounts_page():
    """账户管理页面"""
    try:
        accounts = config_manager.get_account_list()
        template_context = {'accounts': accounts}

        if AUTH_ENABLED and g.get('current_user'):
            template_context['current_user'] = g.current_user
            template_context['user_permissions'] = user_manager.get_user_permissions(g.current_user.get('user_name'))
            # 添加CSRF令牌
            template_context['csrf_token'] = session.get('csrf_token')

        return render_template('accounts_modern.html', **template_context)
    except Exception:
        template_context = {'accounts': []}

        if AUTH_ENABLED and g.get('current_user'):
            template_context['current_user'] = g.current_user
            template_context['user_permissions'] = user_manager.get_user_permissions(g.current_user.get('user_name'))
            # 添加CSRF令牌
            template_context['csrf_token'] = session.get('csrf_token')

        return render_template('accounts_modern.html', **template_context)

@app.route('/global-config')
@login_required if AUTH_ENABLED else lambda f: f
@permission_required('global_config') if AUTH_ENABLED else lambda f: f
def global_config_page():
    """全局配置页面"""
    try:
        global_config = config_manager.get_global_config()
        template_context = {'global_config': global_config}

        if AUTH_ENABLED and g.get('current_user'):
            template_context['current_user'] = g.current_user
            template_context['user_permissions'] = user_manager.get_user_permissions(g.current_user.get('user_name'))

        return render_template('global_config_modern.html', **template_context)
    except Exception:
        template_context = {'global_config': {}}

        if AUTH_ENABLED and g.get('current_user'):
            template_context['current_user'] = g.current_user
            template_context['user_permissions'] = user_manager.get_user_permissions(g.current_user.get('user_name'))

        return render_template('global_config_modern.html', **template_context)

@app.route('/fund-curve')
@login_required if AUTH_ENABLED else lambda f: f
@permission_required('fund_curve') if AUTH_ENABLED else lambda f: f
def fund_curve_page():
    """资金曲线页面"""
    try:
        template_context = {}

        if AUTH_ENABLED and g.get('current_user'):
            template_context['current_user'] = g.current_user
            template_context['user_permissions'] = user_manager.get_user_permissions(g.current_user.get('user_name'))

        return render_template('fund_curve_modern.html', **template_context)
    except Exception as e:
        return f"页面加载失败: {str(e)}", 500





@app.route('/api/status')
@login_required if AUTH_ENABLED else lambda f: f
def api_status():
    """API: 获取程序状态"""
    return jsonify(process_manager.get_status())

@app.route('/api/start', methods=['POST'])
@login_required if AUTH_ENABLED else lambda f: f
@permission_required('program_control') if AUTH_ENABLED else lambda f: f
@google_auth_required if AUTH_ENABLED else lambda f: f
def api_start():
    """API: 启动交易程序"""
    result = process_manager.start_trading()
    if result['success']:
        # 如果使用增强进程管理器，需要手动启动日志监控
        if ENHANCED_PROCESS_MANAGER_ENABLED and hasattr(process_manager, 'process') and process_manager.process:
            # 创建日志文件
            log_file = get_log_file_path()
            # 启动日志监控线程
            start_log_monitoring(process_manager.process, log_file)

        # 启动监控线程
        start_monitoring()
        # 立即发送状态更新到前端
        status = process_manager.get_status()
        socketio.emit('status_update', status)
    return jsonify(result)

@app.route('/api/stop', methods=['POST'])
@login_required if AUTH_ENABLED else lambda f: f
@permission_required('program_control') if AUTH_ENABLED else lambda f: f
@google_auth_required if AUTH_ENABLED else lambda f: f
def api_stop():
    """API: 停止交易程序"""
    # 使用增强的停止方法（如果可用）
    if ENHANCED_PROCESS_MANAGER_ENABLED and hasattr(process_manager, 'stop_trading_enhanced'):
        result = process_manager.stop_trading_enhanced()

        # 记录详细的停止日志
        app.logger.info(f"增强停止流程结果: {result}")

        # 如果增强停止成功，停止监控线程
        if result['success']:
            stop_monitoring()

        # 发送详细的状态更新到前端
        status = process_manager.get_status()
        socketio.emit('status_update', status)

        # 发送停止操作的详细结果到前端
        socketio.emit('stop_operation_result', {
            'success': result['success'],
            'message': result['message'],
            'details': {
                'stop_duration': result.get('stop_duration', 0),
                'processes_cleaned': result.get('cleanup_result', {}).get('processes_found', 0),
                'verification_passed': result.get('verification_result', {}).get('success', False)
            }
        })

    else:
        # 使用原有的停止方法
        result = process_manager.stop_trading()
        if result['success']:
            stop_monitoring()
            status = process_manager.get_status()
            socketio.emit('status_update', status)

    return jsonify(result)

@app.route('/api/schedule-debug', methods=['POST'])
def api_schedule_debug():
    """API: 启动调度调试"""
    try:
        # 检查是否在调试模式
        global_config = config_manager.get_global_config()
        if not global_config.get('is_debug', False):
            return jsonify({'success': False, 'message': '调度调试仅在调试模式下可用'})

        result = process_manager.start_schedule_debug()
        if result['success']:
            # 如果使用增强进程管理器，需要手动启动日志监控
            if ENHANCED_PROCESS_MANAGER_ENABLED and hasattr(process_manager, 'process') and process_manager.process:
                # 创建日志文件
                logs_dir = PROJECT_ROOT / "logs"
                logs_dir.mkdir(exist_ok=True)
                log_file = logs_dir / "schedule_debug.log"
                # 启动日志监控线程
                start_log_monitoring(process_manager.process, log_file)

            # 启动监控线程
            start_monitoring()
            # 立即发送状态更新到前端
            status = process_manager.get_status()
            socketio.emit('status_update', status)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'message': f'启动调度调试失败: {str(e)}'})

@app.route('/api/schedule-debug-test', methods=['POST'])
def api_schedule_debug_test():
    """API: 启动调度调试测试"""
    try:
        result = process_manager.start_schedule_debug_test()
        if result['success']:
            # 如果使用增强进程管理器，需要手动启动日志监控
            if ENHANCED_PROCESS_MANAGER_ENABLED and hasattr(process_manager, 'process') and process_manager.process:
                # 创建日志文件
                logs_dir = PROJECT_ROOT / "logs"
                logs_dir.mkdir(exist_ok=True)
                log_file = logs_dir / "schedule_debug_test.log"
                # 启动日志监控线程
                start_log_monitoring(process_manager.process, log_file)

            # 启动监控线程
            start_monitoring()
            # 立即发送状态更新到前端
            status = process_manager.get_status()
            socketio.emit('status_update', status)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'message': f'启动调度调试测试失败: {str(e)}'})

@app.route('/api/schedule-script', methods=['GET', 'POST'])
def api_schedule_script():
    """API: 获取或保存调度脚本内容"""
    try:
        if request.method == 'GET':
            # 读取 schedule_startup.py 文件内容
            script_path = PROJECT_ROOT / 'schedule_startup.py'
            if script_path.exists():
                with open(script_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                return jsonify({'success': True, 'content': content})
            else:
                return jsonify({'success': False, 'message': 'schedule_startup.py 文件不存在'})

        elif request.method == 'POST':
            # 保存脚本内容
            content = request.form.get('content', '')
            comment = request.form.get('comment', '')
            if not content:
                return jsonify({'success': False, 'message': '脚本内容不能为空'})

            script_path = PROJECT_ROOT / 'schedule_startup.py'

            # 使用备份管理器创建备份
            backup_manager = ScheduleScriptBackupManager()
            if script_path.exists():
                backup_result = backup_manager.create_backup(comment or "手动保存前备份")
                if not backup_result["success"]:
                    return jsonify({'success': False, 'message': f'创建备份失败: {backup_result["message"]}'})

            # 保存新内容
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(content)

            return jsonify({'success': True, 'message': '脚本保存成功'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'操作失败: {str(e)}'})

@app.route('/api/schedule-script/backups', methods=['GET', 'POST', 'DELETE'])
def api_schedule_script_backups():
    """API: 管理调度脚本备份"""
    try:
        backup_manager = ScheduleScriptBackupManager()

        if request.method == 'GET':
            # 获取备份列表
            backups = backup_manager.get_backup_list()
            return jsonify({'success': True, 'backups': backups})

        elif request.method == 'POST':
            action = request.form.get('action', '')

            if action == 'view':
                # 查看备份内容
                filename = request.form.get('filename', '')
                if not filename:
                    return jsonify({'success': False, 'message': '文件名不能为空'})

                result = backup_manager.get_backup_content(filename)
                return jsonify(result)

            elif action == 'restore':
                # 恢复备份
                filename = request.form.get('filename', '')
                if not filename:
                    return jsonify({'success': False, 'message': '文件名不能为空'})

                result = backup_manager.restore_backup(filename)
                return jsonify(result)

            elif action == 'cleanup':
                # 清理旧备份
                keep_count = request.form.get('keep_count', type=int)
                result = backup_manager.cleanup_old_backups(keep_count)
                return jsonify(result)

            else:
                return jsonify({'success': False, 'message': '未知操作'})

        elif request.method == 'DELETE':
            # 删除备份
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'message': '请求数据为空'})

            filenames = data.get('filenames', [])
            if not filenames:
                return jsonify({'success': False, 'message': '未指定要删除的文件'})

            if len(filenames) == 1:
                result = backup_manager.delete_backup(filenames[0])
            else:
                result = backup_manager.delete_multiple_backups(filenames)

            return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'message': f'操作失败: {str(e)}'})

@app.route('/api/toggle-debug', methods=['POST'])
def api_toggle_debug():
    """API: 切换调试模式"""
    try:
        debug_mode = request.form.get('debug_mode', 'false').lower() == 'true'

        # 获取当前配置
        current_config = config_manager.get_global_config()
        current_config['is_debug'] = debug_mode

        # 保存配置
        result = config_manager.save_global_config(current_config)

        if result['success']:
            mode_text = '调试模式' if debug_mode else '生产模式'
            result['message'] = f'已成功切换到{mode_text}'

        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'message': f'切换模式失败: {str(e)}'})

@app.route('/api/logs')
def api_logs():
    """API: 获取系统日志"""
    try:
        # 获取查询参数
        verbose_mode = request.args.get('verbose', 'false').lower() == 'true'
        silent_mode = request.args.get('silent', 'false').lower() == 'true'
        level_filter = request.args.get('level', 'all')  # all, error, warning, info, debug

        log_file = get_log_file_path()
        logs = []

        if os.path.exists(log_file):
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # 只处理最后1000行日志
            recent_lines = lines[-1000:] if len(lines) > 1000 else lines

            for line in recent_lines:
                if line.strip():
                    # 根据模式处理日志
                    if silent_mode:
                        # 静默模式：只显示错误和重要操作
                        processed_log = log_filter.process_message(line.strip(), verbose_mode=False)
                        if processed_log and processed_log['level'] in ['error', 'warning']:
                            logs.append(processed_log)
                    else:
                        # 普通模式或详细模式
                        processed_log = log_filter.process_message(line.strip(), verbose_mode=verbose_mode)
                        if processed_log:
                            # 应用级别过滤
                            if level_filter == 'all' or processed_log['level'] == level_filter:
                                logs.append(processed_log)

        return jsonify({'success': True, 'logs': logs})
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取日志失败: {str(e)}'})

@app.route('/api/log-settings', methods=['GET', 'POST'])
def api_log_settings():
    """API: 日志显示设置"""
    global log_filter

    if request.method == 'GET':
        return jsonify({
            'success': True,
            'settings': {
                'verbose_mode': getattr(log_filter, 'verbose_mode', False),
                'silent_mode': getattr(log_filter, 'silent_mode', False)
            }
        })

    elif request.method == 'POST':
        try:
            data = request.get_json()
            if data:
                log_filter.verbose_mode = data.get('verbose_mode', False)
                log_filter.silent_mode = data.get('silent_mode', False)

                # 通知前端更新设置
                socketio.emit('log_settings_updated', {
                    'verbose_mode': log_filter.verbose_mode,
                    'silent_mode': log_filter.silent_mode
                })

                return jsonify({'success': True, 'message': '日志设置已更新'})

            return jsonify({'success': False, 'message': '无效的请求数据'})
        except Exception as e:
            return jsonify({'success': False, 'message': f'更新设置失败: {str(e)}'})

@app.route('/api/config/global', methods=['GET', 'POST'])
def api_global_config():
    """API: 全局配置管理"""
    if request.method == 'GET':
        return jsonify(config_manager.get_global_config())
    else:
        config_data = request.get_json()
        result = config_manager.save_global_config(config_data)
        return jsonify(result)

@app.route('/api/config/reset', methods=['POST'])
def api_reset_config():
    """API: 重置配置为默认值"""
    try:
        # 这里可以添加重置配置的逻辑
        return jsonify({'success': True, 'message': '配置已重置为默认值'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# 删除重复的备份API端点



@app.route('/api/config/validate', methods=['POST'])
def api_validate_config():
    """API: 验证配置有效性"""
    try:
        config_data = request.get_json() if request.is_json else config_manager.get_global_config()
        validation_result = config_manager.validate_config(config_data)
        return jsonify(validation_result)
    except Exception as e:
        return jsonify({'success': False, 'message': f'验证失败: {str(e)}'})

@app.route('/api/config/backup', methods=['POST'])
def api_create_config_backup():
    """API: 创建配置备份"""
    try:
        result = config_manager.create_config_backup()
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/config/backups', methods=['GET'])
def api_list_config_backups():
    """API: 获取配置备份列表"""
    try:
        result = config_manager.list_config_backups()
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/config/backup-list', methods=['GET'])
def api_get_backup_list():
    """API: 获取配置备份列表 (兼容前端调用)"""
    try:
        result = config_manager.list_config_backups()
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/config/restore', methods=['POST'])
def api_restore_config_backup():
    """API: 恢复配置备份"""
    try:
        data = request.get_json()
        backup_filename = data.get('backup_filename')
        if not backup_filename:
            return jsonify({'success': False, 'message': '缺少备份文件名'})

        result = config_manager.restore_config_backup(backup_filename)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/config/validate-file', methods=['POST'])
def api_validate_config_file():
    """API: 验证配置文件"""
    try:
        result = config_manager.validate_config_file_syntax()
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/config/file-content', methods=['GET'])
def api_get_config_file_content():
    """API: 获取配置文件内容"""
    try:
        result = config_manager.get_config_file_content()
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/config/file-content', methods=['POST'])
def api_save_config_file_content():
    """API: 保存配置文件内容"""
    try:
        data = request.get_json()
        content = data.get('content')
        if content is None:
            return jsonify({'success': False, 'message': '缺少文件内容'})

        result = config_manager.save_config_file_content(content)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/config/create-default', methods=['POST'])
def api_create_default_config():
    """API: 创建默认配置"""
    try:
        # 创建默认配置
        default_config = {
            'realtime_data_path': '/opt/coin-realtime-data_v1.1.0/data',
            'error_webhook_url': '',
            'is_debug': False,
            'job_num': 2,
            'factor_col_limit': 64,
            'simulator_config': {
                'initial_usdt': 2000,
                'swap_c_rate': 0.00085,
                'spot_c_rate': 0.002
            },
            'data_config': {
                'min_kline_num': 0
            },
            'proxy': {}
        }
        result = config_manager.save_global_config(default_config)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/accounts', methods=['GET', 'POST'])
def api_accounts():
    """API: 账户列表管理"""
    if request.method == 'GET':
        return jsonify(config_manager.get_account_list())
    else:
        # 添加新账户
        account_data = request.get_json()
        try:
            accounts = config_manager.get_account_list()
            accounts.append(account_data)
            result = config_manager.save_account_list(accounts)
            return jsonify(result)
        except Exception as e:
            return jsonify({'success': False, 'message': str(e)})

@app.route('/api/accounts/<int:account_index>', methods=['GET', 'POST', 'PUT', 'DELETE'])
def api_account_detail(account_index):
    """API: 账户详情管理（基于索引）"""
    try:
        accounts = config_manager.get_account_list()

        if request.method == 'GET':
            if 0 <= account_index < len(accounts):
                return jsonify(accounts[account_index])
            else:
                return jsonify({'success': False, 'message': '账户不存在'})

        elif request.method == 'POST':
            if 0 <= account_index < len(accounts):
                account_data = request.get_json()
                accounts[account_index] = account_data
                result = config_manager.save_account_list(accounts)
                return jsonify(result)
            else:
                return jsonify({'success': False, 'message': '账户不存在'})

        elif request.method == 'PUT':
            if 0 <= account_index < len(accounts):
                account_data = request.get_json()
                accounts[account_index] = account_data
                result = config_manager.save_account_list(accounts)
                return jsonify(result)
            else:
                return jsonify({'success': False, 'message': '账户不存在'})

        elif request.method == 'DELETE':
            if 0 <= account_index < len(accounts):
                accounts.pop(account_index)
                result = config_manager.save_account_list(accounts)
                return jsonify(result)
            else:
                return jsonify({'success': False, 'message': '账户不存在'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/accounts/<int:account_index>/test', methods=['POST'])
def api_test_account(account_index):
    """API: 测试账户连接"""
    try:
        accounts = config_manager.get_account_list()
        if 0 <= account_index < len(accounts):
            # 这里可以添加实际的连接测试逻辑
            # 目前返回模拟结果
            return jsonify({'success': True, 'message': '连接测试成功'})
        else:
            return jsonify({'success': False, 'message': '账户不存在'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/accounts/<account_name>', methods=['GET', 'POST', 'DELETE'])
def api_account_detail_by_name(account_name):
    """API: 账户详细配置管理（基于名称，保持兼容性）"""
    if request.method == 'GET':
        return jsonify(config_manager.get_account_config(account_name))
    elif request.method == 'POST':
        account_data = request.get_json()
        result = config_manager.save_account_config(account_name, account_data)
        return jsonify(result)
    elif request.method == 'DELETE':
        result = config_manager.delete_account_config(account_name)
        return jsonify(result)

@app.route('/api/accounts/<account_name>/rename', methods=['POST'])
def api_rename_account(account_name):
    """API: 重命名账户"""
    try:
        data = request.get_json()
        new_name = data.get('new_name', '').strip()

        if not new_name:
            return jsonify({'success': False, 'message': '新账户名称不能为空'})

        result = config_manager.rename_account_config(account_name, new_name)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'message': f'重命名失败: {str(e)}'})

@app.route('/api/accounts/<account_name>/toggle', methods=['POST'])
def api_toggle_account(account_name):
    """API: 启用/禁用账户"""
    try:
        data = request.get_json()
        enable = data.get('enable', True)

        result = config_manager.toggle_account_status(account_name, enable)
        return jsonify(result)
    except Exception as e:
        action = "启用" if enable else "禁用"
        return jsonify({'success': False, 'message': f'{action}失败: {str(e)}'})

@app.route('/api/accounts/<account_name>/status', methods=['GET'])
def api_get_account_status(account_name):
    """API: 获取账户状态"""
    try:
        result = config_manager.get_account_status(account_name)
        return jsonify(result)
    except Exception as e:
        return jsonify({'enabled': False, 'exists': False, 'error': str(e)})

@app.route('/api/accounts/<account_name>/test', methods=['POST'])
def api_test_account_by_name(account_name):
    """API: 测试交易账户API连接"""
    try:
        account_config = config_manager.get_account_config(account_name)

        if not account_config:
            return jsonify({'success': False, 'message': '账户不存在'})

        # 检查API配置
        api_key = account_config.get('account_config', {}).get('apiKey')
        secret = account_config.get('account_config', {}).get('secret')

        if not api_key or not secret:
            return jsonify({'success': False, 'message': 'API密钥未配置'})

        # 实际的Binance API连接测试
        try:
            import requests
            import hmac
            import hashlib
            import time

            # Binance API测试端点
            base_url = 'https://api.binance.com'
            endpoint = '/api/v3/account'

            # 生成签名
            timestamp = int(time.time() * 1000)
            query_string = f'timestamp={timestamp}'
            signature = hmac.new(
                secret.encode('utf-8'),
                query_string.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()

            # 请求头
            headers = {
                'X-MBX-APIKEY': api_key
            }

            # 发送请求
            url = f'{base_url}{endpoint}?{query_string}&signature={signature}'
            response = requests.get(url, headers=headers, timeout=10)

            if response.status_code == 200:
                account_info = response.json()
                return jsonify({
                    'success': True,
                    'message': 'API连接测试成功',
                    'details': {
                        'account_type': account_info.get('accountType', '未知'),
                        'can_trade': account_info.get('canTrade', False),
                        'can_withdraw': account_info.get('canWithdraw', False),
                        'can_deposit': account_info.get('canDeposit', False),
                        'balance_count': len(account_info.get('balances', [])),
                        'permissions': account_info.get('permissions', [])
                    }
                })
            elif response.status_code == 401:
                return jsonify({'success': False, 'message': 'API密钥无效或权限不足'})
            elif response.status_code == 403:
                return jsonify({'success': False, 'message': 'API访问被禁止，请检查IP白名单'})
            else:
                error_data = response.json() if response.content else {}
                error_msg = error_data.get('msg', f'HTTP {response.status_code}')
                return jsonify({'success': False, 'message': f'API测试失败: {error_msg}'})

        except requests.exceptions.Timeout:
            return jsonify({'success': False, 'message': 'API请求超时，请检查网络连接'})
        except requests.exceptions.ConnectionError:
            return jsonify({'success': False, 'message': '无法连接到Binance API，请检查网络'})
        except Exception as api_error:
            return jsonify({'success': False, 'message': f'API测试异常: {str(api_error)}'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'测试失败: {str(e)}'})

@app.route('/api/accounts/<account_name>/download', methods=['GET'])
def api_download_account_by_name(account_name):
    """API: 下载交易账户配置文件"""
    try:
        account_file_path = config_manager.accounts_dir / f"{account_name}.py"

        if account_file_path.exists():
            from flask import send_file
            return send_file(account_file_path, as_attachment=True, download_name=f"{account_name}.py")
        else:
            return jsonify({'success': False, 'message': '配置文件不存在'}), 404

    except Exception as e:
        return jsonify({'success': False, 'message': f'下载失败: {str(e)}'}), 500

@app.route('/api/accounts/<account_name>/config-file', methods=['GET'])
def api_get_account_config_file(account_name):
    """API: 获取交易账户配置文件内容"""
    try:
        account_file_path = config_manager.accounts_dir / f"{account_name}.py"

        if account_file_path.exists():
            with open(account_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return jsonify({'success': True, 'content': content})
        else:
            return jsonify({'success': False, 'message': '配置文件不存在'}), 404

    except Exception as e:
        return jsonify({'success': False, 'message': f'读取文件失败: {str(e)}'}), 500

@app.route('/api/accounts/<account_name>/strategy-pool', methods=['GET'])
def api_get_strategy_pool(account_name):
    """API: 获取账户策略池配置"""
    try:
        account_config = config_manager.get_account_config(account_name)
        if not account_config:
            return jsonify({'success': False, 'message': '账户不存在'})

        # 读取原始配置文件来获取策略池代码
        account_file = config_manager.accounts_dir / f"{account_name}.py"
        if account_file.exists():
            with open(account_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 提取策略池配置部分
            strategy_pool_start = content.find('strategy_pool = [')
            if strategy_pool_start != -1:
                # 找到策略池配置的结束位置
                bracket_count = 0
                start_pos = strategy_pool_start
                for i, char in enumerate(content[start_pos:], start_pos):
                    if char == '[':
                        bracket_count += 1
                    elif char == ']':
                        bracket_count -= 1
                        if bracket_count == 0:
                            strategy_pool_code = content[start_pos:i+1]
                            return jsonify({
                                'success': True,
                                'strategy_pool_code': strategy_pool_code
                            })

        return jsonify({'success': True, 'strategy_pool_code': ''})

    except Exception as e:
        return jsonify({'success': False, 'message': f'获取策略池配置失败: {str(e)}'})

@app.route('/api/validate-strategy-pool', methods=['POST'])
def api_validate_strategy_pool():
    """API: 验证策略池配置语法"""
    try:
        data = request.get_json()
        code = data.get('code', '')

        if not code.strip():
            return jsonify({'valid': False, 'error': '代码不能为空'})

        # 基本语法检查
        try:
            # 检查是否包含strategy_pool
            if 'strategy_pool' not in code:
                return jsonify({'valid': False, 'error': '配置中必须包含strategy_pool变量'})

            # 检查括号匹配
            open_brackets = code.count('[')
            close_brackets = code.count(']')
            open_braces = code.count('{')
            close_braces = code.count('}')
            open_parens = code.count('(')
            close_parens = code.count(')')

            if open_brackets != close_brackets:
                return jsonify({'valid': False, 'error': f'方括号不匹配: {open_brackets} 个 [ 和 {close_brackets} 个 ]'})
            if open_braces != close_braces:
                return jsonify({'valid': False, 'error': f'花括号不匹配: {open_braces} 个 {{ 和 {close_braces} 个 }}'})
            if open_parens != close_parens:
                return jsonify({'valid': False, 'error': f'圆括号不匹配: {open_parens} 个 ( 和 {close_parens} 个 )'})

            # 尝试编译代码（简单的语法检查）
            compile(code, '<string>', 'exec')

            return jsonify({'valid': True, 'message': '语法验证通过'})

        except SyntaxError as e:
            return jsonify({'valid': False, 'error': f'语法错误: {str(e)}'})
        except Exception as e:
            return jsonify({'valid': False, 'error': f'验证失败: {str(e)}'})

    except Exception as e:
        return jsonify({'valid': False, 'error': f'服务器错误: {str(e)}'})

@app.route('/api/accounts/<account_name>/copy', methods=['POST'])
def api_copy_account_by_name(account_name):
    """API: 复制交易账户配置"""
    try:
        data = request.get_json()
        new_account_name = data.get('new_name')

        if not new_account_name:
            return jsonify({'success': False, 'message': '新账户名称不能为空'})

        # 验证账户名称格式
        if not re.match(r'^[a-zA-Z0-9_\u4e00-\u9fa5]+$', new_account_name):
            return jsonify({'success': False, 'message': '账户名称只能包含字母、数字、下划线和中文字符'})

        # 检查新账户是否已存在
        new_account_file = config_manager.accounts_dir / f"{new_account_name}.py"
        if new_account_file.exists():
            return jsonify({'success': False, 'message': '账户名称已存在'})

        # 获取源账户配置
        source_config = config_manager.get_account_config(account_name)
        if 'error' in source_config:
            return jsonify({'success': False, 'message': f'获取源账户配置失败: {source_config["error"]}'})

        # 读取源账户文件来获取完整的strategy_pool配置
        source_account_file = config_manager.accounts_dir / f"{account_name}.py"
        strategy_pool_code = ''
        if source_account_file.exists():
            with open(source_account_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 提取策略池配置部分
            strategy_pool_start = content.find('strategy_pool = [')
            if strategy_pool_start != -1:
                # 找到策略池配置的结束位置
                bracket_count = 0
                start_pos = strategy_pool_start
                for i, char in enumerate(content[start_pos:], start_pos):
                    if char == '[':
                        bracket_count += 1
                    elif char == ']':
                        bracket_count -= 1
                        if bracket_count == 0:
                            strategy_pool_code = content[start_pos:i+1]
                            # 更新策略池中的name字段为新账户名
                            strategy_pool_code = strategy_pool_code.replace(
                                f"name='{account_name}'",
                                f"name='{new_account_name}'"
                            )
                            break

        # 创建新账户配置（清空敏感信息）
        account_config = source_config.get('account_config', {})
        new_config = {
            'backtest_name': new_account_name,
            'account_type': account_config.get('account_type', '普通账户'),
            'leverage': source_config.get('leverage', 1),
            'get_kline_num': source_config.get('get_kline_num', 5000),
            'apiKey': '',  # 清空API密钥
            'secret': '',  # 清空Secret密钥
            'strategy_name': source_config.get('strategy_config', {}).get('name', 'FixedRatioStrategy'),
            'hold_period': source_config.get('strategy_config', {}).get('hold_period', '1H'),
            'wechat_webhook_url': account_config.get('wechat_webhook_url', ''),
            'hour_offset': account_config.get('hour_offset', '0m'),
            'buy_bnb_value': account_config.get('buy_bnb_value', 11),
            'if_use_bnb_burn': account_config.get('if_use_bnb_burn', True),
            # 添加其他重要配置字段
            'max_one_order_amount': account_config.get('max_one_order_amount', 100),
            'twap_interval': account_config.get('twap_interval', 2),
            'if_transfer_bnb': account_config.get('if_transfer_bnb', True),
            # 保留源账户的黑白名单配置
            'black_list': source_config.get('black_list', []),
            'white_list': source_config.get('white_list', []),
            # 保留源账户的策略池配置
            'strategy_pool_code': strategy_pool_code
        }

        # 保存新账户配置
        result = config_manager.save_account_config(new_account_name, new_config)
        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'message': f'复制账户失败: {str(e)}'})

@app.route('/api/log-files')
def api_log_files():
    """API: 获取日志文件列表"""
    logs = []
    logs_dir = PROJECT_ROOT / "logs"
    if logs_dir.exists():
        for log_file in logs_dir.glob("*.log"):
            logs.append({
                "name": log_file.name,
                "path": str(log_file),
                "size": log_file.stat().st_size,
                "modified": datetime.fromtimestamp(log_file.stat().st_mtime).strftime("%Y-%m-%d %H:%M:%S")
            })
    return jsonify(sorted(logs, key=lambda x: x['modified'], reverse=True))

@app.route('/api/logs/<log_name>')
def api_log_content(log_name):
    """API: 获取日志文件内容"""
    logs_dir = PROJECT_ROOT / "logs"
    log_file = logs_dir / log_name

    if not log_file.exists():
        return jsonify({"error": "日志文件不存在"}), 404

    try:
        # 读取最后1000行
        lines = []
        with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()

        # 只返回最后1000行
        if len(lines) > 1000:
            lines = lines[-1000:]

        return jsonify({
            "content": "".join(lines),
            "lines": len(lines),
            "size": log_file.stat().st_size
        })
    except Exception as e:
        return jsonify({"error": f"读取日志失败: {str(e)}"}), 500

@app.route('/api/log-files-info')
def api_log_files_info():
    """API: 获取日志文件信息（用于清理前预览）"""
    try:
        logs_dir = PROJECT_ROOT / "logs"
        if not logs_dir.exists():
            return jsonify({
                "success": True,
                "files": [],
                "total_size": 0,
                "message": "日志目录不存在"
            })

        files_info = []
        total_size = 0

        # 遍历所有日志文件，包括带日期后缀的文件
        log_patterns = ["*.log", "*.log.*"]

        for pattern in log_patterns:
            for log_file in logs_dir.glob(pattern):
                if log_file.exists() and log_file.is_file():
                    # 只处理日志文件（排除其他类型的文件）
                    if log_file.suffix == '.log' or '.log.' in log_file.name:
                        file_size = log_file.stat().st_size
                        if file_size > 0:  # 只显示非空文件
                            files_info.append({
                                "name": log_file.name,
                                "size": file_size,
                                "modified": datetime.fromtimestamp(log_file.stat().st_mtime).strftime("%Y-%m-%d %H:%M:%S")
                            })
                            total_size += file_size

        # 按修改时间排序
        files_info.sort(key=lambda x: x['modified'], reverse=True)

        return jsonify({
            "success": True,
            "files": files_info,
            "total_size": total_size,
            "file_count": len(files_info)
        })

    except Exception as e:
        return jsonify({
            "success": False,
            "message": f"获取日志文件信息失败: {str(e)}"
        }), 500

@app.route('/api/clear-logs', methods=['POST'])
def api_clear_logs():
    """API: 清理日志文件"""
    try:
        logs_dir = PROJECT_ROOT / "logs"
        if not logs_dir.exists():
            return jsonify({
                "success": True,
                "message": "日志目录不存在，无需清理",
                "cleared_files": [],
                "total_size_freed": 0
            })

        cleared_files = []
        total_size_freed = 0

        # 遍历所有日志文件，包括带日期后缀的文件
        # 匹配 *.log 和 *.log.YYYY-MM-DD 格式的文件
        log_patterns = ["*.log", "*.log.*"]

        for pattern in log_patterns:
            for log_file in logs_dir.glob(pattern):
                if log_file.exists() and log_file.is_file():
                    # 只处理日志文件（排除其他类型的文件）
                    if log_file.suffix == '.log' or '.log.' in log_file.name:
                        # 记录清理前的文件大小
                        size_before = log_file.stat().st_size

                        if size_before > 0:  # 只处理非空文件
                            # 清空文件内容
                            with open(log_file, 'w', encoding='utf-8') as f:
                                f.write('')

                            cleared_files.append({
                                "name": log_file.name,
                                "size_before": size_before,
                                "size_after": 0
                            })
                            total_size_freed += size_before

        return jsonify({
            "success": True,
            "message": f"成功清理 {len(cleared_files)} 个日志文件",
            "cleared_files": cleared_files,
            "total_size_freed": total_size_freed
        })

    except Exception as e:
        return jsonify({
            "success": False,
            "message": f"清理日志文件失败: {str(e)}"
        }), 500

# 资金曲线API
@app.route('/api/fund-curve/strategies')
def api_fund_curve_strategies():
    """API: 获取所有可用的策略列表"""
    return jsonify(fund_curve_manager.get_available_strategies())

@app.route('/api/fund-curve/strategy-data')
def api_fund_curve_strategy_data():
    """API: 获取指定策略的资金曲线数据"""
    file_path = request.args.get('file_path')
    start_time = request.args.get('start_time')
    end_time = request.args.get('end_time')

    if not file_path:
        return jsonify({
            "success": False,
            "message": "缺少file_path参数"
        })

    return jsonify(fund_curve_manager.get_strategy_data(file_path, start_time, end_time))

@app.route('/api/fund-curve/compare', methods=['POST'])
def api_fund_curve_compare():
    """API: 对比多个策略的资金曲线"""
    try:
        data = request.get_json()
        file_paths = data.get('file_paths', [])
        start_time = data.get('start_time')
        end_time = data.get('end_time')

        return jsonify(fund_curve_manager.compare_strategies(file_paths, start_time, end_time))
    except Exception as e:
        return jsonify({
            "success": False,
            "message": f"策略对比失败: {str(e)}"
        })

@app.route('/api/fund-curve/drawdown-events', methods=['POST'])
def api_fund_curve_drawdown_events():
    """API: 分析回撤事件"""
    try:
        data = request.get_json()
        file_path = data.get('file_path')
        start_time = data.get('start_time')
        end_time = data.get('end_time')
        min_drawdown = data.get('min_drawdown', 0.5)
        max_drawdown = data.get('max_drawdown', 20)
        min_drawdown_days = data.get('min_drawdown_days', 0)
        max_drawdown_days = data.get('max_drawdown_days', 30)
        min_high_interval = data.get('min_high_interval', 0)
        max_high_interval = data.get('max_high_interval', 100)

        if not file_path:
            return jsonify({
                "success": False,
                "message": "缺少file_path参数"
            })

        return jsonify(fund_curve_manager.analyze_drawdown_events(
            file_path, start_time, end_time,
            min_drawdown, max_drawdown,
            min_drawdown_days, max_drawdown_days,
            min_high_interval, max_high_interval
        ))
    except Exception as e:
        return jsonify({
            "success": False,
            "message": f"分析回撤事件失败: {str(e)}"
        })

@app.route('/api/fund-curve/delete-strategy', methods=['DELETE'])
@login_required if AUTH_ENABLED else lambda f: f
@permission_required('fund_curve') if AUTH_ENABLED else lambda f: f
def api_fund_curve_delete_strategy():
    """API: 删除指定的资金曲线文件"""
    try:
        data = request.get_json()
        file_path = data.get('file_path')

        if not file_path:
            return jsonify({
                "success": False,
                "message": "缺少file_path参数"
            })

        return jsonify(fund_curve_manager.delete_strategy_file(file_path))

    except Exception as e:
        return jsonify({
            "success": False,
            "message": f"删除策略失败: {str(e)}"
        })

@app.route('/api/system/info')
def api_system_info():
    """API: 获取系统信息"""
    try:
        # 获取系统信息
        system_info = {
            "platform": platform.system(),
            "platform_version": platform.version(),
            "python_version": platform.python_version(),
            "cpu_count": os.cpu_count(),
            "hostname": platform.node(),
            "architecture": platform.architecture()[0]
        }

        # 获取内存信息
        if psutil:
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            system_info.update({
                "memory_total": memory.total,
                "memory_available": memory.available,
                "memory_percent": memory.percent,
                "disk_total": disk.total,
                "disk_free": disk.free,
                "disk_percent": (disk.used / disk.total) * 100
            })

        return jsonify(system_info)
    except Exception as e:
        return jsonify({"error": f"获取系统信息失败: {str(e)}"}), 500

@app.route('/api/system/resources')
def api_system_resources():
    """API: 获取系统资源使用情况"""
    try:
        resources = {}

        if psutil:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            resources['cpu'] = {
                'percent': cpu_percent,
                'count': psutil.cpu_count(),
                'count_logical': psutil.cpu_count(logical=True)
            }

            # 内存使用情况
            memory = psutil.virtual_memory()
            resources['memory'] = {
                'total': memory.total,
                'available': memory.available,
                'used': memory.used,
                'percent': memory.percent,
                'total_gb': round(memory.total / (1024**3), 2),
                'used_gb': round(memory.used / (1024**3), 2),
                'available_gb': round(memory.available / (1024**3), 2)
            }

            # 虚拟内存（交换空间）使用情况
            swap = psutil.swap_memory()
            resources['swap'] = {
                'total': swap.total,
                'used': swap.used,
                'free': swap.free,
                'percent': swap.percent,
                'total_gb': round(swap.total / (1024**3), 2) if swap.total > 0 else 0,
                'used_gb': round(swap.used / (1024**3), 2) if swap.used > 0 else 0,
                'available': swap.total > 0  # 是否有虚拟内存
            }

        return jsonify(resources)
    except Exception as e:
        return jsonify({"error": f"获取系统资源信息失败: {str(e)}"}), 500

def start_monitoring():
    """启动监控线程"""
    global is_monitoring, process_monitor_thread

    if not is_monitoring:
        is_monitoring = True
        process_monitor_thread = threading.Thread(target=monitor_process, daemon=True)
        process_monitor_thread.start()

def stop_monitoring():
    """停止监控线程"""
    global is_monitoring
    is_monitoring = False

def monitor_process():
    """监控进程状态并通过WebSocket发送更新"""
    while is_monitoring:
        try:
            status = process_manager.get_status()
            socketio.emit('status_update', status)
            time.sleep(5)  # 每5秒更新一次
        except Exception:
            time.sleep(5)

class LogFilter:
    """智能日志过滤器"""

    def __init__(self):
        # 需要过滤的噪音日志模式
        self.noise_patterns = [
            r'客户端已连接',
            r'客户端已断开连接',
            r'GET /socket\.io/',
            r'POST /socket\.io/',
            r'GET /api/logs',
            r'GET /favicon\.ico',
            r'127\.0\.0\.1 - - \[.*?\]',
            r'监控进程出错',
            r'WebSocket连接',
        ]

        # 重复日志检测
        self.recent_messages = {}
        self.message_counts = {}
        self.max_recent_messages = 50

        # 重要关键词（这些日志总是保留）
        self.important_keywords = [
            '启动', '停止', '错误', '失败', '成功', '警告', '异常',
            '交易', '下单', '撤单', '账户', '余额', '仓位',
            'ERROR', 'WARNING', 'CRITICAL', 'EXCEPTION',
            '🔴', '🟠', '🟡', '⚠️', '❌', '✅',
            # 调度调试相关关键词
            '定时任务', '执行', '下次执行时间', '等待', '运行模式',
            '企业微信', 'Python解释器', '开始执行', '执行完成',
            '返回码', 'startup.py', '调度'
        ]

        # 调试信息关键词
        self.debug_keywords = [
            'DEBUG', '调试', '🐞', 'debug', 'trace'
        ]

    def should_filter_message(self, message: str, verbose_mode: bool = False) -> bool:
        """判断是否应该过滤掉这条消息"""
        if not message or not message.strip():
            return True

        # 详细模式下显示所有日志
        if verbose_mode:
            return False

        # 检查是否包含重要关键词
        for keyword in self.important_keywords:
            if keyword in message:
                return False

        # 检查是否是噪音日志
        for pattern in self.noise_patterns:
            if re.search(pattern, message):
                return True

        # 检查是否是调试信息（非详细模式下过滤）
        for keyword in self.debug_keywords:
            if keyword in message:
                return True

        return False

    def process_message(self, message: str, verbose_mode: bool = False) -> Optional[Dict[str, Any]]:
        """处理日志消息，返回处理后的日志对象或None"""
        if self.should_filter_message(message, verbose_mode):
            return None

        # 生成消息的简化版本用于去重
        simplified_message = self._simplify_message(message)
        current_time = time.time()

        # 检查是否是重复消息
        if simplified_message in self.recent_messages:
            last_time = self.recent_messages[simplified_message]
            # 如果5分钟内有相同消息，进行聚合
            if current_time - last_time < 300:  # 5分钟
                self.message_counts[simplified_message] = self.message_counts.get(simplified_message, 1) + 1
                return None

        # 记录新消息
        self.recent_messages[simplified_message] = current_time
        count = self.message_counts.get(simplified_message, 0)

        # 清理旧的消息记录
        if len(self.recent_messages) > self.max_recent_messages:
            self._cleanup_old_messages()

        # 解析日志级别
        level = self._parse_log_level(message)

        # 构建日志对象
        log_obj = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'level': level,
            'message': message.strip(),
            'count': count + 1 if count > 0 else 1
        }

        # 重置计数
        if simplified_message in self.message_counts:
            del self.message_counts[simplified_message]

        return log_obj

    def _simplify_message(self, message: str) -> str:
        """简化消息用于去重检测"""
        # 移除时间戳
        simplified = re.sub(r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}', '', message)
        simplified = re.sub(r'\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\]', '', simplified)
        # 移除日志级别标记
        simplified = re.sub(r'\[(DEBUG|INFO|WARNING|ERROR|CRITICAL)\]', '', simplified)
        # 移除多余空格
        simplified = ' '.join(simplified.split())
        return simplified.strip()

    def _parse_log_level(self, message: str) -> str:
        """解析日志级别"""
        message_upper = message.upper()

        # 错误级别
        if any(keyword in message_upper for keyword in ['ERROR', 'CRITICAL', 'EXCEPTION', '错误', '异常', '失败', '❌', '🔴']):
            return 'error'

        # 警告级别
        if any(keyword in message_upper for keyword in ['WARNING', 'WARN', '警告', '⚠️', '🟠', '🟡']):
            return 'warning'

        # 调试级别
        if any(keyword in message_upper for keyword in ['DEBUG', '调试', '🐞']):
            return 'debug'

        # 成功/信息级别
        if any(keyword in message_upper for keyword in ['成功', '完成', '启动', '✅', '🟢']):
            return 'success'

        # 默认信息级别
        return 'info'

    def _cleanup_old_messages(self):
        """清理旧的消息记录"""
        current_time = time.time()
        expired_keys = [
            key for key, timestamp in self.recent_messages.items()
            if current_time - timestamp > 300  # 5分钟
        ]
        for key in expired_keys:
            del self.recent_messages[key]
            if key in self.message_counts:
                del self.message_counts[key]

# 全局日志过滤器实例
log_filter = LogFilter()

def get_log_file_path():
    """获取当前日志文件路径"""
    logs_dir = PROJECT_ROOT / "logs"
    logs_dir.mkdir(exist_ok=True)
    return logs_dir / f"trading_{datetime.now().strftime('%Y%m%d')}.log"

def start_log_monitoring(process, log_file):
    """启动日志监控线程"""
    global log_monitor_thread

    def monitor_logs():
        """监控程序输出并写入日志文件，同时通过WebSocket推送"""
        try:
            # 发送监控开始消息
            monitor_start_msg = f"开始监控日志文件: {log_file}"
            processed_log = {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'level': 'info',
                'message': monitor_start_msg,
                'raw_message': monitor_start_msg
            }
            socketio.emit('log_update', processed_log)

            with open(log_file, 'w', encoding='utf-8') as f:
                while process.poll() is None:
                    try:
                        # 读取程序输出
                        line = process.stdout.readline()
                        if line:
                            # 写入日志文件（保留所有原始日志）
                            f.write(line)
                            f.flush()

                            # 处理日志消息（应用过滤和优化）
                            verbose_mode = getattr(log_filter, 'verbose_mode', False)
                            silent_mode = getattr(log_filter, 'silent_mode', False)

                            if silent_mode:
                                # 静默模式：只显示错误和重要操作
                                processed_log = log_filter.process_message(line.strip(), verbose_mode=False)
                                if processed_log and processed_log['level'] in ['error', 'warning']:
                                    socketio.emit('log_update', processed_log)
                            else:
                                # 普通模式或详细模式
                                processed_log = log_filter.process_message(line.strip(), verbose_mode=verbose_mode)
                                if processed_log:
                                    socketio.emit('log_update', processed_log)
                        else:
                            time.sleep(0.1)
                    except Exception as e:
                        error_msg = f"读取日志时出错: {str(e)}"
                        processed_log = {
                            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                            'level': 'error',
                            'message': error_msg,
                            'raw_message': error_msg
                        }
                        socketio.emit('log_update', processed_log)
                        break

                # 进程结束后，读取剩余输出
                remaining_output = process.stdout.read()
                if remaining_output:
                    f.write(remaining_output)
                    f.flush()

                    # 推送剩余日志
                    for line in remaining_output.splitlines():
                        if line.strip():
                            processed_log = log_filter.process_message(line.strip())
                            if processed_log:
                                socketio.emit('log_update', processed_log)

        except Exception:
            pass  # 静默处理日志监控错误

    log_monitor_thread = threading.Thread(target=monitor_logs, daemon=True)
    log_monitor_thread.start()

@socketio.on('connect')
def handle_connect():
    """WebSocket连接处理"""
    # 发送当前状态
    status = process_manager.get_status()
    emit('status_update', status)

@socketio.on('disconnect')
def handle_disconnect():
    """WebSocket断开连接处理"""
    pass

if __name__ == '__main__':
    print("启动量化交易系统Web管理界面...")
    print(f"项目根目录: {PROJECT_ROOT}")
    print("访问地址: http://localhost:5000")
    
    # 创建必要的目录
    templates_dir = PROJECT_ROOT / "templates"
    static_dir = PROJECT_ROOT / "static"
    templates_dir.mkdir(exist_ok=True)
    static_dir.mkdir(exist_ok=True)
    
    # 启动Flask应用
    # 在pm2环境下需要allow_unsafe_werkzeug=True来正确处理POST请求中的特殊字符
    socketio.run(app, host='0.0.0.0', port=5000, debug=False, allow_unsafe_werkzeug=True)
