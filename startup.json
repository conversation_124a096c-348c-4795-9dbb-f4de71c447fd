{"apps": [{"name": "scheduler", "script": "/opt/position-mgmt-trading_v1.3.2_web/schedule_startup.py", "exec_interpreter": "/root/anaconda3/envs/py312/bin/python", "merge_logs": false, "watch": false, "error_file": "/opt/position-mgmt-trading_v1.3.2_web/logs/schedule.error.log", "out_file": "/opt/position-mgmt-trading_v1.3.2_web/logs/schedule.out.log"}, {"name": "schedule_资金曲线_企业微信版", "script": "/opt/position-mgmt-trading_v1.3.2_web/待复用模块/schedule_资金曲线_企业微信版.py", "exec_interpreter": "/root/anaconda3/envs/py312/bin/python", "merge_logs": false, "watch": false, "error_file": "/opt/position-mgmt-trading_v1.3.2_web/logs/schedule.error.log", "out_file": "/opt/position-mgmt-trading_v1.3.2_web/logs/schedule.out.log"}, {"name": "pmt-startup", "script": "/opt/position-mgmt-trading_v1.3.2_web/startup.py", "exec_interpreter": "/root/anaconda3/envs/py312/bin/python", "merge_logs": false, "watch": false, "error_file": "/opt/position-mgmt-trading_v1.3.2_web/logs/pmt-startup.error.log", "out_file": "/opt/position-mgmt-trading_v1.3.2_web/logs/pmt-startup.out.log"}, {"name": "web-manager", "script": "/opt/position-mgmt-trading_v1.3.2_web/web_manager.py", "exec_interpreter": "/root/anaconda3/envs/py312/bin/python", "merge_logs": false, "watch": false, "error_file": "/opt/position-mgmt-trading_v1.3.2_web/logs/web-manager.error.log", "out_file": "/opt/position-mgmt-trading_v1.3.2_web/logs/web-manager.out.log", "env": {"FLASK_ENV": "production", "PYTHONUNBUFFERED": "1"}, "instances": 1, "exec_mode": "fork"}]}