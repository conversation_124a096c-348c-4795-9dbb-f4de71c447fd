#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
量化交易系统认证路由
提供登录、登出、Google认证设置等功能
"""

from datetime import datetime
from flask import Blueprint, render_template, request, redirect, url_for, session, jsonify, flash, g

from auth_manager import user_manager, session_manager, google_auth_manager, security_manager
from auth_middleware import csrf_protect, rate_limit

# 创建认证蓝图
auth_bp = Blueprint('auth', __name__, url_prefix='/auth')


@auth_bp.route('/login', methods=['GET', 'POST'])
@rate_limit(max_requests=10, window=300)  # 5分钟内最多10次登录尝试
def login():
    """用户登录"""
    if request.method == 'GET':
        # 如果已经登录，重定向到主页
        if g.get('current_user'):
            return redirect(url_for('index'))
        
        # 生成CSRF令牌
        csrf_token = security_manager.generate_csrf_token()
        session['csrf_token'] = csrf_token
        
        # 获取错误信息
        error_message = None
        error_type = request.args.get('error')
        if error_type == 'access_denied':
            error_message = '访问被拒绝，请重新登录'
        elif error_type == 'rate_limit':
            error_message = '请求过于频繁，请稍后再试'
        elif error_type == 'session_expired':
            error_message = '会话已过期，请重新登录'
        
        return render_template('auth/login.html', 
                             csrf_token=csrf_token,
                             error_message=error_message)
    
    # POST请求处理登录
    username = request.form.get('username', '').strip()
    password = request.form.get('password', '')
    google_code = request.form.get('google_code', '').strip()
    backup_code = request.form.get('backup_code', '').strip()
    remember_me = request.form.get('remember_me') == 'on'
    
    client_ip = g.client_ip
    user_agent = g.user_agent
    
    # 基础验证
    if not username or not password:
        return render_template('auth/login.html',
                             csrf_token=session.get('csrf_token'),
                             error_message='请输入用户名和密码',
                             username=username)

    # 检查账户是否被锁定
    if user_manager.is_account_locked(username):
        user_manager.record_login_attempt(username, False, client_ip, user_agent, '账户被锁定')
        lockout_time = user_manager.get_lockout_remaining_time(username)
        error_msg = f'账户已被锁定，请在 {lockout_time} 分钟后重试'
        return render_template('auth/login.html',
                             csrf_token=session.get('csrf_token'),
                             error_message=error_msg,
                             username=username)

    # 验证用户名和密码
    if not user_manager.verify_password(username, password):
        user_manager.record_login_attempt(username, False, client_ip, user_agent, '密码错误')
        remaining_attempts = user_manager.get_remaining_login_attempts(username)
        if remaining_attempts > 0:
            error_msg = f'用户名或密码错误，还有 {remaining_attempts} 次尝试机会'
        else:
            error_msg = '用户名或密码错误'
        return render_template('auth/login.html',
                             csrf_token=session.get('csrf_token'),
                             error_message=error_msg,
                             username=username)
    
    # 检查是否需要Google认证
    user = user_manager.get_user(username)
    if user.get('is_google_auth') == 'Y':
        # 验证Google认证码
        if google_code:
            if not google_auth_manager.verify_totp_code(username, google_code):
                user_manager.record_login_attempt(username, False, client_ip, user_agent, 'Google验证码错误')
                return render_template('auth/login.html',
                                     csrf_token=session.get('csrf_token'),
                                     error_message='Google验证码错误，请检查时间同步后重试',
                                     show_google_auth=True,
                                     username=username,
                                     password=password,  # 保持密码字段
                                     remember_me=remember_me)
        elif backup_code:
            if not google_auth_manager.verify_backup_code(username, backup_code):
                user_manager.record_login_attempt(username, False, client_ip, user_agent, '备用码错误')
                return render_template('auth/login.html',
                                     csrf_token=session.get('csrf_token'),
                                     error_message='备用码错误或已使用，请尝试其他备用码',
                                     show_google_auth=True,
                                     username=username,
                                     password=password,  # 保持密码字段
                                     remember_me=remember_me)
        else:
            # 需要Google认证但未提供验证码
            return render_template('auth/login.html',
                                 csrf_token=session.get('csrf_token'),
                                 error_message='请输入Google Authenticator验证码完成登录',
                                 show_google_auth=True,
                                 username=username,
                                 password=password,  # 保持密码字段
                                 remember_me=remember_me)
    
    # 登录成功，创建会话
    session_id = session_manager.create_session(username, client_ip, user_agent)
    if not session_id:
        return render_template('auth/login.html',
                             csrf_token=session.get('csrf_token'),
                             error_message='系统繁忙，创建会话失败，请稍后重试',
                             username=username)
    
    # 设置会话信息
    session['username'] = username
    session['session_id'] = session_id
    session['login_time'] = datetime.now().isoformat()
    
    # 如果使用了Google认证，更新认证时间
    if google_code or backup_code:
        session_manager.update_google_auth_time(username, session_id)
    
    # 记录登录成功
    user_manager.record_login_attempt(username, True, client_ip, user_agent)

    # 检查是否需要强制设置Google认证
    if google_auth_manager.is_google_setup_required(username):
        flash('登录成功！正在设置Google认证...', 'success')
        return redirect(url_for('auth.setup_google'))

    # 设置成功登录的Flash消息
    flash(f'欢迎回来，{username}！登录成功。', 'success')

    # 重定向到原来要访问的页面或主页
    next_page = request.args.get('next') or url_for('index')
    return redirect(next_page)


@auth_bp.route('/logout', methods=['GET', 'POST'])
def logout():
    """用户登出"""
    username = session.get('username')
    session_id = session.get('session_id')

    # 记录退出日志
    if username:
        print(f"用户 {username} 正在退出登录")

    # 撤销会话
    if username and session_id:
        session_manager.revoke_session(username, session_id)

    # 清除会话
    session.clear()

    # 设置退出成功消息
    flash('您已成功退出登录，感谢使用量化交易系统！', 'success')

    # 重定向到登录页面
    return redirect(url_for('auth.login'))


@auth_bp.route('/setup-google', methods=['GET', 'POST'])
def setup_google():
    """设置Google认证"""
    # 检查是否已登录 - 优先从g.current_user获取，如果没有则从session获取
    current_user = g.get('current_user')
    if not current_user:
        # 尝试从session获取用户信息
        username = session.get('username')
        session_id = session.get('session_id')

        if not username or not session_id:
            flash('请先登录系统', 'error')
            return redirect(url_for('auth.login'))

        # 验证会话有效性
        client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
        if not session_manager.validate_session(username, session_id, client_ip):
            flash('会话已过期，请重新登录', 'error')
            return redirect(url_for('auth.login'))

        # 获取用户信息
        current_user = user_manager.get_user(username)
        if not current_user:
            flash('用户信息获取失败，请重新登录', 'error')
            return redirect(url_for('auth.login'))

    username = current_user.get('user_name')
    
    if request.method == 'GET':
        # 生成密钥和二维码
        secret_key = google_auth_manager.generate_secret_key()
        qr_code_url = google_auth_manager.generate_qr_code(username, secret_key)
        
        # 生成CSRF令牌
        csrf_token = security_manager.generate_csrf_token()
        session['csrf_token'] = csrf_token
        session['temp_google_secret'] = secret_key
        
        return render_template('auth/setup_google_auth.html',
                             secret_key=secret_key,
                             qr_code_url=qr_code_url,
                             csrf_token=csrf_token)
    
    # POST请求 - 验证设置
    return redirect(url_for('auth.setup_google_verify'))


@auth_bp.route('/setup-google-verify', methods=['POST'])
@csrf_protect
def setup_google_verify():
    """验证Google认证设置"""
    # 检查是否已登录 - 优先从g.current_user获取，如果没有则从session获取
    current_user = g.get('current_user')
    if not current_user:
        # 尝试从session获取用户信息
        username = session.get('username')
        session_id = session.get('session_id')

        if not username or not session_id:
            flash('请先登录系统', 'error')
            return redirect(url_for('auth.login'))

        # 验证会话有效性
        client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
        if not session_manager.validate_session(username, session_id, client_ip):
            flash('会话已过期，请重新登录', 'error')
            return redirect(url_for('auth.login'))

        # 获取用户信息
        current_user = user_manager.get_user(username)
        if not current_user:
            flash('用户信息获取失败，请重新登录', 'error')
            return redirect(url_for('auth.login'))

    username = current_user.get('user_name')
    verification_code = request.form.get('verification_code', '').strip()
    secret_key = request.form.get('secret_key') or session.get('temp_google_secret')
    
    if not verification_code or not secret_key:
        return render_template('auth/setup_google_auth.html',
                             error_message='请输入验证码',
                             secret_key=secret_key,
                             csrf_token=session.get('csrf_token'))
    
    # 验证验证码
    import pyotp
    try:
        totp = pyotp.TOTP(secret_key)
        if not totp.verify(verification_code, valid_window=1):
            return render_template('auth/setup_google_auth.html',
                                 error_message='验证码错误，请重试',
                                 secret_key=secret_key,
                                 csrf_token=session.get('csrf_token'))
    except Exception as e:
        return render_template('auth/setup_google_auth.html',
                             error_message='验证失败，请重试',
                             secret_key=secret_key,
                             csrf_token=session.get('csrf_token'))
    
    # 设置Google认证
    if google_auth_manager.setup_google_auth(username, secret_key):
        # 更新会话的Google认证时间
        session_id = session.get('session_id')
        if session_id:
            session_manager.update_google_auth_time(username, session_id)

        # 清除临时密钥
        session.pop('temp_google_secret', None)

        # 获取备用码并存储到session中，以便在成功页面显示
        user = user_manager.get_user(username)
        backup_codes = user.get('google_backup_codes', [])
        session['google_backup_codes'] = backup_codes

        # 设置成功消息并重定向到成功页面
        flash('🎉 Google认证设置成功！您的账户安全性已得到增强。', 'success')
        return redirect(url_for('auth.setup_google_success'))
    else:
        return render_template('auth/setup_google_auth.html',
                             error_message='设置失败，请重试',
                             secret_key=secret_key,
                             csrf_token=session.get('csrf_token'))


@auth_bp.route('/setup-google-success', methods=['GET'])
def setup_google_success():
    """Google认证设置成功页面"""
    # 检查是否已登录
    current_user = g.get('current_user')
    if not current_user:
        # 尝试从session获取用户信息
        username = session.get('username')
        session_id = session.get('session_id')

        if not username or not session_id:
            flash('请先登录系统', 'error')
            return redirect(url_for('auth.login'))

        # 验证会话有效性
        client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
        if not session_manager.validate_session(username, session_id, client_ip):
            flash('会话已过期，请重新登录', 'error')
            return redirect(url_for('auth.login'))

        # 获取用户信息
        current_user = user_manager.get_user(username)
        if not current_user:
            flash('用户信息获取失败，请重新登录', 'error')
            return redirect(url_for('auth.login'))

    username = current_user.get('user_name')

    # 检查是否真的设置了Google认证
    if not google_auth_manager.is_google_auth_enabled(username):
        flash('Google认证尚未设置，请先完成设置', 'warning')
        return redirect(url_for('auth.setup_google'))

    # 获取备用码
    backup_codes = session.get('google_backup_codes', [])

    # 生成CSRF令牌
    csrf_token = security_manager.generate_csrf_token()
    session['csrf_token'] = csrf_token

    return render_template('auth/setup_google_success.html',
                         backup_codes=backup_codes,
                         csrf_token=csrf_token,
                         username=username)


@auth_bp.route('/google-verify', methods=['GET'])
def google_verify():
    """Google认证验证页面"""
    # 检查是否已登录
    if not g.get('current_user'):
        return redirect(url_for('auth.login'))

    username = g.current_user.get('user_name')
    session_id = session.get('session_id')

    # 检查是否真的需要Google认证
    if not session_manager.is_google_auth_required(username, session_id):
        # 如果不需要认证，跳转到返回URL或首页
        return_url = request.args.get('return_url', '/')
        return redirect(return_url)

    # 获取返回URL和请求数据
    return_url = request.args.get('return_url', '/')
    request_data = request.args.get('request_data', '')

    return render_template('auth/google_verify.html',
                         return_url=return_url,
                         request_data=request_data)


@auth_bp.route('/verify-google', methods=['POST'])
@csrf_protect
def verify_google():
    """处理Google认证验证"""
    # 检查是否已登录
    if not g.get('current_user'):
        return jsonify({'success': False, 'message': '未登录'}), 401

    username = g.current_user.get('user_name')
    session_id = session.get('session_id')

    # 检查是否真的需要Google认证
    if not session_manager.is_google_auth_required(username, session_id):
        return jsonify({'success': False, 'message': '不需要Google认证'}), 400

    # 获取请求数据
    data = request.get_json()
    if not data:
        return jsonify({'success': False, 'message': '无效的请求数据'}), 400

    google_code = data.get('google_code', '').strip()
    backup_code = data.get('backup_code', '').strip()
    return_url = data.get('return_url', '/')

    # 验证Google认证码
    if google_code:
        if google_auth_manager.verify_totp_code(username, google_code):
            session_manager.update_google_auth_time(username, session_id)
            return jsonify({
                'success': True,
                'message': '验证成功',
                'redirect_url': return_url
            })
        else:
            return jsonify({'success': False, 'message': '验证码错误'}), 400
    elif backup_code:
        if google_auth_manager.verify_backup_code(username, backup_code):
            session_manager.update_google_auth_time(username, session_id)
            return jsonify({
                'success': True,
                'message': '验证成功',
                'redirect_url': return_url
            })
        else:
            return jsonify({'success': False, 'message': '备用码错误'}), 400
    else:
        return jsonify({'success': False, 'message': '请输入验证码或备用码'}), 400


# 保留旧的路由以兼容现有代码
@auth_bp.route('/google-verify-old', methods=['POST'])
def google_verify_old():
    """旧的Google认证验证（兼容性保留）"""
    # 获取session信息
    username = session.get('username')
    session_id = session.get('session_id')

    if not username or not session_id:
        return jsonify({'success': False, 'error': '未登录'}), 401

    # POST请求 - 验证Google认证码
    google_code = request.form.get('google_code', '').strip()
    backup_code = request.form.get('backup_code', '').strip()

    # 检查是否为AJAX请求
    is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest' or \
              request.content_type == 'application/json' or \
              'application/json' in request.headers.get('Accept', '')

    if google_code:
        if google_auth_manager.verify_totp_code(username, google_code):
            session_manager.update_google_auth_time(username, session_id)
            if is_ajax:
                return jsonify({'success': True, 'message': 'Google验证成功'})
            else:
                return redirect(url_for('index'))
        else:
            error_message = '验证码错误'
            if is_ajax:
                return jsonify({'success': False, 'error': error_message}), 400
            else:
                return render_template('auth/google_verify.html',
                                     error_message=error_message,
                                     csrf_token=session.get('csrf_token'))
    elif backup_code:
        if google_auth_manager.verify_backup_code(username, backup_code):
            session_manager.update_google_auth_time(username, session_id)
            if is_ajax:
                return jsonify({'success': True, 'message': 'Google验证成功'})
            else:
                return redirect(url_for('index'))
        else:
            error_message = '备用码错误'
            if is_ajax:
                return jsonify({'success': False, 'error': error_message}), 400
            else:
                return render_template('auth/google_verify.html',
                                     error_message=error_message,
                                     csrf_token=session.get('csrf_token'))
    else:
        error_message = '请输入验证码或备用码'
        if is_ajax:
            return jsonify({'success': False, 'error': error_message}), 400
        else:
            return render_template('auth/google_verify.html',
                                 error_message=error_message,
                                 csrf_token=session.get('csrf_token'))


# API路由
@auth_bp.route('/api/session-status', methods=['GET'])
def api_session_status():
    """检查会话状态API"""
    # 手动验证会话，不依赖中间件设置的g.current_user
    username = session.get('username')
    session_id = session.get('session_id')

    if not username or not session_id:
        return jsonify({'valid': False, 'reason': 'not_logged_in'})

    # 获取客户端IP
    client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)

    # 检查会话是否有效
    if not session_manager.validate_session(username, session_id, client_ip):
        return jsonify({'valid': False, 'reason': 'session_expired'})

    # 检查是否需要Google认证
    google_auth_required = session_manager.is_google_auth_required(username, session_id)
    
    return jsonify({
        'valid': True,
        'username': username,
        'google_auth_required': google_auth_required,
        'permissions': user_manager.get_user_permissions(username)
    })


@auth_bp.route('/api/google-verify', methods=['POST'])
@csrf_protect
def api_google_verify():
    """Google认证验证API"""
    # 手动验证会话，不依赖中间件设置的g.current_user
    username = session.get('username')
    session_id = session.get('session_id')

    if not username or not session_id:
        return jsonify({'success': False, 'message': '未登录'}), 401

    # 获取客户端IP
    client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)

    # 检查会话是否有效
    if not session_manager.validate_session(username, session_id, client_ip):
        return jsonify({'success': False, 'message': '会话已过期'}), 401
    google_code = request.json.get('google_code', '').strip()
    backup_code = request.json.get('backup_code', '').strip()
    
    if google_code:
        if google_auth_manager.verify_totp_code(username, google_code):
            session_manager.update_google_auth_time(username, session_id)
            return jsonify({'success': True, 'message': '验证成功'})
        else:
            return jsonify({'success': False, 'message': '验证码错误'})
    elif backup_code:
        if google_auth_manager.verify_backup_code(username, backup_code):
            session_manager.update_google_auth_time(username, session_id)
            return jsonify({'success': True, 'message': '验证成功'})
        else:
            return jsonify({'success': False, 'message': '备用码错误'})
    else:
        return jsonify({'success': False, 'message': '请提供验证码或备用码'})


# 错误处理
@auth_bp.errorhandler(429)
def handle_rate_limit(error):
    """处理速率限制错误"""
    return render_template('auth/login.html',
                         error_message='请求过于频繁，请稍后再试',
                         csrf_token=session.get('csrf_token')), 429
