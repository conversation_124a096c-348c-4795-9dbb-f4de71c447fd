# 邢不行® 策略分享会️

## 🗃️ 动态仓位管理实盘框架

✨ 全新的仓位管理实盘框架

- 🔮 支持动态仓位管理，
- ⌚ 支持择时策略，
- 🔀 具备多类型、多周期策略动态融合功能，
- 🪄 兼容所有选币策略，并提供灵活高效的多策略配置
- 👥 支持多账户和外部数据

### v1.3.2

- 新增带 offset 轮动策略文件
- config 增加了 json 文件配置支持(不影响目前实盘，后面有大用，这里保密)
- 更新了一下加仓统计

#### ℹ️ 更新说明

- [portfolio_margin_client.py](core/binance/portfolio_margin_client.py)，*相对于1.3.1有更新
- [statistics.py](core/utils/statistics.py)，*相对于1.3.1有更新
- [RotationStrategyOffset.py](positions/RotationStrategyOffset.py)，*相对于1.3.1有更新
- [config.py](config.py)，*相对于1.3.1有更新

#### 🆙 更新步骤

1. 📋 替换以下文件：
   - [portfolio_margin_client.py](core/binance/portfolio_margin_client.py)
   - [statistics.py](core/utils/statistics.py)
   - [RotationStrategyOffset.py](positions/RotationStrategyOffset.py)
   - [config.py](config.py)
2. 🔄 重启[`delist.py`](delist.py)
3. 🔄 重启[`monitor.py`](monitor.py)
4. 🔄 重启[`startup.py`](startup.py)

---


### v1.3.1

- 新增 market 配置(如果一下配置没有看懂，可以看一下@学费菌老板的帖子)
  - 选币市场范围 & 交易配置
  - 配置解释： 选币范围 + '_' + 优先交易币种类型
  - spot_spot: 在 '现货' 市场中进行选币。如果现货币种含有'合约'，优先交易 '现货'。
  - swap_swap: 在 '合约' 市场中进行选币。如果现货币种含有'现货'，优先交易 '合约'。
  - spot_swap: 在 '现货' 市场中进行选币。如果现货币种含有'合约',优先交易'合约'。
  - mix_spot:  在 '现货与合约' 的市场中进行选币。如果两边市场都存在的币种，会保留'现货'，并优先下单'现货'。
  - mix_swap:  在 '现货与合约' 的市场中进行选币。如果两边市场都存在的币种，会保留'合约'，并优先下单'合约'。
- 优化计算截面因子的内存
- 1.3.0的截面因子写法已经被抛弃(注意之前 config 配置现在迁移到截面因子内，参考`sections`案例因子)
- 移除配置可以控制现货中不含有合约，合约中不含有现货等设置。(建议通过因子来进行控制)


#### ℹ️ 更新说明

- [core](core)，*相对于1.3.0有更新
- [sections](sections)，*相对于1.3.0有更新
- [startup.py](startup.py)，*相对于1.3.0有更新
- [monitor.py](monitor.py)，*相对于1.3.0有更新
- [delist.py](delist.py)，*相对于1.3.0有更新

#### 🆙 更新步骤

1. 📋 替换以下文件：
   - [core](core)
   - [startup.py](startup.py)
   - [monitor.py](monitor.py)
   - [delist.py](delist.py)
2. 🔄 重启[`delist.py`](delist.py)
3. 🔄 重启[`monitor.py`](monitor.py)
4. 🔄 重启[`startup.py`](startup.py)

---

### v1.3.0

- 增加截面因子计算
- 统计脚本增加多空选币数量，轮动增加绘制子策略资金曲线
- 统计脚本只计算当前下单账号，相同分钟偏移不会多发消息(不建议一台服务器配置多个相同分钟偏移)
- 将最小下单金额下放到下单过滤中
- 增加配置项：现货模式中是否包含合约的币，合约模式中是包含现货的币
- 增加读取下架后又上架的币种市值数据处理
- 调整划转资金为可用资金
- 账户中移除掉黑名单币种净值，避免影响现货下单出现资金不足
- 增加新币无法设置杠杆的容错机制

#### ℹ️ 更新说明

- [core](core)，*相对于1.2.4有更新
- [startup.py](startup.py)，*相对于1.2.4有更新

#### 🆙 更新步骤

1. 📋 替换以下文件：
   - [core](core)
   - [startup.py](startup.py)
2. 🔄 重启[`delist.py`](delist.py)
3. 🔄 重启[`monitor.py`](monitor.py)
4. 🔄 重启[`startup.py`](startup.py)

---


### v1.2.4

- 解决部分情况下计算目标手数数据溢出的问题
- 更新 delist 脚本，兼容最新的下架公告
- 更新 delist 和 monitor 脚本，兼容最新拆单算法

#### ℹ️ 更新说明

- 相对于 `1.2.3` 只更新了如下几个文件：

- [rebalance.py](core/rebalance.py)，*相对于1.2.3有更新
- [delist.py](delist.py)，*相对于1.2.3有更新
- [monitor.py](monitor.py)，*相对于1.2.3有更新

#### 🆙 更新步骤

1. 📋 替换以下文件：
   - [rebalance.py](core/rebalance.py)
   - [delist.py](delist.py)
   - [monitor.py](monitor.py)
2. 🔄 重启[`delist.py`](delist.py)
3. 🔄 重启[`monitor.py`](monitor.py)
4. 🔄 重启[`startup.py`](startup.py)

---

### v1.2.3

- 修复仓位管理框架运行过程中丢失delist数据
- 修复统计脚本在统一账户模式下，统计多头仓位会多计算部分U头寸问题
- 更新统计脚本，支持发送仓位管理状态，仓位管理策略因子，多彩的资金曲线图
- 增加真随机下单功能。感恩[@我已无心战斗](https://bbs.quantclass.cn/user/37137)
- 增加新账户没充值时的容错处理

#### ℹ️ 更新说明

相对于 `1.2.2` 只更新了如下几个文件：

- [base_client.py](core/binance/base_client.py)，*相对于1.2.2有更新
- [portfolio_margin_client.py](core/binance/portfolio_margin_client.py)，*相对于1.2.2有更新
- [backtest_config.py](core/model/backtest_config.py)，*相对于1.2.2有更新
- [statistics.py](core/utils/statistics.py)，*相对于1.2.2有更新
- [strategy_hub.py](core/utils/strategy_hub.py)，*相对于1.2.2有更新
- [account_exec.py](core/account_exec.py)，*相对于1.2.2有更新
- [mef_manager.py](core/mef_manager.py)，*相对于1.2.2有更新
- [select_coin.py](core/select_coin.py)，*相对于1.2.2有更新
- [trade.py](core/trade.py)，*相对于1.2.2有更新


#### 🆙 更新步骤

1. 📋 替换 [`core`](core) 文件夹：
2. 🔄 重启[`startup.py`](startup.py)

---

### v1.2.2

🆘 紧急修复：**增量计算中** 新币仓位错位问题，引起回测资金曲线在某些情况下最终equity为0的情况，以及再择时杠杆对不齐的情况。

并优化统计脚本，针对子策略的name做了容错

> 发布时间: 2025-01-27

`1.2.1` 及之前的版本，会对如下策略，造成影响：

- 24年的轮动
- 以及部分择时策略了

#### ℹ️ 更新说明

该版本是针对 `1.2.1` 的补充更新，更新步骤会相对于 `1.2.0` 版的基础上进行说明。
为了不必要的麻烦，务必详细阅读并参考 **更新步骤** 。

相对于 `1.2.1` 只更新了如下几个文件：

- [core/equity.py](core/equity.py)，*相对于1.2.1有更新
- [core/utils/statistics.py](core/utils/statistics.py)，*相对于1.2.1有更新
- [startup.py](startup.py)，*相对于1.2.1有更新
- 以及 `accounts` 和 `examples` 文件夹中的默认配置都相应更新

#### 🆙 更新步骤

1. 📋 替换 [`core`](core) 文件夹，更新文件如下：
    - 替换 [`core/model/backtest_config.py`](core/model/backtest_config.py)
    - 替换 [`core/equity.py`](core/equity.py)，*相对于1.2.1有更新
    - 替换 [`core/backtest.py`](core/backtest.py)
    - 替换 [`core/version.py`](core/version.py)
2. 📋 替换全局配置文件 [`config.py`](config.py)
3. 📋 替换启动脚本 [`startup.py`](startup.py)，*相对于1.2.1有更新，新增了缓存控制
4. 🗑️ 删除本地缓存数据 [`data`](data) 文件夹
5. 🔄 重启[`startup.py`](startup.py)

- 🚨 注意事项：
    - 🚫 不能使用历史的snapshot，删除data文件夹再启动
    - 💡 该问题只影响 **实盘增量计算**，回测没有问题

#### 📁 更新文件

- [core/model/backtest_config.py](core/model/backtest_config.py)
- [core/backtest.py](core/backtest.py)
- [core/equity.py](core/equity.py)，*相对于1.2.1有更新
- [core/utils/statistics.py](core/utils/statistics.py)，*相对于1.2.1有更新
- [core/version.py](core/version.py)
- [config.py](config.py)
- [startup.py](startup.py)，*相对于1.2.1有更新
- [accounts](accounts) 文件夹，*相对于1.2.1有更新
- [examples](examples) 文件夹，*相对于1.2.1有更新
- [更新说明.md](%E6%9B%B4%E6%96%B0%E8%AF%B4%E6%98%8E.md)，本说明

----

### v1.2.1

❎ 不稳定，参考1.2.2合并后的更新提示

----

### v1.2.0 (不稳定)

🚨 必须配合最新的bmac数据客户端使用（1.1.0或更高版本）

> 发布时间: 2025-01-15

#### 更新内容

- 👥 **多账户支持**
- 🔗 **外部数据支持** 支持使用coin cap数据
- ✨ **delist优化**
- 💿 **IO优化** 配合新bmac数据中心，分批读入数据，优化磁盘性能
- 💾 **存储调整** 调整了存储数据的格式
- 🖖 **优化config的配置顺序**
- 🆕 **新增完整功能案例** 在`examples`文件夹下
- 🔧 **修复了一些问题**

#### 问题修复

- 针对下架又上架的同名币种，做了定向处理
- 解决资金曲线缺最后1H的问题
- 其他的一些细节修复

-----