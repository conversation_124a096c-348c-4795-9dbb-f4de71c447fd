# 资金曲线模块使用说明

## 概述

资金曲线模块是量化交易监控系统的第四个核心模块，专门用于展示和分析策略的资金走势。该模块提供了直观的图表展示、多策略对比、关键指标计算等功能。

## 功能特性

### 1. 策略列表管理
- 自动扫描 `data/子策略回测结果` 目录下的所有资金曲线文件
- 显示策略基本信息：总收益率、数据点数、时间范围
- 支持策略选择和多选功能
- 实时刷新策略列表

### 2. 单策略分析
- 资金曲线图表展示
- 关键指标计算：
  - 总收益率
  - 年化收益率
  - 最大回撤
  - 当前回撤
  - 年化波动率
  - 夏普比率
  - 交易天数

### 3. 多策略对比
- 支持同时对比多个策略
- 统一时间轴展示
- 不同颜色区分各策略
- 并排显示关键指标

### 4. 时间范围控制
- 自定义开始和结束时间
- 快速选择预设时间范围：
  - 近1个月
  - 近3个月
  - 近6个月
  - 近1年
  - 全部时间

## 使用方法

### 访问资金曲线模块
1. 启动Web管理界面：`python web_manager.py`
2. 在浏览器中访问：`http://localhost:5000`
3. 点击侧边栏的"资金曲线"菜单

### 查看单个策略
1. 在策略列表中点击选择一个策略
2. 系统自动显示时间范围控制面板
3. 调整时间范围（可选）
4. 点击"更新图表"查看资金曲线和统计指标

### 对比多个策略
1. 在策略列表中选择多个策略（至少2个）
2. 点击"对比策略"按钮
3. 调整时间范围（可选）
4. 查看对比图表和指标

## 数据格式要求

资金曲线CSV文件必须包含以下列：
- `candle_begin_time`: 时间戳列
- `净值`: 账户净值列

可选列：
- `胜率`
- `盈亏收益比`
- `总手续费`

## 技术实现

### 后端组件
- `FundCurveManager`: 资金曲线数据管理器
- API接口：
  - `/api/fund-curve/strategies`: 获取策略列表
  - `/api/fund-curve/strategy-data`: 获取单个策略数据
  - `/api/fund-curve/compare`: 对比多个策略

### 前端组件
- 基于Bootstrap 5的现代化界面
- Chart.js图表库实现数据可视化
- 响应式设计，支持移动端访问

### 关键指标计算
- **年化收益率**: `(最终净值/初始净值)^(365/天数) - 1`
- **最大回撤**: `min((净值 - 累计最高净值) / 累计最高净值)`
- **夏普比率**: `年化收益率 / 年化波动率`
- **年化波动率**: `日收益率标准差 * sqrt(252)`

## 测试验证

运行测试脚本验证功能：
```bash
python test_fund_curve.py
```

测试内容包括：
- 策略列表获取
- 单个策略数据读取
- 多策略对比功能

## 注意事项

1. 确保数据目录结构正确：`data/子策略回测结果/策略名/资金曲线.csv`
2. CSV文件编码建议使用UTF-8
3. 时间戳格式支持多种标准格式，系统会自动解析
4. 大量数据可能影响加载速度，建议合理设置时间范围

## 故障排除

### 常见问题
1. **找不到策略文件**: 检查数据目录路径和文件结构
2. **图表不显示**: 检查浏览器控制台错误信息
3. **数据加载失败**: 验证CSV文件格式和必要列

### 日志查看
- Web服务器日志会显示API请求和错误信息
- 使用浏览器开发者工具查看前端错误

## 扩展功能

未来可以考虑添加的功能：
- 更多技术指标计算
- 数据导出功能
- 策略排名和筛选
- 风险指标分析
- 回撤分析详情
