#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
量化交易系统用户配置文件
包含用户认证、权限管理、会话管理等功能
"""

user_list = [
    {
        # 基础用户信息
        'user_name': 'admin',
        'password': '0192023a7bbd73250516f069df18b500',
        'role': 'admin',

        # Google Authenticator双因素认证
        'google_secret_key': '3HLPRWUSHBJM42ISJBVPLAKEZIJJFATE',
        'is_google_auth': 'Y',
        'google_backup_codes': ['14595663', '95115688', '55924987', '45857952', '97160305', '53130892', '44566554', '39566398', '61826355', '65439311'],
        'force_google_setup': False,

        # 会话管理
        'session_expire_list': [{'ip': '127.0.0.1', 'expire_time': '2025-07-31 20:55:25', 'first_login_time': '2025-07-31 17:55:25', 'session_id': '238d7c10-5ef8-4dc1-b984-8f1de9749888', 'google_first_auth_time': '2025-07-31 17:55:25', 'user_agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'last_activity': '2025-07-31 17:56:26', 'device_fingerprint': '3b163ffbdf0f7bd42d6285f7c4636a0b'}, {'ip': '127.0.0.1', 'expire_time': '2025-07-31 20:39:22', 'first_login_time': '2025-07-31 17:39:22', 'session_id': 'aafcdf5b-3bcd-4cc3-bae4-e54d5cd67145', 'google_first_auth_time': '2025-07-31 17:39:22', 'user_agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'last_activity': '2025-07-31 17:45:28', 'device_fingerprint': '3b163ffbdf0f7bd42d6285f7c4636a0b'}, {'ip': '127.0.0.1', 'expire_time': '2025-07-31 20:31:07', 'first_login_time': '2025-07-31 17:31:07', 'session_id': 'c739c093-ee87-4f55-a744-d1f41f50861c', 'google_first_auth_time': '2025-07-31 17:31:07', 'user_agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'last_activity': '2025-07-31 17:35:02', 'device_fingerprint': '3b163ffbdf0f7bd42d6285f7c4636a0b'}],

        # 安全设置
        'failed_login_attempts': 0,
        'locked_until': None,
        'last_login_time': '2025-07-31 17:55:25',
        'last_login_ip': '127.0.0.1',
        'password_changed_time': '',

        # 登录历史记录
        'login_history': [{'login_time': '2025-07-31 17:55:25', 'ip': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'success': True, 'failure_reason': ''}, {'login_time': '2025-07-31 17:39:22', 'ip': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'success': True, 'failure_reason': ''}, {'login_time': '2025-07-31 17:31:07', 'ip': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'success': True, 'failure_reason': ''}, {'login_time': '2025-07-31 17:21:49', 'ip': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'success': True, 'failure_reason': ''}, {'login_time': '2025-07-31 17:21:35', 'ip': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'success': False, 'failure_reason': '密码错误'}, {}],

        # 用户偏好设置
        'preferences': {
            'remember_me': False,
            'session_timeout': 180,
            'google_auth_timeout': 10,
            'language': 'zh-CN',
            'theme': 'modern',
        },

        # 权限设置
        'permissions': {
            'program_control': True,
            'account_management': True,
            'global_config': True,
            'fund_curve': True,
            'system_logs': True,
            'user_management': True,
        }
    }
]

# 认证系统配置
AUTH_CONFIG = {
    'max_login_attempts': 5,
    'lockout_duration': 600,
    'session_lifetime': 10800,
    'google_auth_window': 600,
    'password_min_length': 6,
    'require_google_auth': True,
    'max_sessions_per_user': 3,
    'session_cleanup_interval': 3600,
    'login_history_limit': 20,
    'backup_codes_count': 10,
}

# 安全配置
SECURITY_CONFIG = {
    'csrf_protection': True,
    'secure_cookies': True,
    'https_only': False,
    'ip_whitelist': [],
    'rate_limiting': {
        'enabled': True,
        'max_requests': 100,
        'window': 60,
    },
}