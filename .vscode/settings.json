{
    // 1. 为代码分析、格式化和调试等核心功能指定解释器
    "python.defaultInterpreterPath": "/opt/anaconda3/envs/py312venv/bin/python",

    // 2. 简化的终端配置，避免复杂的激活流程导致卡死
    "terminal.integrated.profiles.osx": {
        "bash": {
            "path": "/bin/bash"
        }
    },
    "terminal.integrated.defaultProfile.osx": "bash",

    // 3. 为“Augment Code”等后台扩展使用的bash终端，直接注入正确的PATH路径 (🔥最终修复)
    "terminal.integrated.automationProfile.osx": {
        "path": "/bin/bash"
    },

    // 4. 简化的环境变量设置，只保留必要的PATH配置
    "terminal.integrated.env.osx": {
        "PATH": "/opt/anaconda3/envs/py312venv/bin:/opt/anaconda3/condabin:${env:PATH}"
    }
}
