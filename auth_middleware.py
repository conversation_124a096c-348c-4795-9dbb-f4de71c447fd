#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
量化交易系统认证中间件
提供登录验证、权限控制、CSRF保护等安全功能
"""

import functools
import time
from datetime import datetime
from typing import Dict, List, Optional, Callable, Any
from flask import request, session, redirect, url_for, jsonify, abort, g
from werkzeug.exceptions import TooManyRequests, Forbidden

from auth_manager import user_manager, session_manager, google_auth_manager, security_manager


class AuthMiddleware:
    """认证中间件类"""
    
    def __init__(self, app=None):
        self.app = app
        self.rate_limit_storage = {}  # 存储速率限制数据
        self.failed_attempts = {}     # 存储失败尝试数据
        
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """初始化Flask应用"""
        self.app = app
        
        # 注册请求前处理器
        app.before_request(self.before_request)
        
        # 注册请求后处理器
        app.after_request(self.after_request)
        
        # 注册错误处理器
        app.errorhandler(401)(self.handle_unauthorized)
        app.errorhandler(403)(self.handle_forbidden)
        app.errorhandler(429)(self.handle_rate_limit_exceeded)
    
    def before_request(self):
        """请求前处理"""
        # 获取客户端信息
        g.client_ip = self.get_client_ip()
        g.user_agent = request.headers.get('User-Agent', '')
        g.current_user = None
        g.session_id = None
        
        # 检查IP白名单
        if not security_manager.check_ip_whitelist(g.client_ip):
            abort(403, "IP地址不在白名单中")
        
        # 检查速率限制
        if not self.check_rate_limit(g.client_ip):
            abort(429, "请求过于频繁")
        
        # 跳过认证检查的路径
        skip_auth_paths = [
            '/auth/login',
            '/auth/logout',
            '/static/',
            '/favicon.ico',
            '/auth/api/session-status',
            '/auth/api/google-verify'
        ]
        
        # 检查是否需要跳过认证
        for path in skip_auth_paths:
            if request.path.startswith(path):
                return
        
        # 验证会话
        if not self.validate_session():
            if request.path.startswith('/api/'):
                return jsonify({'error': '会话已过期', 'code': 401, 'session_expired': True}), 401
            else:
                return redirect(url_for('auth.login'))

        # 检查Google认证
        if self.is_google_auth_required():
            if request.path.startswith('/api/'):
                return jsonify({'error': 'Google认证已过期', 'code': 403, 'google_auth_required': True}), 403
            else:
                # 对于页面请求，让页面JavaScript处理Google认证
                pass
    
    def after_request(self, response):
        """请求后处理"""
        # 添加安全头
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # 如果是HTTPS，添加HSTS头
        if request.is_secure:
            response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        
        return response
    
    def get_client_ip(self) -> str:
        """获取客户端IP地址"""
        # 检查代理头
        if request.headers.get('X-Forwarded-For'):
            return request.headers.get('X-Forwarded-For').split(',')[0].strip()
        elif request.headers.get('X-Real-IP'):
            return request.headers.get('X-Real-IP')
        else:
            return request.remote_addr or '127.0.0.1'
    
    def check_rate_limit(self, ip: str) -> bool:
        """检查速率限制"""
        return security_manager.check_rate_limit(ip)
    
    def validate_session(self) -> bool:
        """验证会话"""
        # 从session中获取用户信息
        username = session.get('username')
        session_id = session.get('session_id')
        
        if not username or not session_id:
            return False
        
        # 验证会话有效性
        if not session_manager.validate_session(username, session_id, g.client_ip):
            # 清除无效会话
            session.clear()
            return False
        
        # 设置当前用户信息
        g.current_user = user_manager.get_user(username)
        g.session_id = session_id
        
        return True
    
    def is_google_auth_required(self) -> bool:
        """检查是否需要Google认证"""
        if not g.current_user:
            return False
        
        username = g.current_user.get('user_name')
        session_id = g.session_id
        
        return session_manager.is_google_auth_required(username, session_id)
    
    def handle_unauthorized(self, error):
        """处理401错误"""
        if request.path.startswith('/api/'):
            return jsonify({'error': '未授权访问', 'code': 401}), 401
        else:
            return redirect(url_for('auth.login'))

    def handle_forbidden(self, error):
        """处理403错误"""
        if request.path.startswith('/api/'):
            return jsonify({'error': '访问被拒绝', 'code': 403}), 403
        else:
            return redirect(url_for('auth.login', error='access_denied'))

    def handle_rate_limit_exceeded(self, error):
        """处理429错误"""
        if request.path.startswith('/api/'):
            return jsonify({'error': '请求过于频繁', 'code': 429}), 429
        else:
            return redirect(url_for('auth.login', error='rate_limit'))


def login_required(f: Callable) -> Callable:
    """登录验证装饰器"""
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        if not g.get('current_user'):
            if request.path.startswith('/api/'):
                return jsonify({'error': '需要登录', 'code': 401}), 401
            else:
                return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function


def permission_required(permission: str):
    """权限验证装饰器"""
    def decorator(f: Callable) -> Callable:
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            if not g.get('current_user'):
                if request.path.startswith('/api/'):
                    return jsonify({'error': '需要登录', 'code': 401}), 401
                else:
                    return redirect(url_for('auth.login'))
            
            username = g.current_user.get('user_name')
            if not user_manager.has_permission(username, permission):
                if request.path.startswith('/api/'):
                    return jsonify({'error': '权限不足', 'code': 403}), 403
                else:
                    abort(403)
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator


def google_auth_required(f: Callable) -> Callable:
    """Google认证验证装饰器"""
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        if not g.get('current_user'):
            if request.path.startswith('/api/'):
                return jsonify({'error': '需要登录', 'code': 401}), 401
            else:
                return redirect(url_for('auth.login'))
        
        username = g.current_user.get('user_name')
        session_id = g.get('session_id')
        
        if session_manager.is_google_auth_required(username, session_id):
            if request.path.startswith('/api/'):
                return jsonify({'error': '需要Google认证', 'code': 403, 'google_auth_required': True}), 403
            else:
                # 重定向到Google认证页面或在当前页面显示认证提示
                return redirect(url_for('auth.google_verify'))
        
        return f(*args, **kwargs)
    return decorated_function


def admin_required(f: Callable) -> Callable:
    """管理员权限装饰器"""
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        if not g.get('current_user'):
            if request.path.startswith('/api/'):
                return jsonify({'error': '需要登录', 'code': 401}), 401
            else:
                return redirect(url_for('auth.login'))
        
        if g.current_user.get('role') != 'admin':
            if request.path.startswith('/api/'):
                return jsonify({'error': '需要管理员权限', 'code': 403}), 403
            else:
                abort(403)
        
        return f(*args, **kwargs)
    return decorated_function


def csrf_protect(f: Callable) -> Callable:
    """CSRF保护装饰器"""
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        if request.method in ['POST', 'PUT', 'DELETE', 'PATCH']:
            # 检查CSRF令牌
            token = request.form.get('csrf_token') or request.headers.get('X-CSRFToken')
            session_token = session.get('csrf_token')
            
            if not token or not session_token or not security_manager.validate_csrf_token(token, session_token):
                if request.path.startswith('/api/'):
                    return jsonify({'error': 'CSRF令牌无效', 'code': 403}), 403
                else:
                    abort(403)
        
        return f(*args, **kwargs)
    return decorated_function


def rate_limit(max_requests: int = 100, window: int = 60):
    """速率限制装饰器"""
    def decorator(f: Callable) -> Callable:
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            ip = g.get('client_ip', request.remote_addr)
            
            # 检查速率限制
            current_time = time.time()
            key = f"{ip}:{request.endpoint}"
            
            if not hasattr(g, 'rate_limit_storage'):
                g.rate_limit_storage = {}
            
            if key not in g.rate_limit_storage:
                g.rate_limit_storage[key] = []
            
            # 清理过期记录
            g.rate_limit_storage[key] = [
                req_time for req_time in g.rate_limit_storage[key]
                if current_time - req_time < window
            ]
            
            # 检查是否超过限制
            if len(g.rate_limit_storage[key]) >= max_requests:
                if request.path.startswith('/api/'):
                    return jsonify({'error': '请求过于频繁', 'code': 429}), 429
                else:
                    abort(429)
            
            # 记录当前请求
            g.rate_limit_storage[key].append(current_time)
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator


# 创建中间件实例
auth_middleware = AuthMiddleware()
