#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
量化交易系统 Web 管理界面启动脚本
作者: AI Assistant
功能: 检查依赖、安装缺失的包并启动Web管理界面
"""

import os
import sys
import subprocess
import importlib
from pathlib import Path

def check_and_install_dependencies():
    """检查并安装依赖包"""
    required_packages = [
        'flask==3.0.0',
        'flask-socketio==5.3.6', 
        'psutil==5.9.6',
        'pyyaml==6.0.1',
        'python-dotenv==1.0.0'
    ]
    
    missing_packages = []
    
    # 检查每个包是否已安装
    for package in required_packages:
        package_name = package.split('==')[0].replace('-', '_')
        try:
            importlib.import_module(package_name)
            print(f"✓ {package_name} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package_name} 未安装")
    
    # 安装缺失的包
    if missing_packages:
        print(f"\n需要安装 {len(missing_packages)} 个缺失的包...")
        
        for package in missing_packages:
            print(f"正在安装 {package}...")
            try:
                subprocess.check_call([
                    sys.executable, '-m', 'pip', 'install', package
                ])
                print(f"✓ {package} 安装成功")
            except subprocess.CalledProcessError as e:
                print(f"✗ {package} 安装失败: {e}")
                return False
    
    return True

def create_directories():
    """创建必要的目录"""
    directories = [
        'templates',
        'static/css',
        'static/js',
        'logs',
        'accounts'
    ]
    
    for directory in directories:
        dir_path = Path(directory)
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"✓ 目录 {directory} 已创建")

def check_project_structure():
    """检查项目结构"""
    required_files = [
        'web_manager.py',
        'templates/base_modern.html',
        'templates/index_modern.html',
        'templates/config_modern.html',
        'templates/accounts_modern.html',
        'templates/global_config_modern.html'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("缺失以下必要文件:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    
    print("✓ 项目结构检查通过")
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("量化交易系统 Web 管理界面启动器")
    print("=" * 60)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        sys.exit(1)
    
    print(f"✓ Python版本: {sys.version}")
    
    # 检查项目结构
    print("\n1. 检查项目结构...")
    if not check_project_structure():
        print("错误: 项目结构不完整，请确保所有必要文件都存在")
        sys.exit(1)
    
    # 创建必要目录
    print("\n2. 创建必要目录...")
    create_directories()
    
    # 检查并安装依赖
    print("\n3. 检查依赖包...")
    if not check_and_install_dependencies():
        print("错误: 依赖包安装失败")
        sys.exit(1)
    
    # 启动Web管理界面
    print("\n4. 启动Web管理界面...")
    print("-" * 60)
    print("Web管理界面正在启动...")
    print("访问地址: http://localhost:5000")
    print("按 Ctrl+C 停止服务")
    print("-" * 60)
    
    try:
        # 导入并启动Web管理器
        from web_manager import app, socketio
        socketio.run(app, host='0.0.0.0', port=5000, debug=False)
    except KeyboardInterrupt:
        print("\n\n服务已停止")
    except Exception as e:
        print(f"\n启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
