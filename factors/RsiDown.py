import pandas as pd
import numpy as np
from typing import Union, Tuple


def signal(*args):
    """
    计算RSI指标，使用固定周期14，判断RSI是否在50-75区间且斜率朝上
    :param args: 第一个参数为币对数据，第二个参数为RSI阈值（保留参数兼容性），第三个参数为因子名称
    :return: 返回添加了RSI因子值的DataFrame
    """
    df = args[0]
    threshold = args[1]  # RSI阈值（保留参数兼容性，但不使用）
    factor_name = args[2]
    
    # RSI计算周期固定为14
    rsi_period = 14
    
    # 计算价格变化
    price_change = df['close'].diff()
    
    # 分离上涨和下跌
    gains = price_change.where(price_change > 0, 0)
    losses = -price_change.where(price_change < 0, 0)
    
    # 计算平均收益和平均损失
    avg_gains = gains.rolling(window=rsi_period).mean()
    avg_losses = losses.rolling(window=rsi_period).mean()
    
    # 计算相对强度指数RS
    rs = avg_gains / avg_losses
    
    # 计算RSI
    rsi = 100 - (100 / (1 + rs))
    
    # 计算RSI的斜率（当前RSI - 前一个RSI）
    rsi_slope = rsi.diff()
    
    # 判断条件：RSI在50-75区间且斜率朝上（大于0）
    # 满足条件: 1, 不满足条件: 0
    # condition = (rsi > 50) & (rsi < threshold) & (rsi_slope > 0)
    condition = rsi < threshold
    df[factor_name] = np.where(
        condition, 
        1,  # 状态1：RSI小于阈值
        0   # 状态0：RSI大于等于阈值
    )
    
    return df