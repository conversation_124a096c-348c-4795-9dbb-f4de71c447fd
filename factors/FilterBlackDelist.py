"""
邢不行｜策略分享会
仓位管理框架

版权所有 ©️ 邢不行
微信: xbx1717

本代码仅供个人学习使用，未经授权不得复制、修改或用于商业用途。

Author: 邢不行
"""
import numpy as np


def signal(*args):
    df = args[0]
    n = args[1]
    factor_name = args[2]

    # 举例：black_list = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'BNBUSDT']
    # 20250502下架： 'ALPACAUSDT', 'PDAUSDT', 'VIBUSDT', 'WINGUSDT'
    # 拉黑：ATM,ALCX,ASR,ACM,BIFI,DATA,KMD,PSG,PORTO,PIVX,WAN
    # 原因：BN 每个月初会添加一部分币进入观察名单中，这会导致加入名单的币会下跌，研究发现市值最小的10%更容易被加入到观察名单中，这是彩虹官方筛选一批小市值币种，供实盘拉黑
    # 20250626下架0704正式下架：'ALPHAUSDT', 'BSWUSDT', 'KMDUSDT', 'LEVERUSDT', 'LTOUSDT'
    black_list = [
        'ALPHAUSDT', 'BSWUSDT', 'KMDUSDT', 'LEVERUSDT', 'LTOUSDT'
    ]
    df[factor_name] = np.where(df['symbol'].isin(black_list), 0, 1)

    return df
