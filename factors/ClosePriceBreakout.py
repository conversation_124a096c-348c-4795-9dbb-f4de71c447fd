import pandas as pd
import numpy as np
from typing import Union, Tuple


def signal(*args):
    """
    计算价格突破指标，判断价格是否突破前期最低点加上阈值
    :param args: 第一个参数为币对数据，第二个参数为回看周期，第三个参数为因子名称
    :return: 返回添加了价格突破因子值的DataFrame
    """
    df = args[0]
    n = args[1]  # 回看周期，用于确定前低
    factor_name = args[2]
    threshold = 0.06 #0.02 0.04  # 阈值设置为2%
    
    # 获取前N个周期的最低价
    low_min = df['low'].rolling(window=n).min().shift(1)
    
    # 计算突破阈值
    breakout_level = low_min * (1 + threshold)
    
    # 创建状态列
    # 状态1：当前价格高于前低加上阈值，设置为1
    # 状态0：当前价格低于前低加上阈值，设置为0
    df[factor_name] = np.where(
        df['close'] > breakout_level, 
        1,  # 状态1：高于前低加上阈值
        0   # 状态0：低于前低加上阈值
    )
    
    return df 