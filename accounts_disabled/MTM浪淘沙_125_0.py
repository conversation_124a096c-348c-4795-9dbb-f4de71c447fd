# ====================================================================================================
# ** 账户配置 **
# ====================================================================================================
account_config = {
    # 交易所API配置
    'apiKey': '',
    'secret': '',
    'account_type': '普通账户',
    # ++++ 分钟偏移功能 ++++
    # 支持任意时间开始的小时级别K线
    "hour_offset": '0m',  # 分钟偏移设置，可以自由设置时间，配置必须是kline脚本中interval的倍数。默认：0m，表示不偏移。15m，表示每个小时偏移15m下单。
    # ++++ 企业微信机器人功能 ++++
    "wechat_webhook_url": 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5d1e265e-1c47-4f7a-922c-b2ca904120cc',

    # ++++ 下单时动态拆单功能 ++++
    "max_one_order_amount": 100,  # 最大拆单金额。
    "twap_interval": 2,  # 下单间隔

    # ++++ BNB抵扣手续费功能 ++++
    "if_use_bnb_burn": False,  # 是否开启BNB燃烧，抵扣手续费
    "buy_bnb_value": 11,  # 买多少U的bnb来抵扣手续费。建议最低11U，现货最小下单量限制10U

    # ++++ 小额资产自动兑换BNB功能 ++++
    # 当且仅当 `if_use_bnb_burn` 为 True 时生效
    "if_transfer_bnb": True,  # 是否开启小额资产兑换BNB功能。仅现货模式下生效
}

# ====================================================================================================
# ** 策略细节配置 **
# 案例策略，需要自己探索，不保证可用
# ====================================================================================================
backtest_name = 'MTM浪淘沙_125_0'  # 策略名称。用于标识当前运行的策略配置
get_kline_num = 10000  # 获取K线数量。日线策略代表日线K线数量，小时策略代表小时K线数量
strategy_config = {
    'name': 'FixedRatioStrategy',  # *必填。资金平衡方式，用于管理账户资金分配策略
    'hold_period': '1H',  # *必填。资金平衡周期。资金重新平衡的时间间隔，例：1H，6H，3D，7D......
    'params': {  # 非必填。资金平衡策略的参数配置
        'cap_ratios': [1.0]
    }
}

strategy_pool = [
    dict(
        name='MTM浪淘沙_125_0',
        strategy_list=[
        # ===========================================================
        # !!! 实盘前先回测一下，不要无脑跑案例策略，没人能保证案例策略能赚钱 !!!
        # 以下配置非官方提供的案例策略，这里只是举例如何配置，具体配置请自行处理
        # ===========================================================
        {
            # 策略名称。与strategy目录中的策略文件名保持一致。
            "strategy": "Strategy_MtmStd",
            "offset_list": [0],
            "hold_period": "1H",
            "is_use_spot": False,
            # 资金权重。程序会自动根据这个权重计算你的策略占比，具体可以看1.8的直播讲解
            "cap_weight": 3 / 3 / 3,
            # 选币数量，1，2，3等数字代表选币的个数，0.1，0.2代表选币的比例。
            # 选币可以为0。空头为0代表纯多，多头为0代表纯空。
            "long_cap_weight": 1,  # 策略内多头资金权重
            "short_cap_weight": 0,  # 策略内空头资金权重
            "long_select_coin_num": 1,
            "short_select_coin_num": 0,
            # 选币因子信息列表，用于`2_选币_单offset.py`，`3_计算多offset资金曲线.py`共用计算资金曲线
            "factor_list": [
                ('Mtmstd', False, param, 1)  # 多头因子名（和factors文件中相同），排序方式，参数，权重。
            ],
            "filter_list": [
                ('新币', 1 ,'val:>=168'),
                ('FilterSXL', 24, 'val:>0', False),
                ('Pctmax', 24, 'val:<0.2', True), # 因子名（和factors文件中相同），参数
                ('QuoteVolumeMean', 24, 'rank:<=30', False),
            ],
            "filter_list_post": [
                # ('Pctmax', 24, 'val:<0.2', True),
                ('ZfMeanQ', y, 'val:>0.5', True),
                ('RsiDown', 85, 'val:>0', True)
            ],
            "use_custom_func": False  # 使用系统内置因子计算、过滤函数
        } for param in [70, 90, 110] for y in [240, 432, 600]
    ] + [
        {
                # 策略名称。与strategy目录中的策略文件名保持一致。
                "strategy": "ILLQStd策略",
                "offset_list": [14],
                "hold_period": '24H',
                "is_use_spot": False,
                # 资金权重。程序会自动根据这个权重计算你的策略占比，具体可以看1.8的直播讲解
                "cap_weight": 12 / 3 / 3,
                "long_cap_weight": 1,  # 策略内多头资金权重
                "short_cap_weight": 0,  # 策略内空头资金权重
                # 选币数量，1，2，3等数字代表选币的个数，0.1，0.2代表选币的比例。
                # 选币可以为0。空头为0代表纯多，多头为0代表纯空。
                "long_select_coin_num": (0.1,0.2),
                "short_select_coin_num": 0,
                # 选币因子信息列表，用于`2_选币_单offset.py`，`3_计算多offset资金曲线.py`共用计算资金曲线
                "factor_list": [
                    ('ILLQStd', True, param, 1)  # 多头因子名（和factors文件中相同），排序方式，参数，权重。
                ],
                "filter_list": [
                    ('新币', 1 ,'val:>=168'),
                    ('FilterSXL', 24, 'val:>0', False),
                    ('PctChange', 456, 'pct:<0.8'),  # 因子名（和factors文件中相同），参数                    
                ],
                "filter_list_post": [
                    ('ClosePriceBreakout', y, 'val:>0', True)
                ],
                "use_custom_func": False,
            } for param in [80, 100, 280]for y in [64, 96, 112]
        ] + [
            {
                "strategy": "Strategy_空头",
                "offset_list": [13],
                "hold_period": '24H',
                "is_use_spot": False,
                # 资金权重。程序会自动根据这个权重计算你的策略占比，具体可以看1.8的直播讲解
                'cap_weight': 3 / 3,
                'long_cap_weight': 0,
                'short_cap_weight': 1,
                'long_select_coin_num': 0,
                'short_select_coin_num': 0.2,
                # 选币因子信息列表，用于`2_选币_单offset.py`，`3_计算多offset资金曲线.py`共用计算资金曲线
                "factor_list": [
                    ('Cci', False, param, 1),  # 多头因子名（和factors文件中相同），排序方式，参数，权重。
                ],
                "filter_list": [
                    ('新币', 1 ,'val:>=168'),
                    ('FilterSXL', 24, 'val:>0', False),
                    ('Pctmax', 24, 'val:<0.2', True),
                    ('QuoteVolumeMean', 110, 'pct:<0.2', False)
                ],
                "short_filter_list_post": [
                    # ('EMA', 7 * 24, 'val:<0', True),  # 价格在EMA之上
                    # ('CrossMA', (13 * 24, 21 * 24), 'val:<0', True),  # 快线在慢线下方且差值超过5%
                    # ('ZfMeanQ', x, 'val:<0.5'),
                    ('RsiUP', 30, 'val:>0', True)
                ],
                "use_custom_func": False  # 使用系统内置因子计算、过滤函数
            } for param in [180, 220, 780] 
        ] + [
            {
                "strategy": "Strategy_空头",
                "offset_list": [13],
                "hold_period": '24H',
                "is_use_spot": False,
                # 资金权重。程序会自动根据这个权重计算你的策略占比，具体可以看1.8的直播讲解
                'cap_weight': 3 / 3 / 3,
                'long_cap_weight': 0,
                'short_cap_weight': 1,
                'long_select_coin_num': 0,
                'short_select_coin_num': 0.1,
                # 选币因子信息列表，用于`2_选币_单offset.py`，`3_计算多offset资金曲线.py`共用计算资金曲线
                "factor_list": [
                    ('Cci', False, param, 1),  # 多头因子名（和factors文件中相同），排序方式，参数，权重。
                ],
                "filter_list": [
                    ('新币', 1 ,'val:>=168'),
                    ('FilterSXL', 24, 'val:>0', False),
                    ('Pctmax', 24, 'val:<0.2', True),
                    ('QuoteVolumeMean', 110, 'pct:<0.2', False)
                ],
                "short_filter_list_post": [
                    # ('EMA', 7 * 24, 'val:<0', True),  # 价格在EMA之上
                    # ('CrossMA', (13 * 24, 21 * 24), 'val:<0', True),  # 快线在慢线下方且差值超过5%
                    ('ZfMeanQ', x, 'val:<0.5'),
                ],
                "use_custom_func": False  # 使用系统内置因子计算、过滤函数
            } for param in [180, 220, 780] for x in [240, 432, 600] 
        ] + [
            {
                "strategy": "Strategy_空头",
                "offset_list": [14],
                "hold_period": '16H',
                "is_use_spot": False,
                # 资金权重。程序会自动根据这个权重计算你的策略占比，具体可以看1.8的直播讲解
                'cap_weight': 3,
                'long_cap_weight': 0,
                'short_cap_weight': 1,
                'long_select_coin_num': 0,
                'short_select_coin_num': 0.2,
                # 选币因子信息列表，用于`2_选币_单offset.py`，`3_计算多offset资金曲线.py`共用计算资金曲线
                "factor_list": [
                    ('Cci', False, param, 1),  # 多头因子名（和factors文件中相同），排序方式，参数，权重。
                ],
                "filter_list": [
                    ('新币', 1 ,'val:>=168'),
                    ('FilterSXL', 24, 'val:>0', False),
                    ('Pctmax', 24, 'val:<0.2', True),
                    ('QuoteVolumeMean', 110, 'pct:<0.2', False)
                ],
                "short_filter_list_post": [
                    # ('EMA', 7 * 24, 'val:<0', True),  # 价格在EMA之上
                    # ('CrossMA', (13 * 24, 21 * 24), 'val:<0', True),  # 快线在慢线下方且差值超过5%
                    ('RsiUP', 30, 'val:>0', True)
                ],
                "use_custom_func": False  # 使用系统内置因子计算、过滤函数
            } for param in [225]
        ] + [
            {
                "strategy": "Strategy_空头",
                "offset_list": [14],
                "hold_period": '16H',
                "is_use_spot": False,
                # 资金权重。程序会自动根据这个权重计算你的策略占比，具体可以看1.8的直播讲解
                'cap_weight': 3 / 3,
                'long_cap_weight': 0,
                'short_cap_weight': 1,
                'long_select_coin_num': 0,
                'short_select_coin_num': 0.1,
                # 选币因子信息列表，用于`2_选币_单offset.py`，`3_计算多offset资金曲线.py`共用计算资金曲线
                "factor_list": [
                    ('Cci', False, param, 1),  # 多头因子名（和factors文件中相同），排序方式，参数，权重。
                ],
                "filter_list": [
                    ('新币', 1 ,'val:>=168'),
                    ('FilterSXL', 24, 'val:>0', False),
                    ('Pctmax', 24, 'val:<0.2', True),
                    ('QuoteVolumeMean', 110, 'pct:<0.2', False)
                ],
                "short_filter_list_post": [
                    # ('EMA', 7 * 24, 'val:<0', True),  # 价格在EMA之上
                    # ('CrossMA', (13 * 24, 21 * 24), 'val:<0', True),  # 快线在慢线下方且差值超过5%
                    ('ZfMeanQ', x, 'val:<0.5'),
                ],
                "use_custom_func": False  # 使用系统内置因子计算、过滤函数
            } for param in [225] for x in [240, 432, 600] 
        ] + [
    {
        "strategy": "Strategy_空头",
        "offset_list": [7],
        "hold_period": '8H',
        "is_use_spot": False,
        # 资金权重。程序会自动根据这个权重计算你的策略占比，具体可以看1.8的直播讲解
        'cap_weight': 3 / 2 / 3,
        'long_cap_weight': 0,
        'short_cap_weight': 1,
        'long_select_coin_num': 0,
        'short_select_coin_num': 0.1,
        # 选币因子信息列表，用于`2_选币_单offset.py`，`3_计算多offset资金曲线.py`共用计算资金曲线
        "factor_list": [
            ('Cci', False, param, 1),  # 多头因子名（和factors文件中相同），排序方式，参数，权重。
        ],
        "filter_list": [
            ('新币', 1 ,'val:>=168'),
            ('FilterSXL', 24, 'val:>0', False),
            ('Pctmax', 24, 'val:<0.2', True),
            ('QuoteVolumeMean', 110, 'pct:<0.2', False)            
        ],
        "short_filter_list_post": [
            # ('EMA', 7 * 24, 'val:<0', True),  # 价格在EMA之上
            # ('CrossMA', (13 * 24, 21 * 24), 'val:<0', True),  # 快线在慢线下方且差值超过5%
            ('ZfMeanQ', x, 'val:<0.5'),
        ],
        "use_custom_func": False  # 使用系统内置因子计算、过滤函数
    } for param in [185, 200] for x in [240, 432, 600] 
]
    )
]

leverage = 1.25  # 杠杆数。我看哪个赌狗要把这里改成大于1的。高杠杆如梦幻泡影。不要想着一夜暴富，脚踏实地赚自己该赚的钱。
black_list = []  # 拉黑名单，永远不会交易。不喜欢的币、异常的币。例：LUNA-USDT, 这里与实盘不太一样，需要有'-'
white_list = []  # 如果不为空，即只交易这些币，只在这些币当中进行选币。例：LUNA-USDT, 这里与实盘不太一样，需要有'-'
