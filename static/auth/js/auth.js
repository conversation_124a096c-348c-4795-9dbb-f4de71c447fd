/**
 * 认证系统JavaScript文件
 * 提供登录、Google认证等功能的前端交互
 */



// 全局变量
let authConfig = {
    maxLoginAttempts: 5,
    lockoutDuration: 600, // 10分钟
    googleAuthWindow: 600, // 10分钟
    sessionTimeout: 10800 // 3小时
};

// 页面加载完成后初始化
$(document).ready(function() {
    initializeAuth();
});

/**
 * 初始化认证系统
 */
function initializeAuth() {
    // 初始化表单验证
    initializeFormValidation();
    
    // 初始化验证码输入框
    initializeCodeInputs();
    
    // 初始化会话检查
    initializeSessionCheck();
    
    // 初始化CSRF保护
    initializeCSRFProtection();
    
    // 初始化错误处理
    initializeErrorHandling();
}

/**
 * 初始化表单验证
 */
function initializeFormValidation() {
    // 登录表单验证
    $('#loginForm').on('submit', function(e) {
        if (!validateLoginForm()) {
            e.preventDefault();
            return false;
        }
        
        showLoadingState('#loginBtn', '登录中...');
    });
    
    // Google认证设置表单验证
    $('#verifyForm').on('submit', function(e) {
        if (!validateVerificationForm()) {
            e.preventDefault();
            return false;
        }
        
        showLoadingState('#verifyBtn', '验证中...');
    });
    
    // 实时验证
    $('#username').on('blur', function() {
        validateUsername($(this).val());
    });
    
    $('#password').on('blur', function() {
        validatePassword($(this).val());
    });
}

/**
 * 验证登录表单
 */
function validateLoginForm() {
    const username = $('#username').val().trim();
    const password = $('#password').val();
    
    // 清除之前的错误
    clearFormErrors();
    
    let isValid = true;
    
    // 验证用户名
    if (!username) {
        showFieldError('#username', '请输入用户名');
        isValid = false;
    } else if (username.length < 3) {
        showFieldError('#username', '用户名至少3个字符');
        isValid = false;
    }
    
    // 验证密码
    if (!password) {
        showFieldError('#password', '请输入密码');
        isValid = false;
    } else if (password.length < 6) {
        showFieldError('#password', '密码至少6个字符');
        isValid = false;
    }
    
    // 如果显示了Google认证部分，验证验证码
    if ($('#googleAuthSection').hasClass('show')) {
        const googleCode = $('#google_code').val().trim();
        const backupCode = $('#backup_code').val().trim();
        
        if (!googleCode && !backupCode) {
            showError('请输入Google验证码或备用码');
            isValid = false;
        } else if (googleCode && !/^\d{6}$/.test(googleCode)) {
            showFieldError('#google_code', '验证码必须是6位数字');
            isValid = false;
        } else if (backupCode && !/^\d{8}$/.test(backupCode)) {
            showFieldError('#backup_code', '备用码必须是8位数字');
            isValid = false;
        }
    }
    
    return isValid;
}

/**
 * 验证Google认证设置表单
 */
function validateVerificationForm() {
    const code = $('#verification_code').val().trim();
    
    if (!code) {
        showFieldError('#verification_code', '请输入验证码');
        return false;
    }
    
    if (!/^\d{6}$/.test(code)) {
        showFieldError('#verification_code', '验证码必须是6位数字');
        return false;
    }
    
    return true;
}

/**
 * 验证用户名
 */
function validateUsername(username) {
    if (!username) {
        return false;
    }
    
    if (username.length < 3) {
        showFieldError('#username', '用户名至少3个字符');
        return false;
    }
    
    if (!/^[a-zA-Z0-9_]+$/.test(username)) {
        showFieldError('#username', '用户名只能包含字母、数字和下划线');
        return false;
    }
    
    clearFieldError('#username');
    return true;
}

/**
 * 验证密码
 */
function validatePassword(password) {
    if (!password) {
        return false;
    }
    
    if (password.length < 6) {
        showFieldError('#password', '密码至少6个字符');
        return false;
    }
    
    clearFieldError('#password');
    return true;
}

/**
 * 初始化验证码输入框
 */
function initializeCodeInputs() {
    // Google验证码输入框
    $('#google_code').on('input', function() {
        let value = this.value.replace(/\D/g, '');
        if (value.length > 6) {
            value = value.substring(0, 6);
        }
        this.value = value;
        
        // 自动提交（可选）
        if (value.length === 6) {
            // 可以在这里添加自动验证逻辑
        }
    });
    
    // 备用码输入框
    $('#backup_code').on('input', function() {
        let value = this.value.replace(/\D/g, '');
        if (value.length > 8) {
            value = value.substring(0, 8);
        }
        this.value = value;
    });
    
    // 验证码输入框
    $('#verification_code').on('input', function() {
        let value = this.value.replace(/\D/g, '');
        if (value.length > 6) {
            value = value.substring(0, 6);
        }
        this.value = value;
    });
}

/**
 * 初始化会话检查
 */
function initializeSessionCheck() {
    // 如果在登录页面或Google验证页面，不启动会话检查
    if (window.location.pathname === '/auth/login' || window.location.pathname === '/auth/google-verify') {
        return;
    }

    // 定期检查会话状态 - 频繁检查以及时发现Google认证超时（30秒）
    const checkInterval = setInterval(checkSessionStatus, 10000); // 每10秒检查一次

    // 页面可见性变化时检查
    document.addEventListener('visibilitychange', function() {
        if (!document.hidden) {
            checkSessionStatus();
        }
    });

    // 页面即将关闭时清理定时器
    window.addEventListener('beforeunload', function() {
        if (checkInterval) {
            clearInterval(checkInterval);
        }
    });

    // 页面加载后立即检查一次
    setTimeout(checkSessionStatus, 2000);
}

/**
 * 检查会话状态
 */
function checkSessionStatus() {
    // 如果在登录页面或Google验证页面，跳过会话检查
    if (window.location.pathname === '/auth/login' || window.location.pathname === '/auth/google-verify') {
        return;
    }

    // 如果已经在处理Google认证，跳过检查
    if (window.googleAuthInProgress) {
        return;
    }

    $.ajax({
        url: '/auth/api/session-status',
        method: 'GET',
        timeout: 5000,
        success: function(response) {
            if (!response.valid) {
                handleSessionExpired();
            } else if (response.google_auth_required) {
                window.googleAuthInProgress = true;
                showGoogleAuthModal();
            }
        },
        error: function(xhr, status, error) {
            // 如果是403错误，可能需要Google认证
            if (xhr.status === 403) {
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    if (errorResponse.google_auth_required) {
                        window.googleAuthInProgress = true;
                        showGoogleAuthModal();
                        return;
                    }
                } catch (e) {
                    // 解析错误，忽略
                }
            } else if (xhr.status === 401) {
                handleSessionExpired();
            }

            // 其他网络错误暂时忽略，避免频繁提示
        }
    });
}

/**
 * 处理会话过期
 */
function handleSessionExpired() {
    showError('会话已过期，请重新登录', 'warning');
    setTimeout(function() {
        window.location.href = '/auth/login';
    }, 2000);
}

/**
 * 显示需要Google认证
 */
function showGoogleAuthRequired() {
    showGoogleAuthModal();
}

/**
 * 处理Google认证要求（用于AJAX请求）
 */
function handleGoogleAuthRequired(originalRequest) {
    // 保存原始请求信息，用于验证成功后重试
    window.pendingRequest = originalRequest;

    // 显示Google验证模态框
    showGoogleAuthModal();
}

/**
 * 初始化CSRF保护
 */
function initializeCSRFProtection() {
    // 为所有AJAX请求添加CSRF令牌
    $.ajaxSetup({
        beforeSend: function(xhr, settings) {
            if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                const csrfToken = $('meta[name=csrf-token]').attr('content') || 
                                 $('input[name=csrf_token]').val();
                if (csrfToken) {
                    xhr.setRequestHeader("X-CSRFToken", csrfToken);
                }
            }
        }
    });
}

/**
 * 初始化错误处理
 */
function initializeErrorHandling() {
    // 设置全局AJAX默认设置
    $.ajaxSetup({
        error: function(xhr, status, error) {
            if (xhr.status === 401) {
                handleSessionExpired();
            } else if (xhr.status === 403) {
                // 检查是否需要Google认证
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.google_auth_required) {
                        // 保存原始请求信息
                        window.pendingRequest = {
                            url: this.url,
                            method: this.type || 'GET',
                            data: this.data
                        };

                        // 调用Google认证处理
                        showGoogleAuthModal();
                        return; // 阻止默认错误处理
                    } else {
                        showError('访问被拒绝，权限不足');
                    }
                } catch (e) {
                    showError('访问被拒绝，权限不足');
                }
            } else if (xhr.status === 429) {
                showError('请求过于频繁，请稍后再试');
            } else if (xhr.status >= 500) {
                showError('服务器错误，请稍后再试');
            }
        }
    });

    // 备用：使用ajaxError事件作为后备
    $(document).ajaxError(function(event, xhr, settings, error) {
        // 只处理没有自定义error回调的请求
        if (!settings.error || settings.error === $.ajaxSetup().error) {
            // 备用错误处理
        }
    });
}

/**
 * 显示加载状态
 */
function showLoadingState(buttonSelector, text) {
    const btn = $(buttonSelector);
    const spinner = btn.find('.spinner-border');
    const textElement = btn.find('span:not(.spinner-border)').last();
    
    btn.prop('disabled', true);
    if (spinner.length > 0) {
        spinner.show();
    }
    if (textElement.length > 0) {
        textElement.text(text);
    }
}

/**
 * 重置按钮状态
 */
function resetButtonState(buttonSelector, originalText) {
    const btn = $(buttonSelector);
    const spinner = btn.find('.spinner-border');
    const textElement = btn.find('span:not(.spinner-border)').last();
    
    btn.prop('disabled', false);
    if (spinner.length > 0) {
        spinner.hide();
    }
    if (textElement.length > 0) {
        textElement.text(originalText);
    }
}

/**
 * 显示错误消息
 */
function showError(message, type = 'danger') {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // 移除现有的警告
    $('.alert').remove();
    
    // 添加新的警告
    if ($('.auth-form').length > 0) {
        $('.auth-form').prepend(alertHtml);
    } else if ($('.setup-body').length > 0) {
        $('.setup-body').prepend(alertHtml);
    } else {
        $('body').prepend(alertHtml);
    }
    
    // 滚动到顶部
    $('html, body').animate({ scrollTop: 0 }, 300);
}

/**
 * 显示成功消息
 */
function showSuccess(message) {
    showError(message, 'success');
}

/**
 * 显示信息消息
 */
function showInfo(message) {
    showError(message, 'info');
}

/**
 * 显示字段错误
 */
function showFieldError(fieldSelector, message) {
    const field = $(fieldSelector);
    const fieldGroup = field.closest('.form-group, .mb-3');

    // 移除现有的错误状态
    clearFieldError(fieldSelector);

    // 添加错误样式
    field.addClass('is-invalid');

    // 添加错误消息
    const errorHtml = `<div class="invalid-feedback">${message}</div>`;
    field.after(errorHtml);
}

/**
 * 清除字段错误
 */
function clearFieldError(fieldSelector) {
    const field = $(fieldSelector);

    // 移除错误样式
    field.removeClass('is-invalid');

    // 移除错误消息
    field.siblings('.invalid-feedback').remove();
}

/**
 * 清除表单所有错误
 */
function clearFormErrors() {
    $('.is-invalid').removeClass('is-invalid');
    $('.invalid-feedback').remove();
}

/**
 * 关闭Google认证模态框并完全清理状态
 */
function closeGoogleAuthModal() {
    const modal = $('#googleAuthModal');
    if (modal.length === 0) {
        return;
    }

    // 1. 尝试使用Bootstrap标准方法关闭
    modal.modal('hide');

    // 2. 强制清理所有相关状态
    setTimeout(() => {

        // 移除模态框相关的CSS类
        modal.removeClass('show fade');
        modal.css({
            'display': 'none',
            'visibility': 'hidden',
            'opacity': '0'
        });

        // 清理body上的模态框相关类
        $('body').removeClass('modal-open');

        // 移除所有背景遮罩
        $('.modal-backdrop').remove();

        // 获取原始body样式（在模态框显示前的状态）
        const bodyElement = $('body')[0];
        const computedStyle = window.getComputedStyle(bodyElement);

        // 移除所有可能影响布局的内联样式
        const stylesToRemove = ['overflow', 'padding-right', 'margin-right', 'width'];
        stylesToRemove.forEach(prop => {
            bodyElement.style.removeProperty(prop);
        });

        // 同样清理html元素
        const htmlElement = $('html')[0];
        stylesToRemove.forEach(prop => {
            htmlElement.style.removeProperty(prop);
        });

            overflow: $('body').css('overflow'),
            paddingRight: $('body').css('padding-right'),
            classes: $('body').attr('class')
        });

        // 移除模态框元素
        modal.remove();

        // 强制触发页面重新布局
        setTimeout(() => {

            // 检查页面布局状态
            const bodyClasses = $('body').attr('class');
            const bodyStyle = $('body').attr('style');
            const htmlStyle = $('html').attr('style');
            const containerWidth = $('.container-fluid').width();
            const mainContentWidth = $('.main-content').width();

                bodyClasses: bodyClasses,
                bodyStyle: bodyStyle,
                htmlStyle: htmlStyle,
                containerWidth: containerWidth,
                mainContentWidth: mainContentWidth,
                windowWidth: $(window).width()
            });

            // 最终检查并清理任何残留样式
            const finalBodyStyle = $('body').attr('style');
            const finalHtmlStyle = $('html').attr('style');


            // 如果还有任何与模态框相关的样式，完全移除style属性
            if (finalBodyStyle && (
                finalBodyStyle.includes('padding-right') ||
                finalBodyStyle.includes('overflow') ||
                finalBodyStyle.includes('width')
            )) {
                $('body').removeAttr('style');
            }

            if (finalHtmlStyle && (
                finalHtmlStyle.includes('padding-right') ||
                finalHtmlStyle.includes('overflow')
            )) {
                $('html').removeAttr('style');
            }

            // 强制重新计算布局

            // 检查并修复可能的滚动条问题
            const hasVerticalScrollbar = document.body.scrollHeight > window.innerHeight;
            const hasHorizontalScrollbar = document.body.scrollWidth > window.innerWidth;

                hasVertical: hasVerticalScrollbar,
                hasHorizontal: hasHorizontalScrollbar,
                bodyScrollHeight: document.body.scrollHeight,
                bodyScrollWidth: document.body.scrollWidth,
                windowInnerHeight: window.innerHeight,
                windowInnerWidth: window.innerWidth
            });

            // 强制重置所有可能影响布局的样式
            $('html, body').css({
                'overflow-x': 'hidden',
                'overflow-y': 'auto',
                'width': '100%',
                'max-width': '100%'
            });

            // 方法1: 触发resize事件
            $(window).trigger('resize');

            // 方法2: 强制重新渲染
            $('body').hide().show(0);

            // 方法3: 不要移除modern-layout类，而是强制刷新flex布局

            // 强制触发flex布局重新计算
            const bodyElement = $('body')[0];
            if (bodyElement) {
                // 临时改变display属性来强制重新计算
                const originalDisplay = bodyElement.style.display;
                bodyElement.style.display = 'none';
                bodyElement.offsetHeight; // 强制重排
                bodyElement.style.display = originalDisplay || '';

            }

            // 最终检查布局
            setTimeout(() => {
                const finalMainContentWidth = $('.main-content').width();
                const finalWindowWidth = $(window).width();
                    mainContentWidth: finalMainContentWidth,
                    windowWidth: finalWindowWidth,
                    difference: finalWindowWidth - finalMainContentWidth
                });

                // 如果主内容区宽度仍然为0，说明有严重问题
                if (finalMainContentWidth === 0) {

                    // 强制刷新整个页面布局
                    $('body').css('display', 'flex');
                    $('.main-content').css({
                        'flex': '1',
                        'min-width': '0',
                        'width': 'auto'
                    });

                    // 再次触发重排
                    document.body.offsetHeight;

                }
            }, 100);

        }, 50);


        // 额外的布局修复检查
        setTimeout(() => {

            // 检查页面是否有偏移
            const bodyRect = document.body.getBoundingClientRect();
            const htmlRect = document.documentElement.getBoundingClientRect();

                bodyLeft: bodyRect.left,
                bodyRight: bodyRect.right,
                htmlLeft: htmlRect.left,
                htmlRight: htmlRect.right,
                bodyWidth: bodyRect.width,
                windowWidth: window.innerWidth
            });

            // 如果发现偏移，强制修复
            if (bodyRect.left !== 0 || htmlRect.left !== 0) {

                // 移除所有可能的transform和position样式
                $('html, body').css({
                    'transform': 'none',
                    'position': 'static',
                    'left': '0',
                    'right': 'auto',
                    'margin-left': '0',
                    'margin-right': '0'
                });

                // 强制刷新布局
                document.body.offsetHeight; // 触发重排

            } else {
            }
        }, 200);

    }, 100);
}

/**
 * 处理Google认证要求 - 智能选择处理方式
 */
function showGoogleAuthModal() {

    // 暂停日志更新定时器（如果存在该函数）
    if (typeof pauseLogUpdates === 'function') {
        pauseLogUpdates();
    }

    // 检查当前页面类型，决定处理方式
    const currentPath = window.location.pathname;
    const isMainPage = currentPath === '/' || currentPath === '/index' || currentPath.includes('index');
    const isAccountPage = currentPath.includes('accounts');


    // 对于账户管理页面，优先使用备用模态框（避免跳转打断操作）
    if (isAccountPage && typeof createBackupGoogleAuthModal === 'function') {
        createBackupGoogleAuthModal();
        return;
    }

    // 对于账户页面但没有备用模态框函数的情况，直接跳转
    if (isAccountPage) {
    }

    // 直接跳转到验证页面，不显示确认对话框

    // 构建跳转URL，包含返回地址和请求数据
    const currentUrl = window.location.href;
    const returnUrl = encodeURIComponent(currentUrl);

    // 如果有待处理的请求，保存请求信息
    let requestData = '';
    if (window.pendingRequest) {
        requestData = encodeURIComponent(JSON.stringify({
            url: window.pendingRequest.url,
            method: window.pendingRequest.type || 'GET',
            data: window.pendingRequest.data
        }));
    }

    const googleVerifyUrl = `/auth/google-verify?return_url=${returnUrl}&request_data=${requestData}`;


    // 跳转到独立的Google认证页面
    window.location.href = googleVerifyUrl;
}

// 废弃的函数已移除，使用独立认证页面

/**
 * 重试待处理的请求
 */
function retryPendingRequest() {
    if (window.pendingRequest) {
        const request = window.pendingRequest;

        // 清除待处理请求
        window.pendingRequest = null;

        // 重新发送请求
        $.ajax({
            url: request.url,
            method: request.type || 'GET',
            data: request.data,
            success: request.success,
            error: request.error
        });
    } else {
    }
}
// 废弃函数已清理
// 初始化全局状态变量
window.googleAuthInProgress = false;

// 确保关键函数在全局作用域中可用
window.initializeAuth = initializeAuth;
window.showGoogleAuthModal = showGoogleAuthModal;
window.checkSessionStatus = checkSessionStatus;
window.showGoogleAuthRequired = showGoogleAuthRequired;
window.validateUsername = validateUsername;
window.validatePassword = validatePassword;

// 添加重置Google认证状态的函数
window.resetGoogleAuthStatus = function() {
    window.googleAuthInProgress = false;
};


// 自动初始化认证系统（如果页面没有手动调用）
$(document).ready(function() {

    // 延迟一点时间，让页面的初始化代码先执行
    setTimeout(function() {
        if (!window.authInitialized) {
            initializeAuth();
            window.authInitialized = true;
        } else {
        }
    }, 500);
});
