/* 认证系统样式文件 */

/* 全局样式 */
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
}

/* 卡片样式 */
.auth-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    max-width: 400px;
    width: 100%;
}

.auth-card-wide {
    max-width: 600px;
}

/* 头部样式 */
.auth-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    text-align: center;
}

.auth-header h2 {
    margin: 0;
    font-weight: 600;
    font-size: 1.5rem;
}

.auth-header .subtitle {
    margin-top: 8px;
    opacity: 0.9;
    font-size: 0.9rem;
}

/* 表单样式 */
.auth-form {
    padding: 30px;
}

.form-floating > .form-control {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 12px 16px;
    height: auto;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-floating > .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-floating > label {
    padding: 12px 16px;
    color: #6c757d;
}

/* 按钮样式 */
.btn-auth {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 12px;
    padding: 12px 24px;
    font-size: 1rem;
    font-weight: 600;
    color: white;
    width: 100%;
    transition: all 0.3s ease;
}

.btn-auth:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    color: white;
}

.btn-auth:disabled {
    opacity: 0.6;
    transform: none;
    box-shadow: none;
}

/* 验证码输入框 */
.verification-code-input {
    text-align: center;
    font-size: 1.2rem;
    letter-spacing: 0.5rem;
    font-weight: 600;
}

/* 二维码容器 */
.qr-code-container {
    text-align: center;
    margin: 30px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 15px;
    border: 2px dashed #dee2e6;
}

.qr-code-image {
    max-width: 200px;
    height: auto;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* 密钥显示 */
.secret-key-container {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin: 20px 0;
    border-left: 4px solid #667eea;
}

.secret-key {
    font-family: 'Courier New', monospace;
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
    word-break: break-all;
    background: white;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #dee2e6;
}

/* 备用码样式 */
.backup-codes-container {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
}

.backup-codes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    margin-top: 15px;
}

.backup-code {
    background: white;
    padding: 8px 12px;
    border-radius: 5px;
    font-family: 'Courier New', monospace;
    font-weight: 600;
    text-align: center;
    border: 1px solid #ffeaa7;
}

/* 步骤指示器 */
.step-indicator {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
}

.step {
    display: flex;
    align-items: center;
    margin: 0 10px;
}

.step-number {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
}

.step.active .step-number {
    background: #667eea;
    color: white;
}

.step.completed .step-number {
    background: #28a745;
    color: white;
}

.step-line {
    width: 50px;
    height: 2px;
    background: #e9ecef;
    margin: 0 10px;
}

.step.completed + .step .step-line {
    background: #28a745;
}

/* 警告和提示 */
.alert {
    border-radius: 12px;
    border: none;
    margin-bottom: 20px;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-success {
    background-color: #d1edff;
    color: #0c5460;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

/* 加载动画 */
.loading-spinner {
    display: none;
    margin-right: 8px;
}

/* 链接样式 */
.auth-link {
    color: #667eea;
    text-decoration: none;
    font-size: 0.9rem;
}

.auth-link:hover {
    color: #764ba2;
    text-decoration: underline;
}

/* 状态指示器 */
.status-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    padding: 10px 15px;
    font-size: 0.8rem;
    color: #6c757d;
    backdrop-filter: blur(10px);
    z-index: 1000;
}

/* 复选框样式 */
.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

/* 指令列表 */
.instruction-list {
    padding-left: 0;
    list-style: none;
    counter-reset: step-counter;
}

.instruction-list li {
    padding: 10px 0;
    border-bottom: 1px solid #e9ecef;
    position: relative;
    padding-left: 30px;
}

.instruction-list li:before {
    content: counter(step-counter);
    counter-increment: step-counter;
    position: absolute;
    left: 0;
    top: 10px;
    background: #667eea;
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 576px) {
    .auth-container {
        padding: 15px;
    }
    
    .auth-header, .auth-form {
        padding: 20px;
    }
    
    .step-indicator {
        flex-direction: column;
        align-items: center;
    }
    
    .step {
        margin: 5px 0;
    }
    
    .step-line {
        display: none;
    }
    
    .status-indicator {
        position: static;
        margin: 20px;
        text-align: center;
    }
    
    .backup-codes {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-card {
    animation: fadeIn 0.5s ease-out;
}

/* 焦点样式 */
.form-control:focus,
.btn:focus {
    outline: none;
}

/* 打印样式 */
@media print {
    .auth-container {
        background: white;
    }
    
    .auth-card {
        box-shadow: none;
        border: 1px solid #ccc;
    }
    
    .auth-header {
        background: #667eea !important;
        -webkit-print-color-adjust: exact;
    }
}

/* Google验证模态框样式 */
.auth-method-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.auth-method-tab {
    flex: 1;
    padding: 0.75rem 1rem;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 0.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.auth-method-tab:hover {
    background: #e9ecef;
    border-color: #007bff;
}

.auth-method-tab.active {
    background: #007bff;
    border-color: #007bff;
    color: white;
}

.auth-method-content {
    display: none;
}

.auth-method-content.active {
    display: block;
}

.backup-code-input {
    font-family: 'Courier New', monospace;
    font-size: 1.2rem;
    text-align: center;
    letter-spacing: 0.2rem;
}

.verification-code-input:focus,
.backup-code-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 模态框动画 */
.modal.fade .modal-dialog {
    transform: translateY(-50px);
    transition: transform 0.3s ease-out;
}

.modal.show .modal-dialog {
    transform: translateY(0);
}

/* 模态框响应式设计 */
@media (max-width: 768px) {
    .auth-method-tabs {
        flex-direction: column;
        gap: 0.5rem;
    }

    .auth-method-tab {
        text-align: center;
    }

    .verification-code-input,
    .backup-code-input {
        font-size: 1rem;
        letter-spacing: 0.1rem;
    }
}
