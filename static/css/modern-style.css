/* 现代化量化交易系统界面样式 */

/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 字体和基础样式 */
body {
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    color: #2c3e50;
    line-height: 1.6;
    overflow-x: hidden;
}

.modern-layout {
    display: flex;
    min-height: 100vh;
}

/* 侧边栏样式 */
.sidebar {
    width: 260px;
    background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: fixed;
    height: 100vh;
    left: 0;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar.collapsed {
    width: 70px;
}

.sidebar-header {
    padding: 1.5rem 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-brand {
    display: flex;
    align-items: center;
    font-size: 1.25rem;
    font-weight: 600;
    color: white;
    text-decoration: none;
}

.sidebar-brand i {
    font-size: 1.5rem;
    margin-right: 0.75rem;
    color: #ffd700;
}

.brand-text {
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .brand-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

/* 导航样式 */
.sidebar-nav {
    padding: 1rem 0;
    flex: 1;
}

.nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin: 0.25rem 0;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    border-radius: 0 25px 25px 0;
    margin-right: 1rem;
    position: relative;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(5px);
}

.nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.nav-link i {
    font-size: 1.1rem;
    margin-right: 0.75rem;
    width: 20px;
    text-align: center;
}

.nav-text {
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .nav-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
}





/* 强制重置按钮样式，确保可见性 */
#sidebar-toggle {
    all: unset !important;
    box-sizing: border-box !important;
}

/* 禁用Bootstrap响应式圆角计算 */
:root {
    --bs-border-radius: 12px !important;
    --bs-border-radius-sm: 12px !important;
    --bs-border-radius-lg: 12px !important;
    --bs-border-radius-xl: 12px !important;
    --bs-border-radius-2xl: 12px !important;
}

/* 全局禁用Bootstrap的calc()圆角计算 */
* {
    --bs-border-radius: 12px !important;
}

/* 强制覆盖Bootstrap的border-radius计算 - 使用最高特异性 */
html body .main-content .top-bar #sidebar-toggle,
html body .main-content .top-bar button#sidebar-toggle,
html body .main-content .top-bar .sidebar-toggle-btn,
html body .top-bar #sidebar-toggle,
html body .top-bar button#sidebar-toggle,
html body .top-bar .sidebar-toggle-btn,
html body #sidebar-toggle,
html body button#sidebar-toggle,
html body .sidebar-toggle-btn,
#sidebar-toggle,
button#sidebar-toggle,
.sidebar-toggle-btn {
    border-radius: 12px !important;
    -webkit-border-radius: 12px !important;
    -moz-border-radius: 12px !important;
    -ms-border-radius: 12px !important;
    -o-border-radius: 12px !important;

    /* 禁用Bootstrap的响应式计算 */
    border-top-left-radius: 12px !important;
    border-top-right-radius: 12px !important;
    border-bottom-left-radius: 12px !important;
    border-bottom-right-radius: 12px !important;

    /* 强制覆盖任何calc()值 */
    border-start-start-radius: 12px !important;
    border-start-end-radius: 12px !important;
    border-end-start-radius: 12px !important;
    border-end-end-radius: 12px !important;
}

/* 侧边栏折叠按钮样式 - 现代化设计 */
.main-content .top-bar #sidebar-toggle.sidebar-toggle-btn,
.top-bar-left #sidebar-toggle.sidebar-toggle-btn,
.top-bar #sidebar-toggle.sidebar-toggle-btn,
button#sidebar-toggle.sidebar-toggle-btn,
#sidebar-toggle.sidebar-toggle-btn {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
    border: 2px solid rgba(0, 123, 255, 0.6) !important;
    color: #007bff !important;
    font-size: 1.2rem !important;
    cursor: pointer !important;
    padding: 0.5rem !important;
    border-radius: 12px !important;
    -webkit-border-radius: 12px !important;
    -moz-border-radius: 12px !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    margin-right: 0.75rem !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 44px !important;
    height: 44px !important;
    position: relative !important;
    z-index: 1000 !important;
    box-shadow:
        0 4px 12px rgba(0, 123, 255, 0.25),
        0 2px 4px rgba(0, 0, 0, 0.15) !important;
    opacity: 1 !important;
    visibility: visible !important;
    backdrop-filter: blur(10px) !important;
}

.main-content .top-bar #sidebar-toggle.sidebar-toggle-btn:hover,
.top-bar-left #sidebar-toggle.sidebar-toggle-btn:hover,
.top-bar #sidebar-toggle.sidebar-toggle-btn:hover,
button#sidebar-toggle.sidebar-toggle-btn:hover,
#sidebar-toggle.sidebar-toggle-btn:hover {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
    color: #ffffff !important;
    border-color: rgba(0, 123, 255, 0.8) !important;
    transform: translateY(-2px) scale(1.05) !important;
    box-shadow:
        0 8px 25px rgba(0, 123, 255, 0.4),
        0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

.main-content .top-bar #sidebar-toggle.sidebar-toggle-btn:focus,
.top-bar-left #sidebar-toggle.sidebar-toggle-btn:focus,
.top-bar #sidebar-toggle.sidebar-toggle-btn:focus,
button#sidebar-toggle.sidebar-toggle-btn:focus,
#sidebar-toggle.sidebar-toggle-btn:focus {
    outline: 2px solid #007bff !important;
    outline-offset: 2px !important;
}

/* 确保图标正确显示 */
.sidebar-toggle-btn i {
    font-size: 1.2rem !important;
    line-height: 1 !important;
    display: inline-block !important;
    font-style: normal !important;
    font-variant: normal !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
    color: #007bff !important;
    text-align: center !important;
    vertical-align: middle !important;
}

/* 悬停状态下的主图标颜色 */
.sidebar-toggle-btn:hover i {
    color: #ffffff !important;
}

/* Bootstrap Icons 备用样式 - 如果字体未加载则显示Unicode符号 */
.sidebar-toggle-btn .bi-list::before {
    content: "☰" !important;
    font-family: Arial, sans-serif !important;
    font-size: 1.2rem !important;
    font-weight: bold !important;
    color: #007bff !important;
    text-align: center !important;
    vertical-align: middle !important;
}

/* 如果图标字体未加载，显示备用文字 */
.sidebar-toggle-btn .bi-list:not([class*="bi-"]):after {
    content: "☰" !important;
    font-size: 1.2rem !important;
    color: #007bff !important;
    font-weight: bold !important;
}

/* 备用图标样式 */
.sidebar-toggle-btn .fallback-icon {
    font-size: 1.2rem !important;
    line-height: 1 !important;
    font-weight: bold !important;
    color: #007bff !important;
    display: inline-block !important;
    text-align: center !important;
    vertical-align: middle !important;
}

.sidebar-toggle-btn .text-fallback {
    font-size: 0.8rem !important;
    line-height: 1 !important;
    font-weight: bold !important;
    color: #007bff !important;
    display: inline-block !important;
    text-align: center !important;
    vertical-align: middle !important;
}

/* 强制显示备用图标的样式类 */
.sidebar-toggle-btn.use-fallback .bi-list {
    display: none !important;
}

.sidebar-toggle-btn.use-fallback .fallback-icon {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    color: #007bff !important;
    font-size: 1.3rem !important;
    font-weight: 900 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
    z-index: 10 !important;
    position: relative !important;
}

/* 悬停状态下的图标颜色 */
.sidebar-toggle-btn.use-fallback:hover .fallback-icon {
    color: #ffffff !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
}

/* Flash消息倒计时指示器样式 */
.flash-countdown {
    margin-top: 8px !important;
    padding-top: 8px !important;
    border-top: 1px solid rgba(0, 0, 0, 0.1) !important;
    font-size: 0.8rem !important;
    opacity: 0.8 !important;
}

.flash-countdown .countdown-text {
    font-weight: 500 !important;
}

.flash-countdown i {
    opacity: 0.7 !important;
}

/* 成功消息的特殊样式 */
.alert-success .flash-countdown {
    border-top-color: rgba(25, 135, 84, 0.2) !important;
}

.alert-success .flash-countdown .text-muted {
    color: rgba(25, 135, 84, 0.7) !important;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    margin-left: 260px;
    transition: margin-left 0.3s ease;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.main-content.sidebar-collapsed {
    margin-left: 70px;
}

/* 顶部栏 - 现代化设计 */
.main-content .top-bar,
div.top-bar,
.top-bar {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    backdrop-filter: blur(10px) !important;
    border-bottom: 1px solid rgba(0, 123, 255, 0.1) !important;
    padding: 1.25rem 2rem !important;
    box-shadow:
        0 4px 20px rgba(0, 0, 0, 0.08),
        0 1px 3px rgba(0, 0, 0, 0.1) !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    position: sticky !important;
    top: 0 !important;
    z-index: 100 !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* 顶部栏滚动时的效果 */
.main-content .top-bar.scrolled,
div.top-bar.scrolled,
.top-bar.scrolled {
    background: linear-gradient(135deg, rgba(248, 249, 250, 0.95) 0%, rgba(233, 236, 239, 0.95) 100%) !important;
    backdrop-filter: blur(20px) !important;
    padding: 1rem 2rem !important;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.12),
        0 2px 8px rgba(0, 0, 0, 0.08) !important;
    border-bottom: 1px solid rgba(0, 123, 255, 0.2) !important;
}

/* 顶部栏悬停效果 */
.main-content .top-bar:hover,
div.top-bar:hover,
.top-bar:hover {
    box-shadow:
        0 6px 25px rgba(0, 0, 0, 0.1),
        0 2px 6px rgba(0, 0, 0, 0.08) !important;
}

.top-bar-left {
    display: flex;
    align-items: center;
}

.top-bar-left .page-title,
h1.page-title,
.page-title {
    font-size: 1.5rem !important;
    font-weight: 700 !important;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #007bff 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    margin: 0 !important;
    margin-left: 1rem !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
    letter-spacing: -0.025em !important;
}

.top-bar-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* 状态指示器 */
.status-indicator {
    display: flex;
    align-items: center;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.875rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-success {
    background: rgba(34, 197, 94, 0.1);
    color: #22c55e;
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.status-danger {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.status-warning {
    background: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
    border: 1px solid rgba(245, 158, 11, 0.2);
}

/* 当前时间 */
.current-time {
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 0.9rem;
    color: #64748b;
    background: #f8fafc;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

/* 内容区域 */
.content-area {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

/* 现代化卡片样式 */
.modern-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: none;
    transition: all 0.3s ease;
    overflow: hidden;
}

.modern-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.modern-card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border: none;
    font-weight: 600;
}

.modern-card-body {
    padding: 2rem;
}

/* 统计卡片 */
.stat-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: none;
    transition: all 0.3s ease;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
    color: white;
}

.stat-icon.blue {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.stat-icon.green {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.stat-icon.purple {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.stat-icon.orange {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6b7280;
    font-size: 0.875rem;
    font-weight: 500;
}

/* 按钮样式 */
.modern-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    border: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.modern-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.modern-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.modern-btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.modern-btn-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.modern-btn-info {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
}

.modern-btn-info:disabled {
    background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
    color: #d1d5db;
    cursor: not-allowed;
    transform: none;
}

.modern-btn-info:disabled:hover {
    transform: none;
    box-shadow: none;
}

.modern-btn-outline {
    background: transparent;
    border: 2px solid #667eea;
    color: #667eea;
}

.modern-btn-outline:disabled {
    background: transparent;
    border: 2px solid #9ca3af;
    color: #9ca3af;
    cursor: not-allowed;
    transform: none;
}

.modern-btn-outline:disabled:hover {
    background: transparent;
    border: 2px solid #9ca3af;
    color: #9ca3af;
    transform: none;
    box-shadow: none;
}

.modern-btn-outline:hover {
    background: #667eea;
    color: white;
}

/* 脚本查看器样式 */
.modern-card .btn-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.8);
    opacity: 1;
}

.modern-card .btn-close:hover {
    color: white;
    opacity: 1;
}

#schedule-script-viewer pre {
    background: #2d3748;
    color: #e2e8f0;
    border: 1px solid #4a5568;
}

#schedule-script-viewer pre code {
    background: transparent;
    color: inherit;
}

/* 调度脚本模态框样式 */
#scheduleScriptModal .modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

#scheduleScriptModal .modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
    border-bottom: none;
    padding: 1.5rem;
}

#scheduleScriptModal .modal-title {
    font-weight: 600;
    font-size: 1.25rem;
}

#scheduleScriptModal .btn-close {
    filter: invert(1);
    opacity: 0.8;
}

#scheduleScriptModal .btn-close:hover {
    opacity: 1;
}

#scheduleScriptModal .modal-body {
    padding: 2rem;
    background: #f8f9fa;
}

#scheduleScriptModal .modal-footer {
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 12px 12px;
    padding: 1.5rem 2rem;
}

#scheduleScriptModal textarea,
#scheduleScriptModal pre {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    font-size: 14px;
    line-height: 1.5;
}

#scheduleScriptModal textarea:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* 响应式设计 */
@media (max-width: 1200px) {
    #scheduleScriptModal .modal-dialog {
        max-width: 95%;
        margin: 1rem auto;
    }

    #scheduleScriptModal textarea {
        rows: 20;
    }

    #scheduleScriptModal #schedule-script-viewer pre {
        height: 500px;
    }
}

@media (max-width: 768px) {
    #scheduleScriptModal .modal-body {
        padding: 1rem;
    }

    #scheduleScriptModal .modal-footer {
        padding: 1rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    #scheduleScriptModal .modal-footer .btn {
        width: 100%;
    }

    #scheduleScriptModal textarea {
        font-size: 12px;
    }
}

/* 账户项样式 */
.account-item {
    background: #f8fafc;
    border: 1px solid #e2e8f0 !important;
    transition: all 0.3s ease;
}

.account-item:hover {
    background: white;
    border-color: #667eea !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

/* 表单样式增强 */
.form-control:focus,
.form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    color: #374151;
    font-weight: 500;
}

/* 模态框样式 */
.modal-content {
    border: none;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 16px 16px 0 0;
    border: none;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* 下拉菜单样式 */
.dropdown-menu {
    border: none;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.dropdown-item i {
    width: 16px;
    margin-right: 0.5rem;
}

/* 进度条样式 */
.progress {
    background: #e5e7eb;
    border-radius: 50px;
    overflow: hidden;
}

.progress-bar {
    border-radius: 50px;
    transition: width 0.6s ease;
}

/* 徽章样式 */
.badge {
    font-weight: 500;
    padding: 0.5em 0.75em;
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modern-card {
    animation: fadeInUp 0.6s ease-out;
}

.stat-card {
    animation: fadeInUp 0.6s ease-out;
}

/* 日志查看器样式 */
.log-viewer {
    background: #1a1a1a;
    border-radius: 8px;
    max-height: 400px;
    overflow-y: auto;
}

.log-text {
    color: #e5e7eb;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
    margin: 0;
    padding: 1rem;
    white-space: pre-wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .top-bar {
        padding: 1rem 1.5rem;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        box-shadow:
            0 2px 15px rgba(0, 0, 0, 0.06),
            0 1px 3px rgba(0, 0, 0, 0.08);
    }

    .page-title {
        font-size: 1.25rem;
        margin-left: 0.5rem;
    }

    .content-area {
        padding: 1rem;
    }

    .current-time {
        display: none;
    }

    .account-item {
        margin-bottom: 1rem;
    }

    .account-item .row .col-6 {
        flex: 0 0 100%;
        max-width: 100%;
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 576px) {
    .top-bar-right {
        gap: 0.5rem;
    }

    .status-badge {
        padding: 0.375rem 0.75rem;
        font-size: 0.75rem;
    }

    .stat-card {
        padding: 1rem;
    }

    .stat-value {
        font-size: 1.5rem;
    }

    .modern-btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    .modal-dialog {
        margin: 1rem;
    }

    .log-viewer {
        font-size: 0.75rem;
        max-height: 300px;
    }
}

/* 日志查看器样式 */
.log-viewer {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.4;
    background: #1e1e1e;
    color: #d4d4d4;
    border-radius: 0.375rem;
    padding: 1rem;
    overflow-y: auto;
    max-height: 400px;
}

.log-entry {
    padding: 0.25rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.log-entry:last-child {
    border-bottom: none;
}

.log-message {
    margin-left: 0.5rem;
    word-wrap: break-word;
}

/* 日志级别颜色 */
.log-viewer .text-danger {
    color: #f85149 !important;
}

.log-viewer .text-warning {
    color: #f0ad4e !important;
}

.log-viewer .text-info {
    color: #5bc0de !important;
}

.log-viewer .text-muted {
    color: #6c757d !important;
}

/* 调试模式状态样式 */
.status-badge.status-warning {
    background: linear-gradient(135deg, #ffc107, #ff8f00);
    color: white;
}

.status-badge.status-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

/* 用户信息区域样式 */
.user-info-section {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-right: 1rem;
}



.user-info-section .dropdown-toggle {
    border: 1px solid rgba(0, 123, 255, 0.2);
    color: #495057;
    padding: 0.625rem 1rem;
    border-radius: 12px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 4px 12px rgba(0, 123, 255, 0.1),
        0 2px 4px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(10px);
}

.user-info-section .dropdown-toggle:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-color: rgba(0, 123, 255, 0.4);
    color: #212529;
    box-shadow:
        0 6px 20px rgba(0, 123, 255, 0.15),
        0 4px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px) scale(1.02);
}

.user-info-section .dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    border-color: #667eea;
}

.user-info-section .dropdown-toggle i {
    font-size: 1.1rem;
}

.user-info-section .dropdown-menu {
    border: 1px solid #e9ecef;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-radius: 12px;
    padding: 0.75rem 0;
    min-width: 220px;
    margin-top: 0.5rem;
}

.user-info-section .dropdown-item {
    padding: 0.75rem 1.25rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    border: none;
}

.user-info-section .dropdown-item:hover {
    background: #f8f9fa;
    color: #495057;
}

.user-info-section .dropdown-item.text-danger:hover {
    background: #f8d7da;
    color: #721c24;
}

.user-info-section .dropdown-item-text {
    padding: 0.75rem 1.25rem;
    font-size: 0.875rem;
    color: #6c757d;
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 0.5rem;
}

.user-info-section .dropdown-divider {
    margin: 0.5rem 0;
    border-color: #e9ecef;
}

.user-info-section .dropdown-item-text {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
    color: #6c757d;
}

/* 响应式用户信息 */
@media (max-width: 768px) {
    .user-info-section {
        margin-right: 0.5rem;
    }

    .user-info-section .dropdown-toggle {
        padding: 0.375rem 0.5rem;
        font-size: 0.8rem;
    }

    .user-info-section .dropdown-toggle .d-none.d-md-inline {
        display: none !important;
    }
}

@media (max-width: 576px) {
    .user-info-section {
        gap: 0.5rem;
    }

    .top-bar-right {
        flex-wrap: wrap;
        gap: 0.5rem;
    }
}
